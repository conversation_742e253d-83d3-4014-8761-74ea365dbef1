{"version": 3, "sources": ["../src/parser/geo/geobuf.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAC;AACjC,OAAO,GAAG,MAAM,KAAK,CAAC;AACtB,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAGhD,OAAO,EAAE,uBAAuB,EAAE,aAAa,EAAE,MAAM,WAAW,CAAC;AAInE,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAMlC,MAAM,CAAC,MAAM,YAAY,GAAW,CAAC,IAAiB,EAAE,UAA0B,EAAE,EAAE,QAAkB,EAAE,EAAE;IAC1G,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC;IAClC,MAAM,YAAY,GAAG,cAAc,CAAC,uBAAuB,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;IAC9F,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7C,OAAO,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AACxD,CAAC,CAAC", "file": "geobuf.js", "sourcesContent": ["import * as geobuf from 'geobuf';\nimport Pbf from 'pbf';\nimport { DATAVIEW_TYPE } from '../../constants';\nimport type { DataView } from '../../data-view';\nimport { mergeDeepImmer } from '../../utils/js';\nimport type { Parser } from '..';\nimport type { IGeoJSONOptions } from './geojson';\nimport { DEFAULT_GEOJSON_OPTIONS, geoJSONParser } from './geojson';\n\nexport type IGeoBufOptions = IGeoJSONOptions;\n\nconst DEFAULT_GEOBUF_OPTIONS = {};\n/**\n * geobuf pbf 解码为 geojson\n * @param data\n * @returns\n */\nexport const geoBufParser: Parser = (data: ArrayBuffer, options: IGeoBufOptions = {}, dataView: DataView) => {\n  dataView.type = DATAVIEW_TYPE.GEO;\n  const mergeOptions = mergeDeepImmer(DEFAULT_GEOJSON_OPTIONS, DEFAULT_GEOBUF_OPTIONS, options);\n  const geoData = geobuf.decode(new Pbf(data)); // 对GeoBuf解码\n  return geoJSONParser(geoData, mergeOptions, dataView);\n};\n"]}