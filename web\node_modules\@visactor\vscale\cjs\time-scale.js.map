{"version": 3, "sources": ["../src/time-scale.ts"], "names": [], "mappings": ";;;AAAA,6CAAiG;AACjG,yDAAqD;AAErD,iCAAmC;AACnC,uCAA6D;AAC7D,yCAAqC;AAErC,MAAa,SAAU,SAAQ,kCAAe;IAK5C,YAAY,QAAiB,KAAK;QAChC,KAAK,EAAE,CAAC;QALD,SAAI,GAAwB,gBAAS,CAAC,IAAI,CAAC;QAMlD,IAAI,CAAC,OAAO,GAAG,KAAK;YAClB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,CAAS;QACd,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAID,MAAM,CAAC,CAAkB,EAAE,OAAiB;QAC1C,IAAI,CAAC,CAAC,EAAE;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAM,CAAC,CAAC;SACjC;QAED,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,mBAAY,CAAqB,CAAC;QAEnE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,QAAuC;QAC3C,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE3B,MAAM,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;QAC7B,IAAI,OAAO,EAAE;YACX,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC/B;QAED,IAAI,OAAO,GAAG,QAA+B,CAAC;QAE9C,IAAI,IAAA,iBAAQ,EAAC,QAAQ,CAAC,IAAI,IAAA,cAAK,EAAC,QAAQ,CAAC,EAAE;YACzC,OAAO,GAAG,IAAA,sBAAe,EAAC,KAAK,EAAE,IAAI,EAAE,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,QAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAClG;QAED,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAa,CAAC,CAAC;QAEpC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC;QACjB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,MAAM,QAAQ,GAAG,GAAG,CAAC;QAErB,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,QAAQ,EAAE;YACnC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACxC,CAAC,EAAE,CAAC;SACL;QACD,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;IACrD,CAAC;IAED,UAAU,CAAC,KAAc,EAAE,SAAkB;QAC3C,OAAO,SAAS,IAAI,IAAI;YACtB,CAAC,CAAC,IAAA,yBAAgB,EAAC,IAAA,2BAAkB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;YACjF,CAAC,CAAC,IAAA,yBAAgB,EAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK;QACH,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;aAC9B,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;aAC3B,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;aACxB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;aACtB,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;aAC/B,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAED,IAAI,CAAC,QAAuC;QAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,OAAO,GAAG,QAA+B,CAAC;QAE9C,IAAI,IAAA,iBAAQ,EAAC,QAAQ,CAAC,IAAI,IAAA,cAAK,EAAC,QAAQ,CAAC,EAAE;YACzC,OAAO,GAAG,IAAA,sBAAe,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,QAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5G;QAED,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,IAAA,YAAI,EAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AA9FD,8BA8FC", "file": "time-scale.js", "sourcesContent": ["import { isNil, getTimeFormatter, getFormatFromValue, toDate, isNumber } from '@visactor/vutils';\nimport { ContinuousScale } from './continuous-scale';\nimport type { ContinuousScaleType, DateLikeType, FloorCeilType } from './interface';\nimport { ScaleEnum } from './type';\nimport { getTickInterval, toDateNumber } from './utils/time';\nimport { nice } from './utils/utils';\n\nexport class TimeScale extends ContinuousScale {\n  readonly type: ContinuousScaleType = ScaleEnum.Time;\n\n  _isUtc?: boolean;\n\n  constructor(isUtc: boolean = false) {\n    super();\n    this._domain = isUtc\n      ? [Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]\n      : [+new Date(2000, 0, 1), +new Date(2000, 0, 2)];\n    this._isUtc = isUtc;\n  }\n\n  invert(y: number) {\n    return new Date(super.invert(y));\n  }\n\n  domain(): Date[];\n  domain(_: DateLikeType[], slience?: boolean): this;\n  domain(_?: DateLikeType[], slience?: boolean): Date[] | this {\n    if (!_) {\n      return this._domain.map(toDate);\n    }\n\n    const nextDomain = Array.from(_, toDateNumber) as [number, number];\n\n    this._domain = nextDomain;\n    return this.rescale(slience);\n  }\n\n  ticks(interval?: number | FloorCeilType<Date>) {\n    const d = this.domain();\n    let start = d[0];\n    let stop = d[d.length - 1];\n\n    const reverse = stop < start;\n    if (reverse) {\n      [start, stop] = [stop, start];\n    }\n\n    let options = interval as FloorCeilType<Date>;\n\n    if (isNumber(interval) || isNil(interval)) {\n      options = getTickInterval(start, stop, isNil(interval) ? 10 : (interval as number), this._isUtc);\n    }\n\n    start = options.ceil(start as Date);\n\n    const tickValues = [];\n    let cur = +start;\n    let i = 0;\n    const maxCount = 100;\n\n    while (cur <= +stop && i < maxCount) {\n      tickValues.push(new Date(cur));\n      cur = +options.offset(new Date(cur), 1);\n      i++;\n    }\n    return reverse ? tickValues.reverse() : tickValues;\n  }\n\n  tickFormat(count?: number, specifier?: string) {\n    return specifier == null\n      ? getTimeFormatter(getFormatFromValue(this._domain[0], this._isUtc), this._isUtc)\n      : getTimeFormatter(specifier, this._isUtc);\n  }\n\n  clone(): TimeScale {\n    return new TimeScale(this._isUtc)\n      .domain(this.domain(), true)\n      .range(this._range, true)\n      .unknown(this._unknown)\n      .clamp(this.clamp(), null, true)\n      .interpolate(this._interpolate);\n  }\n\n  nice(interval?: number | FloorCeilType<Date>) {\n    const d = this.domain();\n    let options = interval as FloorCeilType<Date>;\n\n    if (isNumber(interval) || isNil(interval)) {\n      options = getTickInterval(d[0], d[d.length - 1], isNil(interval) ? 10 : (interval as number), this._isUtc);\n    }\n\n    if (options) {\n      this.domain(nice(d, options));\n    }\n\n    return this;\n  }\n\n  utc() {\n    return this._isUtc;\n  }\n}\n"]}