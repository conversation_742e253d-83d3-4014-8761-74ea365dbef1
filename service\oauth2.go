// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.

package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
	"veloera/common"
)

// OAuth2Provider 定义OAuth2提供商接口
type OAuth2Provider interface {
	GetName() string
	GetAuthURL(state, redirectURI string) string
	ExchangeToken(code, redirectURI string) (*OAuth2Token, error)
	GetUserInfo(token *OAuth2Token) (*OAuth2User, error)
	IsEnabled() bool
	GetClientID() string
}

// OAuth2Token OAuth2令牌结构
type OAuth2Token struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresIn    int    `json:"expires_in,omitempty"`
	Scope        string `json:"scope,omitempty"`
}

// OAuth2User OAuth2用户信息结构
type OAuth2User struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	DisplayName string `json:"display_name"`
	AvatarURL   string `json:"avatar_url,omitempty"`
}

// OAuth2Config OAuth2配置结构
type OAuth2Config struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	AuthURL      string `json:"auth_url"`
	TokenURL     string `json:"token_url"`
	UserInfoURL  string `json:"user_info_url"`
	Scopes       string `json:"scopes"`
	Enabled      bool   `json:"enabled"`
}

// BaseOAuth2Provider 基础OAuth2提供商实现
type BaseOAuth2Provider struct {
	Name   string
	Config OAuth2Config
}

func (p *BaseOAuth2Provider) GetName() string {
	return p.Name
}

func (p *BaseOAuth2Provider) GetClientID() string {
	return p.Config.ClientID
}

func (p *BaseOAuth2Provider) IsEnabled() bool {
	return p.Config.Enabled && p.Config.ClientID != "" && p.Config.ClientSecret != ""
}

func (p *BaseOAuth2Provider) GetAuthURL(state, redirectURI string) string {
	params := url.Values{}
	params.Set("response_type", "code")
	params.Set("client_id", p.Config.ClientID)
	params.Set("redirect_uri", redirectURI)
	params.Set("state", state)
	if p.Config.Scopes != "" {
		params.Set("scope", p.Config.Scopes)
	}
	
	return p.Config.AuthURL + "?" + params.Encode()
}

func (p *BaseOAuth2Provider) ExchangeToken(code, redirectURI string) (*OAuth2Token, error) {
	if code == "" {
		return nil, errors.New("无效的授权码")
	}

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", redirectURI)
	data.Set("client_id", p.Config.ClientID)
	data.Set("client_secret", p.Config.ClientSecret)

	req, err := http.NewRequest("POST", p.Config.TokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("无法连接到 %s 服务器: %v", p.Name, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("%s 令牌交换失败，状态码: %d", p.Name, resp.StatusCode)
	}

	var token OAuth2Token
	if err := json.NewDecoder(resp.Body).Decode(&token); err != nil {
		return nil, fmt.Errorf("解析 %s 令牌响应失败: %v", p.Name, err)
	}

	if token.AccessToken == "" {
		return nil, fmt.Errorf("%s 返回的访问令牌为空", p.Name)
	}

	return &token, nil
}

func (p *BaseOAuth2Provider) GetUserInfo(token *OAuth2Token) (*OAuth2User, error) {
	req, err := http.NewRequest("GET", p.Config.UserInfoURL, nil)
	if err != nil {
		return nil, err
	}

	// 设置Authorization头
	authHeader := "Bearer " + token.AccessToken
	if token.TokenType != "" && strings.ToLower(token.TokenType) != "bearer" {
		authHeader = token.TokenType + " " + token.AccessToken
	}
	req.Header.Set("Authorization", authHeader)
	req.Header.Set("Accept", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("无法获取 %s 用户信息: %v", p.Name, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("获取 %s 用户信息失败，状态码: %d", p.Name, resp.StatusCode)
	}

	// 解析用户信息需要由具体提供商实现
	var userInfo map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("解析 %s 用户信息失败: %v", p.Name, err)
	}

	return p.parseUserInfo(userInfo)
}

// parseUserInfo 解析用户信息，需要由具体提供商重写
func (p *BaseOAuth2Provider) parseUserInfo(data map[string]interface{}) (*OAuth2User, error) {
	return nil, errors.New("parseUserInfo 方法需要由具体提供商实现")
}

// OAuth2Service OAuth2服务
type OAuth2Service struct {
	providers map[string]OAuth2Provider
}

// NewOAuth2Service 创建OAuth2服务实例
func NewOAuth2Service() *OAuth2Service {
	return &OAuth2Service{
		providers: make(map[string]OAuth2Provider),
	}
}

// RegisterProvider 注册OAuth2提供商
func (s *OAuth2Service) RegisterProvider(provider OAuth2Provider) {
	s.providers[provider.GetName()] = provider
}

// GetProvider 获取OAuth2提供商
func (s *OAuth2Service) GetProvider(name string) (OAuth2Provider, error) {
	provider, exists := s.providers[name]
	if !exists {
		return nil, fmt.Errorf("OAuth2提供商 '%s' 不存在", name)
	}
	return provider, nil
}

// GetEnabledProviders 获取所有启用的提供商
func (s *OAuth2Service) GetEnabledProviders() []OAuth2Provider {
	var enabled []OAuth2Provider
	for _, provider := range s.providers {
		if provider.IsEnabled() {
			enabled = append(enabled, provider)
		}
	}
	return enabled
}

// GenerateAuthURL 生成授权URL
func (s *OAuth2Service) GenerateAuthURL(providerName, state, redirectURI string) (string, error) {
	provider, err := s.GetProvider(providerName)
	if err != nil {
		return "", err
	}

	if !provider.IsEnabled() {
		return "", fmt.Errorf("OAuth2提供商 '%s' 未启用", providerName)
	}

	return provider.GetAuthURL(state, redirectURI), nil
}

// ExchangeCodeForUser 通过授权码获取用户信息
func (s *OAuth2Service) ExchangeCodeForUser(providerName, code, redirectURI string) (*OAuth2User, error) {
	provider, err := s.GetProvider(providerName)
	if err != nil {
		return nil, err
	}

	if !provider.IsEnabled() {
		return nil, fmt.Errorf("OAuth2提供商 '%s' 未启用", providerName)
	}

	token, err := provider.ExchangeToken(code, redirectURI)
	if err != nil {
		return nil, err
	}

	return provider.GetUserInfo(token)
}

// OAuth2Manager OAuth2管理器，负责管理所有OAuth2提供商
type OAuth2Manager struct {
	service *OAuth2Service
}

// NewOAuth2Manager 创建OAuth2管理器
func NewOAuth2Manager() *OAuth2Manager {
	return &OAuth2Manager{
		service: NewOAuth2Service(),
	}
}

// RegisterBuiltinProviders 注册内置的OAuth2提供商
func (m *OAuth2Manager) RegisterBuiltinProviders() {
	// 注册GitHub提供商
	m.service.RegisterProvider(NewGitHubProvider())

	// 注册LinuxDO提供商
	m.service.RegisterProvider(NewLinuxDOProvider())

	// 注册OIDC提供商
	m.service.RegisterProvider(NewOIDCProvider())
}

// RegisterGenericProvider 注册通用OAuth2提供商
func (m *OAuth2Manager) RegisterGenericProvider(name string, config OAuth2Config, parser func(map[string]interface{}) (*OAuth2User, error)) {
	provider := NewGenericOAuth2Provider(name, config, parser)
	m.service.RegisterProvider(provider)
}

// RegisterProviderFromTemplate 从模板注册OAuth2提供商
func (m *OAuth2Manager) RegisterProviderFromTemplate(name, templateName, clientID, clientSecret string, enabled bool) error {
	template, exists := OAuth2ProviderTemplates[templateName]
	if !exists {
		return fmt.Errorf("OAuth2提供商模板 '%s' 不存在", templateName)
	}

	parser, exists := OAuth2UserParsers[templateName]
	if !exists {
		return fmt.Errorf("OAuth2用户解析器 '%s' 不存在", templateName)
	}

	config := template
	config.ClientID = clientID
	config.ClientSecret = clientSecret
	config.Enabled = enabled

	m.RegisterGenericProvider(name, config, parser)
	return nil
}

// GetService 获取OAuth2服务实例
func (m *OAuth2Manager) GetService() *OAuth2Service {
	return m.service
}

// GetProviderInfo 获取提供商信息（用于前端显示）
func (m *OAuth2Manager) GetProviderInfo() []map[string]interface{} {
	var info []map[string]interface{}

	for _, provider := range m.service.GetEnabledProviders() {
		info = append(info, map[string]interface{}{
			"name":      provider.GetName(),
			"client_id": provider.GetClientID(),
			"enabled":   provider.IsEnabled(),
		})
	}

	return info
}

// ValidateProviderConfig 验证提供商配置
func (m *OAuth2Manager) ValidateProviderConfig(name string) error {
	provider, err := m.service.GetProvider(name)
	if err != nil {
		return err
	}

	if !provider.IsEnabled() {
		return fmt.Errorf("OAuth2提供商 '%s' 未启用或配置不完整", name)
	}

	return nil
}

// 全局OAuth2管理器实例
var GlobalOAuth2Manager *OAuth2Manager

// InitOAuth2Service 初始化OAuth2服务
func InitOAuth2Service() {
	// 初始化配置管理器
	InitOAuth2ConfigManager()

	// 初始化OAuth2管理器
	GlobalOAuth2Manager = NewOAuth2Manager()
	GlobalOAuth2Manager.RegisterBuiltinProviders()

	// 注册动态配置的提供商
	GlobalOAuth2ConfigManager.RegisterDynamicProviders(GlobalOAuth2Manager)

	common.SysLog("OAuth2 service initialized with built-in and dynamic providers")
}
