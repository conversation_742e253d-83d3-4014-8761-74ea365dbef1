var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import * as React from "react";
function SvgComponent(props) {
  return /* @__PURE__ */ React.createElement("svg", __spreadValues({
    width: 200,
    height: 200,
    viewBox: "0 0 200 200",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    focusable: false,
    "aria-hidden": true
  }, props), /* @__PURE__ */ React.createElement("rect", {
    width: 200,
    height: 200,
    fill: "transparent"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "m123.17 114.66-.2-.18-34.6 27.1.46-.04c10.03-.9 16.71-.38 18.79 0l.1.02 15.45-26.9Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("rect", {
    x: 67.706,
    y: 73.871,
    width: 55.6333,
    height: 39.8004,
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "m49.97 136.5 17.7-22.44h55.44l-17.7 22.45H49.97Z",
    fill: "#E6E8EA",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M101.67 78.57H75.75v21.45h18.77v6.25h14.6v-13.7h-7.45v-14Z",
    fill: "#C6CACD"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M76.66 79.43h24.26v13.14h-6.4v6.65H76.66v-19.8Zm17.86 20.62H75.74V78.6h26.01v13.97h7.36v13.7h-14.6v-6.22Zm7.23-6.65h6.54v12.05H95.34v-5.4h6.41V93.4Zm-.83 0v5.82h-5.58V93.4h5.58Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M43.29 174.89H33.5c4.19-1.22 4.04-11.99 3.44-17.22.88 5.73 4.6 13.87 6.34 17.22Z",
    fill: "#515151",
    stroke: "black"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M75.93 40.59a32.78 32.78 0 1 1-65.56 0 32.78 32.78 0 0 1 65.56 0Z",
    fill: "var(--semi-color-primary-light-default)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M47.56 25.12a4.4 4.4 0 0 0-8.82 0v11.06H27.68a4.4 4.4 0 0 0 0 8.82h11.06v11.06a4.4 4.4 0 0 0 8.82 0V45h11.06a4.4 4.4 0 0 0 0-8.82H47.56V25.12Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M63.82 83.33c-4.52-1.84-13.53-7.96-13.38-17.79",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M52.59 110.23c2.88.1 6.5.91 9.97 4.88 2.48 2.84 4.67 7.21 7.45 14.5a73.53 73.53 0 0 1 4.27-16.32c1.2-3.07 5.29-10.25 9.35-10.52 4.06-.26 4.47 3.57 2.57 4.65-1.9 1.08-3 .96-4.8 3.41-1.77 2.46-2.95 4.95-2 11.34.72-2.94 1.76-4.59 2.77-5.71 1-1.12 8.8-8.06 14.36-8.35 3.86-.2 5.2.7 4.78 1.73a9.27 9.27 0 0 1 4.08-.33c2.08.41 2.78 1.9 1.83 3.18 2.19-.16 4.28.23 5.36 **********.24 1.64-.56 2.58-.77.92-2.25 1.64-2.83 1.95-1.2.63-5.94 3.72-7.35 8.1-1.41 4.4-2.73 7.47 3.46 20.83 4.71 10.16 5.64 21.33 1.87 29.6 8.96-.27 16.89-2.14 22.02-5.04 4.39-2.48 8.27-5.5 10.39-10.5-2.33-19.66-2.27-57.26.91-57.43 4.5-.23 1.73 48 0 54.7a19.5 19.5 0 0 1-.91 2.72c.67 5.67 1.54 9.84 2.59 11.2 1.7 2.19 40.44 4.76 50.2 1.59 1.28-.42 2.07-.93 2.2-1.55 1.02-4.54-14.79-8.26-20.87-13.96-4.19-3.91-2.77-32.42-4.97-50.45-2.2-18.04-7.9-36.48-25.26-40.71-13.62-3.33-24.98 1.73-30.14 5.64H85.62c2.71-5.6 10.46-16.06 26.4-19.18 19.26-3.78 36.18 5.19 46.96 17.46 4.95 5.24 7.75 8.71 9.23 8.71 1.48 0 9.22-4.68 17.6-5.22 2.5-.16 2.46 2.64 1.19 6.8a30.8 30.8 0 0 1-2.09 5.07c-2.44 4.94-7.84 14.3-16.18 21.42 2.2 18.04.78 46.55 4.97 50.46 6.08 5.7 21.89 9.42 20.88 13.96-.14.62-.93 1.13-2.2 1.55-9.77 3.17-48.51.6-50.21-1.59-1.05-1.36-1.92-5.53-2.6-11.2-2.1 5.01-5.99 8.03-10.38 10.51-5.13 2.9-13.06 4.77-22.02 5.05a20.37 20.37 0 0 1-5.01 6.87c-9 7.98-28.99 11.67-47.77 1.67-9.57-5.09-14.72-17.62-17.5-30.17v.06c.85 8.92 1.34 14.13-2.18 18.52a49.9 49.9 0 0 1-19.58-1.92c-5.22-1.61-12.91-8.31-8.38-22.61 4.54-14.3 27.98-32.78 30.14-34.21 2.15-1.44 7.96-5.58 15.7-5.58Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M70.01 129.61c-1.78 10.7.14 28.07 3.33 27.29 1-.25 1.57-1.58 1.67-3.9 7.16.34 27.77 7.99 34.47 11.8.15 4.62-.59 9.01-2.31 12.8a20.38 20.38 0 0 1-5.01 6.87c-9 7.98-28.99 11.67-47.77 1.67-9.57-5.09-14.72-17.62-17.5-30.17v.06c.85 8.92 1.34 14.13-2.18 18.52a49.9 49.9 0 0 1-19.58-1.92c-5.22-1.61-12.91-8.31-8.38-22.61 4.54-14.3 27.98-32.78 30.14-34.21 2.15-1.44 7.96-5.58 15.7-5.58 2.88.1 6.5.91 9.97 4.88 2.48 2.84 4.67 7.21 7.45 14.5Z",
    fill: "#E6E8EA"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M105.3 148c4.71 10.15 5.64 21.32 1.87 29.6 8.96-.28 16.89-2.15 22.02-5.05 4.39-2.48 8.27-5.5 10.39-10.5-2.33-19.66-2.27-57.26.91-57.43 4.5-.23 1.73 48 0 54.7a19.5 19.5 0 0 1-.91 2.72c.67 5.67 1.54 9.84 2.59 11.2 1.7 2.19 40.44 4.76 50.2 1.59 1.28-.41 2.07-.93 2.2-1.55 1.02-4.54-14.79-8.26-20.87-13.96-4.19-3.91-2.77-32.42-4.97-50.45-2.2-18.04-7.9-36.48-25.26-40.71-13.62-3.33-24.98 1.73-30.14 5.64h10.17v39.9l-16 27.8-4.96-.07a86.47 86.47 0 0 0 2.76 6.56Z",
    fill: "white",
    className: ""
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M70.01 129.61c5.5 16.51 6.26 26.57 3.33 27.29-3.2.78-5.1-16.59-3.33-27.29Zm0 0c-2.78-7.29-4.97-11.66-7.45-14.5-3.46-3.97-7.09-4.79-9.97-4.88-7.74 0-13.55 4.14-15.7 5.58-2.16 1.43-25.6 19.9-30.14 34.2-4.53 14.3 3.16 21 8.38 22.62 6.19 1.92 12.5 2.54 19.58 1.92 3.53-4.4 3.03-9.62 2.18-18.58M70 129.61a73.5 73.5 0 0 1 4.27-16.32c1.2-3.07 5.29-10.25 9.35-10.52 4.06-.26 4.47 3.57 2.57 4.65-1.9 1.08-3 .96-4.8 3.41-1.77 2.46-2.95 4.95-2 11.34.72-2.94 1.76-4.59 2.77-5.7 1-1.13 8.8-8.07 14.36-8.36 5.57-.29 5.88 1.73 3.28 3.08-2.6 1.34-7.62 2.6-14.34 12.16 0 0-.6-5.24 10-11.04 2.88-1.57 7-3.36 9.92-2.8 3.14.62 3.12 3.7-.94 4.81a15.88 15.88 0 0 0-5.09 2.92 30.45 30.45 0 0 0-5.78 6.54c.43-3.53 4.31-8.52 8.96-10.08 3.21-1.08 8.15-1.98 10.04.83.33.49.24 1.64-.56 2.59-.77.91-2.25 1.63-2.83 1.94-1.2.63-5.94 3.72-7.35 8.1-1.15 3.57-2.23 6.26.7 14.27m-68.88-15.17c.08 5.48.55 17.6 3.23 29.71m0 0c2.78 12.55 7.93 25.09 17.5 30.17 18.78 10 38.78 6.3 47.77-1.67a20.4 20.4 0 0 0 5-6.87m0 0c3.78-8.28 2.85-19.45-1.86-29.6a86.47 86.47 0 0 1-2.76-6.57m4.63 36.17c8.96-.28 16.89-2.15 22.02-5.05 4.39-2.48 8.27-5.5 10.39-10.5m29.15-53.19c2.2 18.04.78 46.55 4.97 50.46 6.08 5.7 21.89 9.42 20.88 13.96-.14.62-.93 1.14-2.2 1.55m-23.65-65.97c-2.2-18.03-7.9-36.47-25.26-40.7-13.62-3.33-24.98 1.73-30.14 5.64m55.4 35.06c8.35-7.12 13.74-16.48 16.18-21.42 1-2 1.57-3.36 2.09-5.07m5.38 92.46c-9.77 3.17-48.51.6-50.21-1.59-1.05-1.36-1.92-5.53-2.6-11.2m52.8 12.79c-.42-2.92-1.24-3.56-4.8-4.6M187 82.37c1.27-4.16 1.3-6.96-1.19-6.8-8.38.54-16.12 5.22-17.6 5.22-1.49 0-4.28-3.46-9.23-8.7-10.78-12.28-27.7-21.25-46.96-17.47-15.94 3.12-23.69 13.58-26.4 19.18h27.7M187 82.37c.01-1 .15-2.07-.43-2.74-1.77-2.02-5.89-2.07-6.46 1.98m-66.79-7.81h10.18v39.9l-16 27.8-4.96-.07m37.04 20.61c-2.33-19.65-2.27-57.25.91-57.42 4.5-.23 1.73 48 0 54.7a19.5 19.5 0 0 1-.91 2.72Z",
    stroke: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M58.12 104.22A10.5 10.5 0 0 1 47.7 114.8a10.5 10.5 0 0 1-10.43-10.58A10.5 10.5 0 0 1 47.7 93.65a10.5 10.5 0 0 1 10.43 10.57Z",
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("mask", {
    id: "path-16-inside-1",
    fill: "white"
  }, /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M49.01 93.79c6.18.59 4.56 12.3.6 17.58-2.89 3.82-1.18 7.13.9 11.15a77 77 0 0 1 1.28 2.56 17.5 17.5 0 0 1 3.1 9.92c0 10.25-8.96 18.55-20 18.55S14.9 145.25 14.9 135c0-7.89 5.3-14.63 12.79-17.31 7.3-4.04 9.58-8.08 9.85-15.11.24-6.1 5.3-9.38 11.47-8.79Z"
  })), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M49.01 93.79c6.18.59 4.56 12.3.6 17.58-2.89 3.82-1.18 7.13.9 11.15a77 77 0 0 1 1.28 2.56 17.5 17.5 0 0 1 3.1 9.92c0 10.25-8.96 18.55-20 18.55S14.9 145.25 14.9 135c0-7.89 5.3-14.63 12.79-17.31 7.3-4.04 9.58-8.08 9.85-15.11.24-6.1 5.3-9.38 11.47-8.79Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "m49.6 111.37 2.4 1.8-2.4-1.8Zm-.59-17.58-.28 2.99.28-2.99Zm1.5 28.73 2.67-1.38-2.67 1.38Zm1.28 2.56-2.71 *********.13.2 2.48-1.69Zm-24.1-7.4 1.01 2.83.23-.08.2-.12-1.44-2.62Zm9.85-15.1-3-.12 3 .12ZM52 113.18c2.4-3.2 3.97-8.06 4.11-12.25.08-2.1-.2-4.37-1.18-6.27a6.93 6.93 0 0 0-5.63-3.86l-.57 5.98c.**********.***********.56 1.74.5 3.3-.1 3.13-1.34 6.76-2.9 8.84l4.79 3.61Zm1.18 7.96c-1.1-2.12-1.8-3.53-2.03-4.8-.18-1-.08-1.94.85-3.17l-4.8-3.6a9.65 9.65 0 0 0-1.96 7.83c.43 2.34 1.62 4.6 2.6 6.5l5.34-2.76Zm1.33 2.66c-.45-.94-.91-1.84-1.33-2.66l-5.34 2.76c.43.82.85 1.63 1.24 2.46l5.43-2.56Zm-5.2 2.97a14.51 14.51 0 0 1 2.57 8.23h6a20.5 20.5 0 0 0-3.61-11.61l-4.96 3.38Zm2.57 8.23c0 8.38-7.39 15.55-16.99 15.55v6c12.49 0 23-9.44 23-21.55h-6ZM34.9 150.55c-9.6 0-16.99-7.17-16.99-15.55h-6c0 12.11 10.51 21.55 23 21.55v-6ZM17.9 135c0-6.47 4.36-12.18 10.8-14.49l-2.03-5.65C18.15 117.92 11.9 125.7 11.9 135h6Zm16.64-32.54c-.12 3.22-.7 5.4-1.82 7.2-1.15 1.8-3.07 3.51-6.49 5.4l2.9 5.25c3.9-2.14 6.77-4.47 8.65-7.45 1.9-3 2.61-6.35 2.76-10.17l-6-.23ZM49.3 90.8c-3.67-.34-7.25.43-10.02 2.46a11.64 11.64 0 0 0-4.74 9.2l6 .23a5.65 5.65 0 0 1 2.28-4.59c1.36-1 3.4-1.56 5.91-1.32l.57-5.98Z",
    fill: "#515151",
    mask: "url(#path-16-inside-1)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M54.1 105.95c0 1.69-1.43 3.1-3.24 3.1-1.81 0-3.24-1.41-3.24-3.1 0-1.7 1.43-3.1 3.24-3.1 1.8 0 3.24 1.4 3.24 3.1Z",
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M37.12 95.07a3.46 3.46 0 0 1-3.53 3.39c-2 0-3.54-1.56-3.54-3.4a3.46 3.46 0 0 1 3.54-3.38c1.99 0 3.53 1.55 3.53 3.39Z",
    fill: "#515151",
    stroke: "#515151",
    strokeWidth: 3
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M147.22 155.65c-.1 3.58 1.27 4.88 4.7 6.08",
    stroke: "#515151"
  }));
}
var IllustrationNoContent_default = SvgComponent;
export {
  IllustrationNoContent_default as default
};
