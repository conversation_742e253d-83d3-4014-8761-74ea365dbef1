import { BaseScale } from './base-scale';
import type { BimapType, InterpolateType, PolymapType, TransformType, IContinuousScale, ContinuousScaleType, TickData, NiceType, ScaleFishEyeOptions } from './interface';
export declare class ContinuousScale extends BaseScale implements IContinuousScale {
    readonly type: ContinuousScaleType;
    protected transformer: TransformType;
    protected untransformer: TransformType;
    protected _niceDomain: number[];
    protected _niceType?: NiceType;
    protected _domain: number[];
    protected _range: any[];
    protected _unknown: any;
    protected _forceAlign: boolean;
    protected _output?: (x: number) => number;
    protected _input?: (x: number) => number;
    protected _interpolate?: InterpolateType<any>;
    protected _piecewise: BimapType<any> | PolymapType<any>;
    protected _domainValidator?: (val: number) => boolean;
    _clamp?: (x: number) => number;
    _autoClamp?: boolean;
    constructor(transformer?: TransformType, untransformer?: TransformType);
    calculateVisibleDomain(range: any[]): any[];
    fishEye(): ScaleFishEyeOptions;
    fishEye(options: ScaleFishEyeOptions, slience?: boolean, clear?: boolean): this;
    scale(x: any): any;
    invert(y: any): any;
    domain(): any[];
    domain(_: any[], slience?: boolean): this;
    range(): any[];
    range(_: any[], slience?: boolean): this;
    rangeRound(_: any[], slience?: boolean): this;
    rescale(slience?: boolean): this;
    clamp(): boolean;
    clamp(_: boolean, f?: (x: number) => number, slience?: boolean): this;
    interpolate(): InterpolateType<any>;
    interpolate(_: InterpolateType<any>, slience?: boolean): this;
    ticks(count?: number): any[];
    tickData(count?: number): TickData[];
    rangeFactor(): [number, number];
    rangeFactor(_: [number, number], slience?: boolean): this;
    rangeFactorStart(): number;
    rangeFactorStart(_: number, slience?: boolean): this;
    rangeFactorEnd(): number;
    rangeFactorEnd(_: number, slience?: boolean): this;
    forceAlignDomainRange(): boolean;
    forceAlignDomainRange(enable: boolean): this;
}
