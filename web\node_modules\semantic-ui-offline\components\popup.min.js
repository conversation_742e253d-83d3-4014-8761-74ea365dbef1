!function(N,V,W,M){"use strict";V=void 0!==V&&V.Math==Math?V:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),N.fn.popup=function(x){var k,t=N(this),E=N(W),S=N(V),A=N("body"),F=t.selector||"",O=(new Date).getTime(),D=[],j=x,H="string"==typeof j,R=[].slice.call(arguments,1);return t.each(function(){var u,p,t,e,o,c=N.isPlainObject(x)?N.extend(!0,{},N.fn.popup.settings,x):N.extend({},N.fn.popup.settings),i=c.selector,d=c.className,f=c.error,g=c.metadata,n=c.namespace,r="."+c.namespace,a="module-"+n,h=N(this),s=N(c.context),l=N(c.scrollContext),m=N(c.boundary),v=c.target?N(c.target):h,b=0,w=!1,y=!1,P=this,C=h.data(a),T={initialize:function(){T.debug("Initializing",h),T.createID(),T.bind.events(),!T.exists()&&c.preserve&&T.create(),c.observeChanges&&T.observeChanges(),T.instantiate()},instantiate:function(){T.verbose("Storing instance",T),C=T,h.data(a,C)},observeChanges:function(){"MutationObserver"in V&&((t=new MutationObserver(T.event.documentChanged)).observe(W,{childList:!0,subtree:!0}),T.debug("Setting up mutation observer",t))},refresh:function(){c.popup?u=N(c.popup).eq(0):c.inline&&(u=v.nextAll(i.popup).eq(0),c.popup=u),c.popup?(u.addClass(d.loading),p=T.get.offsetParent(),u.removeClass(d.loading),c.movePopup&&T.has.popup()&&T.get.offsetParent(u)[0]!==p[0]&&(T.debug("Moving popup to the same offset parent as target"),u.detach().appendTo(p))):p=c.inline?T.get.offsetParent(v):T.has.popup()?T.get.offsetParent(u):A,p.is("html")&&p[0]!==A[0]&&(T.debug("Setting page as offset parent"),p=A),T.get.variation()&&T.set.variation()},reposition:function(){T.refresh(),T.set.position()},destroy:function(){T.debug("Destroying previous module"),t&&t.disconnect(),u&&!c.preserve&&T.removePopup(),clearTimeout(T.hideTimer),clearTimeout(T.showTimer),T.unbind.close(),T.unbind.events(),h.removeData(a)},event:{start:function(t){var e=N.isPlainObject(c.delay)?c.delay.show:c.delay;clearTimeout(T.hideTimer),y||(T.showTimer=setTimeout(T.show,e))},end:function(){var t=N.isPlainObject(c.delay)?c.delay.hide:c.delay;clearTimeout(T.showTimer),T.hideTimer=setTimeout(T.hide,t)},touchstart:function(t){y=!0,T.show()},resize:function(){T.is.visible()&&T.set.position()},documentChanged:function(t){[].forEach.call(t,function(t){t.removedNodes&&[].forEach.call(t.removedNodes,function(t){(t==P||0<N(t).find(P).length)&&(T.debug("Element removed from DOM, tearing down events"),T.destroy())})})},hideGracefully:function(t){var e=N(t.target),o=N.contains(W.documentElement,t.target),n=0<e.closest(i.popup).length;t&&!n&&o?(T.debug("Click occurred outside popup hiding popup"),T.hide()):T.debug("Click was inside popup, keeping popup open")}},create:function(){var t=T.get.html(),e=T.get.title(),o=T.get.content();t||o||e?(T.debug("Creating pop-up html"),t=t||c.templates.popup({title:e,content:o}),u=N("<div/>").addClass(d.popup).data(g.activator,h).html(t),c.inline?(T.verbose("Inserting popup element inline",u),u.insertAfter(h)):(T.verbose("Appending popup element to body",u),u.appendTo(s)),T.refresh(),T.set.variation(),c.hoverable&&T.bind.popup(),c.onCreate.call(u,P)):0!==v.next(i.popup).length?(T.verbose("Pre-existing popup found"),c.inline=!0,c.popup=v.next(i.popup).data(g.activator,h),T.refresh(),c.hoverable&&T.bind.popup()):c.popup?(N(c.popup).data(g.activator,h),T.verbose("Used popup specified in settings"),T.refresh(),c.hoverable&&T.bind.popup()):T.debug("No content specified skipping display",P)},createID:function(){o=(Math.random().toString(16)+"000000000").substr(2,8),e="."+o,T.verbose("Creating unique id for element",o)},toggle:function(){T.debug("Toggling pop-up"),T.is.hidden()?(T.debug("Popup is hidden, showing pop-up"),T.unbind.close(),T.show()):(T.debug("Popup is visible, hiding pop-up"),T.hide())},show:function(t){if(t=t||function(){},T.debug("Showing pop-up",c.transition),T.is.hidden()&&(!T.is.active()||!T.is.dropdown())){if(T.exists()||T.create(),!1===c.onShow.call(u,P))return void T.debug("onShow callback returned false, cancelling popup animation");c.preserve||c.popup||T.refresh(),u&&T.set.position()&&(T.save.conditions(),c.exclusive&&T.hideAll(),T.animate.show(t))}},hide:function(t){if(t=t||function(){},T.is.visible()||T.is.animating()){if(!1===c.onHide.call(u,P))return void T.debug("onHide callback returned false, cancelling popup animation");T.remove.visible(),T.unbind.close(),T.restore.conditions(),T.animate.hide(t)}},hideAll:function(){N(i.popup).filter("."+d.popupVisible).each(function(){N(this).data(g.activator).popup("hide")})},exists:function(){return!!u&&(c.inline||c.popup?T.has.popup():1<=u.closest(s).length)},removePopup:function(){T.has.popup()&&!c.popup&&(T.debug("Removing popup",u),u.remove(),u=M,c.onRemove.call(u,P))},save:{conditions:function(){T.cache={title:h.attr("title")},T.cache.title&&h.removeAttr("title"),T.verbose("Saving original attributes",T.cache.title)}},restore:{conditions:function(){return T.cache&&T.cache.title&&(h.attr("title",T.cache.title),T.verbose("Restoring original attributes",T.cache.title)),!0}},supports:{svg:function(){return"undefined"==typeof SVGGraphicsElement}},animate:{show:function(t){t=N.isFunction(t)?t:function(){},c.transition&&N.fn.transition!==M&&h.transition("is supported")?(T.set.visible(),u.transition({animation:c.transition+" in",queue:!1,debug:c.debug,verbose:c.verbose,duration:c.duration,onComplete:function(){T.bind.close(),t.call(u,P),c.onVisible.call(u,P)}})):T.error(f.noTransition)},hide:function(t){t=N.isFunction(t)?t:function(){},T.debug("Hiding pop-up"),!1!==c.onHide.call(u,P)?c.transition&&N.fn.transition!==M&&h.transition("is supported")?u.transition({animation:c.transition+" out",queue:!1,duration:c.duration,debug:c.debug,verbose:c.verbose,onComplete:function(){T.reset(),t.call(u,P),c.onHidden.call(u,P)}}):T.error(f.noTransition):T.debug("onHide callback returned false, cancelling popup animation")}},change:{content:function(t){u.html(t)}},get:{html:function(){return h.removeData(g.html),h.data(g.html)||c.html},title:function(){return h.removeData(g.title),h.data(g.title)||c.title},content:function(){return h.removeData(g.content),h.data(g.content)||c.content||h.attr("title")},variation:function(){return h.removeData(g.variation),h.data(g.variation)||c.variation},popup:function(){return u},popupOffset:function(){return u.offset()},calculations:function(){var t,e,o=T.get.offsetParent(u),n=v[0],i=m[0]==V,r=c.inline||c.popup&&c.movePopup?v.position():v.offset(),a=i?{top:0,left:0}:m.offset(),s={},l=i?{top:S.scrollTop(),left:S.scrollLeft()}:{top:0,left:0},s={target:{element:v[0],width:v.outerWidth(),height:v.outerHeight(),top:r.top,left:r.left,margin:{}},popup:{width:u.outerWidth(),height:u.outerHeight()},parent:{width:p.outerWidth(),height:p.outerHeight()},screen:{top:a.top,left:a.left,scroll:{top:l.top,left:l.left},width:m.width(),height:m.height()}};return o.get(0)!==p.get(0)&&(e=o.offset(),s.target.top-=e.top,s.target.left-=e.left,s.parent.width=o.outerWidth(),s.parent.height=o.outerHeight()),c.setFluidWidth&&T.is.fluid()&&(s.container={width:u.parent().outerWidth()},s.popup.width=s.container.width),s.target.margin.top=c.inline?parseInt(V.getComputedStyle(n).getPropertyValue("margin-top"),10):0,s.target.margin.left=c.inline?T.is.rtl()?parseInt(V.getComputedStyle(n).getPropertyValue("margin-right"),10):parseInt(V.getComputedStyle(n).getPropertyValue("margin-left"),10):0,t=s.screen,s.boundary={top:t.top+t.scroll.top,bottom:t.top+t.scroll.top+t.height,left:t.left+t.scroll.left,right:t.left+t.scroll.left+t.width},s},id:function(){return o},startEvent:function(){return"hover"==c.on?"mouseenter":"focus"==c.on&&"focus"},scrollEvent:function(){return"scroll"},endEvent:function(){return"hover"==c.on?"mouseleave":"focus"==c.on&&"blur"},distanceFromBoundary:function(t,e){var o={},n=(e=e||T.get.calculations()).popup,i=e.boundary;return t&&(o={top:t.top-i.top,left:t.left-i.left,right:i.right-(t.left+n.width),bottom:i.bottom-(t.top+n.height)},T.verbose("Distance from boundaries determined",t,o)),o},offsetParent:function(t){var e=(t!==M?t[0]:v[0]).parentNode,o=N(e);if(e)for(var n="none"===o.css("transform"),i="static"===o.css("position"),r=o.is("body");e&&!r&&i&&n;)e=e.parentNode,n="none"===(o=N(e)).css("transform"),i="static"===o.css("position"),r=o.is("body");return o&&0<o.length?o:N()},positions:function(){return{"top left":!1,"top center":!1,"top right":!1,"bottom left":!1,"bottom center":!1,"bottom right":!1,"left center":!1,"right center":!1}},nextPosition:function(t){var e=t.split(" "),o=e[0],n=e[1],i="top"==o||"bottom"==o,r=!1,a=!1,s=!1;return w||(T.verbose("All available positions available"),w=T.get.positions()),T.debug("Recording last position tried",t),w[t]=!0,"opposite"===c.prefer&&(s=(s=[{top:"bottom",bottom:"top",left:"right",right:"left"}[o],n]).join(" "),r=!0===w[s],T.debug("Trying opposite strategy",s)),"adjacent"===c.prefer&&i&&(s=(s=[o,{left:"center",center:"right",right:"left"}[n]]).join(" "),a=!0===w[s],T.debug("Trying adjacent strategy",s)),(a||r)&&(T.debug("Using backup position",s),s={"top left":"top center","top center":"top right","top right":"right center","right center":"bottom right","bottom right":"bottom center","bottom center":"bottom left","bottom left":"left center","left center":"top left"}[t]),s}},set:{position:function(t,e){if(0!==v.length&&0!==u.length){var o,n,i,r,a,s,l,p;if(e=e||T.get.calculations(),t=t||h.data(g.position)||c.position,o=h.data(g.offset)||c.offset,n=c.distanceAway,i=e.target,r=e.popup,a=e.parent,T.should.centerArrow(e)&&(T.verbose("Adjusting offset to center arrow on small target element"),"top left"!=t&&"bottom left"!=t||(o+=i.width/2,o-=c.arrowPixelsFromEdge),"top right"!=t&&"bottom right"!=t||(o-=i.width/2,o+=c.arrowPixelsFromEdge)),0===i.width&&0===i.height&&!T.is.svg(i.element))return T.debug("Popup target is hidden, no action taken"),!1;switch(c.inline&&(T.debug("Adding margin to calculation",i.margin),"left center"==t||"right center"==t?(o+=i.margin.top,n+=-i.margin.left):"top left"==t||"top center"==t||"top right"==t?(o+=i.margin.left,n-=i.margin.top):(o+=i.margin.left,n+=i.margin.top)),T.debug("Determining popup position from calculations",t,e),T.is.rtl()&&(t=t.replace(/left|right/g,function(t){return"left"==t?"right":"left"}),T.debug("RTL: Popup position updated",t)),b==c.maxSearchDepth&&"string"==typeof c.lastResort&&(t=c.lastResort),t){case"top left":s={top:"auto",bottom:a.height-i.top+n,left:i.left+o,right:"auto"};break;case"top center":s={bottom:a.height-i.top+n,left:i.left+i.width/2-r.width/2+o,top:"auto",right:"auto"};break;case"top right":s={bottom:a.height-i.top+n,right:a.width-i.left-i.width-o,top:"auto",left:"auto"};break;case"left center":s={top:i.top+i.height/2-r.height/2+o,right:a.width-i.left+n,left:"auto",bottom:"auto"};break;case"right center":s={top:i.top+i.height/2-r.height/2+o,left:i.left+i.width+n,bottom:"auto",right:"auto"};break;case"bottom left":s={top:i.top+i.height+n,left:i.left+o,bottom:"auto",right:"auto"};break;case"bottom center":s={top:i.top+i.height+n,left:i.left+i.width/2-r.width/2+o,bottom:"auto",right:"auto"};break;case"bottom right":s={top:i.top+i.height+n,right:a.width-i.left-i.width-o,left:"auto",bottom:"auto"}}if(s===M&&T.error(f.invalidPosition,t),T.debug("Calculated popup positioning values",s),u.css(s).removeClass(d.position).addClass(t).addClass(d.loading),l=T.get.popupOffset(),p=T.get.distanceFromBoundary(l,e),T.is.offstage(p,t)){if(T.debug("Position is outside viewport",t),b<c.maxSearchDepth)return b++,t=T.get.nextPosition(t),T.debug("Trying new position",t),!!u&&T.set.position(t,e);if(!c.lastResort)return T.debug("Popup could not find a position to display",u),T.error(f.cannotPlace,P),T.remove.attempts(),T.remove.loading(),T.reset(),c.onUnplaceable.call(u,P),!1;T.debug("No position found, showing with last position")}return T.debug("Position is on stage",t),T.remove.attempts(),T.remove.loading(),c.setFluidWidth&&T.is.fluid()&&T.set.fluidWidth(e),!0}T.error(f.notFound)},fluidWidth:function(t){t=t||T.get.calculations(),T.debug("Automatically setting element width to parent width",t.parent.width),u.css("width",t.container.width)},variation:function(t){(t=t||T.get.variation())&&T.has.popup()&&(T.verbose("Adding variation to popup",t),u.addClass(t))},visible:function(){h.addClass(d.visible)}},remove:{loading:function(){u.removeClass(d.loading)},variation:function(t){(t=t||T.get.variation())&&(T.verbose("Removing variation",t),u.removeClass(t))},visible:function(){h.removeClass(d.visible)},attempts:function(){T.verbose("Resetting all searched positions"),b=0,w=!1}},bind:{events:function(){T.debug("Binding popup events to module"),"click"==c.on&&h.on("click"+r,T.toggle),"hover"==c.on&&h.on("touchstart"+r,T.event.touchstart),T.get.startEvent()&&h.on(T.get.startEvent()+r,T.event.start).on(T.get.endEvent()+r,T.event.end),c.target&&T.debug("Target set to element",v),S.on("resize"+e,T.event.resize)},popup:function(){T.verbose("Allowing hover events on popup to prevent closing"),u&&T.has.popup()&&u.on("mouseenter"+r,T.event.start).on("mouseleave"+r,T.event.end)},close:function(){(!0===c.hideOnScroll||"auto"==c.hideOnScroll&&"click"!=c.on)&&T.bind.closeOnScroll(),T.is.closable()?T.bind.clickaway():"hover"==c.on&&y&&T.bind.touchClose()},closeOnScroll:function(){T.verbose("Binding scroll close event to document"),l.one(T.get.scrollEvent()+e,T.event.hideGracefully)},touchClose:function(){T.verbose("Binding popup touchclose event to document"),E.on("touchstart"+e,function(t){T.verbose("Touched away from popup"),T.event.hideGracefully.call(P,t)})},clickaway:function(){T.verbose("Binding popup close event to document"),E.on("click"+e,function(t){T.verbose("Clicked away from popup"),T.event.hideGracefully.call(P,t)})}},unbind:{events:function(){S.off(e),h.off(r)},close:function(){E.off(e),l.off(e)}},has:{popup:function(){return u&&0<u.length}},should:{centerArrow:function(t){return!T.is.basic()&&t.target.width<=2*c.arrowPixelsFromEdge}},is:{closable:function(){return"auto"==c.closable?"hover"!=c.on:c.closable},offstage:function(t,o){var n=[];return N.each(t,function(t,e){e<-c.jitter&&(T.debug("Position exceeds allowable distance from edge",t,e,o),n.push(t))}),0<n.length},svg:function(t){return T.supports.svg()&&t instanceof SVGGraphicsElement},basic:function(){return h.hasClass(d.basic)},active:function(){return h.hasClass(d.active)},animating:function(){return u!==M&&u.hasClass(d.animating)},fluid:function(){return u!==M&&u.hasClass(d.fluid)},visible:function(){return u!==M&&u.hasClass(d.popupVisible)},dropdown:function(){return h.hasClass(d.dropdown)},hidden:function(){return!T.is.visible()},rtl:function(){return"rtl"==h.css("direction")}},reset:function(){T.remove.visible(),c.preserve?N.fn.transition!==M&&u.transition("remove transition"):T.removePopup()},setting:function(t,e){if(N.isPlainObject(t))N.extend(!0,c,t);else{if(e===M)return c[t];c[t]=e}},internal:function(t,e){if(N.isPlainObject(t))N.extend(!0,T,t);else{if(e===M)return T[t];T[t]=e}},debug:function(){!c.silent&&c.debug&&(c.performance?T.performance.log(arguments):(T.debug=Function.prototype.bind.call(console.info,console,c.name+":"),T.debug.apply(console,arguments)))},verbose:function(){!c.silent&&c.verbose&&c.debug&&(c.performance?T.performance.log(arguments):(T.verbose=Function.prototype.bind.call(console.info,console,c.name+":"),T.verbose.apply(console,arguments)))},error:function(){c.silent||(T.error=Function.prototype.bind.call(console.error,console,c.name+":"),T.error.apply(console,arguments))},performance:{log:function(t){var e,o;c.performance&&(o=(e=(new Date).getTime())-(O||e),O=e,D.push({Name:t[0],Arguments:[].slice.call(t,1)||"",Element:P,"Execution Time":o})),clearTimeout(T.performance.timer),T.performance.timer=setTimeout(T.performance.display,500)},display:function(){var t=c.name+":",o=0;O=!1,clearTimeout(T.performance.timer),N.each(D,function(t,e){o+=e["Execution Time"]}),t+=" "+o+"ms",F&&(t+=" '"+F+"'"),(console.group!==M||console.table!==M)&&0<D.length&&(console.groupCollapsed(t),console.table?console.table(D):N.each(D,function(t,e){console.log(e.Name+": "+e["Execution Time"]+"ms")}),console.groupEnd()),D=[]}},invoke:function(n,t,e){var i,r,o,a=C;return t=t||R,e=P||e,"string"==typeof n&&a!==M&&(n=n.split(/[\. ]/),i=n.length-1,N.each(n,function(t,e){var o=t!=i?e+n[t+1].charAt(0).toUpperCase()+n[t+1].slice(1):n;if(N.isPlainObject(a[o])&&t!=i)a=a[o];else{if(a[o]!==M)return r=a[o],!1;if(!N.isPlainObject(a[e])||t==i)return a[e]!==M&&(r=a[e]),!1;a=a[e]}})),N.isFunction(r)?o=r.apply(e,t):r!==M&&(o=r),N.isArray(k)?k.push(o):k!==M?k=[k,o]:o!==M&&(k=o),r}};H?(C===M&&T.initialize(),T.invoke(j)):(C!==M&&C.invoke("destroy"),T.initialize())}),k!==M?k:this},N.fn.popup.settings={name:"Popup",silent:!1,debug:!1,verbose:!1,performance:!0,namespace:"popup",observeChanges:!0,onCreate:function(){},onRemove:function(){},onShow:function(){},onVisible:function(){},onHide:function(){},onUnplaceable:function(){},onHidden:function(){},on:"hover",boundary:V,addTouchEvents:!0,position:"top left",variation:"",movePopup:!0,target:!1,popup:!1,inline:!1,preserve:!1,hoverable:!1,content:!1,html:!1,title:!1,closable:!0,hideOnScroll:"auto",exclusive:!1,context:"body",scrollContext:V,prefer:"opposite",lastResort:!1,arrowPixelsFromEdge:20,delay:{show:50,hide:70},setFluidWidth:!0,duration:200,transition:"scale",distanceAway:0,jitter:2,offset:0,maxSearchDepth:15,error:{invalidPosition:"The position you specified is not a valid position",cannotPlace:"Popup does not fit within the boundaries of the viewport",method:"The method you called is not defined.",noTransition:"This module requires ui transitions <https://github.com/Semantic-Org/UI-Transition>",notFound:"The target or popup you specified does not exist on the page"},metadata:{activator:"activator",content:"content",html:"html",offset:"offset",position:"position",title:"title",variation:"variation"},className:{active:"active",basic:"basic",animating:"animating",dropdown:"dropdown",fluid:"fluid",loading:"loading",popup:"ui popup",position:"top left center bottom right",visible:"visible",popupVisible:"visible"},selector:{popup:".ui.popup"},templates:{escape:function(t){var e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};return/[&<>"'`]/.test(t)?t.replace(/[&<>"'`]/g,function(t){return e[t]}):t},popup:function(t){var e="",o=N.fn.popup.settings.templates.escape;return typeof t!==M&&(typeof t.title!==M&&t.title&&(t.title=o(t.title),e+='<div class="header">'+t.title+"</div>"),typeof t.content!==M&&t.content&&(t.content=o(t.content),e+='<div class="content">'+t.content+"</div>")),e}}}}(jQuery,window,document);