# Veloera OAuth2 集成

本项目为 Veloera 添加了完整的 OAuth2 认证支持，允许用户使用多种第三方服务进行登录和注册。

## 🚀 功能特性

### ✅ 支持的 OAuth2 提供商

**内置提供商**
- 🐙 **GitHub** - 使用 GitHub 账户登录
- 🔐 **OIDC** - 支持标准 OpenID Connect 提供商  
- 🐧 **LinuxDO** - 使用 LinuxDO 社区账户登录

**通用提供商**
- 🔍 **Google** - 使用 Google 账户登录
- 🏢 **Microsoft** - 使用 Microsoft/Azure AD 账户登录
- 🎮 **Discord** - 使用 Discord 账户登录
- 🦊 **GitLab** - 使用 GitLab 账户登录

### ✅ 核心功能

- 🔄 **统一认证流程** - 所有提供商使用相同的认证流程
- 🔗 **账户绑定** - 支持将多个 OAuth2 账户绑定到同一用户
- ⚙️ **动态配置** - 支持通过环境变量和管理界面配置
- 🛡️ **安全防护** - 内置 CSRF 保护和状态验证
- 📱 **响应式UI** - 现代化的登录界面
- 🔧 **易于扩展** - 支持添加自定义 OAuth2 提供商

## 📁 项目结构

```
├── service/
│   ├── oauth2.go              # OAuth2 核心服务
│   ├── oauth2_providers.go    # OAuth2 提供商实现
│   ├── oauth2_config.go       # OAuth2 配置管理
│   └── oauth2_test.go         # 单元测试
├── controller/
│   └── oauth2.go              # OAuth2 控制器
├── model/
│   └── user.go                # 用户模型扩展 (OAuth2 字段)
├── web/src/components/
│   ├── OAuth2LoginButtons.js  # OAuth2 登录按钮组件
│   ├── OAuth2GenericCallback.js # OAuth2 回调处理组件
│   └── utils.js               # OAuth2 工具函数
├── docs/
│   ├── OAuth2_Integration_Guide.md # 详细集成指南
│   ├── oauth2_config_example.env   # 配置示例
│   └── OAuth2_README.md            # 本文件
└── bin/
    └── migration_oauth2_providers.sql # 数据库迁移脚本
```

## 🚀 快速开始

### 1. 数据库迁移

```bash
# 执行数据库迁移，添加 oauth2_providers 字段
mysql -u username -p database_name < bin/migration_oauth2_providers.sql
```

### 2. 环境配置

复制配置示例文件：
```bash
cp docs/oauth2_config_example.env .env
```

编辑 `.env` 文件，填入您的 OAuth2 应用配置：
```bash
# Google OAuth2
OAUTH2_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH2_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH2_GOOGLE_ENABLED=true

# Microsoft OAuth2
OAUTH2_MICROSOFT_CLIENT_ID=your_microsoft_client_id
OAUTH2_MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
OAUTH2_MICROSOFT_ENABLED=true
```

### 3. 启动服务

```bash
# 启动 Veloera 服务
go run main.go
```

### 4. 管理界面配置

1. 访问管理界面：`https://your-domain.com/admin`
2. 进入 **系统设置** > **配置通用 OAuth2 提供商**
3. 填入相应的 Client ID 和 Client Secret
4. 启用需要的提供商
5. 保存设置

## 🔧 API 接口

### 获取可用提供商
```http
GET /api/oauth2/providers
```

### 获取授权URL
```http
GET /api/oauth2/auth/{provider}
```

### OAuth2回调处理
```http
GET /api/oauth2/callback/{provider}?code={code}&state={state}
```

### 解绑OAuth2账户
```http
POST /api/oauth2/unbind/{provider}
```

## 🎨 前端集成

### 使用 OAuth2 登录按钮组件

```jsx
import OAuth2LoginButtons from './components/OAuth2LoginButtons';

function LoginPage() {
  return (
    <div>
      <h2>登录</h2>
      {/* 传统登录表单 */}
      <form>...</form>
      
      {/* OAuth2 登录按钮 */}
      <OAuth2LoginButtons />
    </div>
  );
}
```

### 手动触发 OAuth2 登录

```javascript
import { onOAuth2Clicked } from './components/utils';

// 触发特定提供商登录
onOAuth2Clicked('google');
onOAuth2Clicked('microsoft');
onOAuth2Clicked('discord');
```

## 🛡️ 安全特性

- **CSRF 保护** - 使用随机状态参数防止跨站请求伪造
- **会话安全** - 安全的会话管理和令牌存储
- **HTTPS 强制** - 生产环境强制使用 HTTPS
- **密钥保护** - Client Secret 不会暴露给前端
- **输入验证** - 严格的参数验证和错误处理

## 🔄 向后兼容性

- 保留现有的 OAuth2 字段（`github_id`、`oidc_id` 等）
- 现有的 OAuth2 路由继续工作
- 平滑迁移到新的通用 OAuth2 系统
- 支持混合使用旧版和新版 OAuth2 功能

## 🧪 测试

运行单元测试：
```bash
go test ./service -v
```

测试覆盖的功能：
- OAuth2 服务核心功能
- 配置管理器
- 提供商注册和管理
- 用户信息解析器
- 提供商模板

## 📚 文档

- [详细集成指南](OAuth2_Integration_Guide.md) - 完整的配置和使用说明
- [配置示例](oauth2_config_example.env) - 环境变量配置模板
- [API 文档](../router/api-router.go) - OAuth2 相关 API 接口

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 添加新的 OAuth2 提供商

1. 在 `OAuth2ProviderTemplates` 中添加提供商模板
2. 在 `OAuth2UserParsers` 中添加用户信息解析器
3. 更新文档和配置示例
4. 添加相应的测试用例

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/Veloera/Veloera.git

# 安装依赖
go mod tidy

# 运行测试
go test ./... -v

# 启动开发服务器
go run main.go
```

## 📄 许可证

本项目采用 GNU General Public License v3.0 许可证。

## 🙏 致谢

感谢所有为 Veloera OAuth2 集成做出贡献的开发者！

---

**注意**: 在生产环境中使用前，请确保：
1. 使用 HTTPS
2. 正确配置所有 OAuth2 应用的回调 URL
3. 妥善保管 Client Secret
4. 定期更新依赖包
5. 监控认证日志
