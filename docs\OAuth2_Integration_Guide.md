# OAuth2 集成指南

本文档介绍如何在 Veloera 中配置和使用 OAuth2 认证功能。

## 概述

Veloera 支持多种 OAuth2 提供商，包括：

### 内置提供商
- **GitHub** - 使用 GitHub 账户登录
- **OIDC** - 支持标准 OpenID Connect 提供商
- **LinuxDO** - 使用 LinuxDO 社区账户登录

### 通用提供商
- **Google** - 使用 Google 账户登录
- **Microsoft** - 使用 Microsoft 账户登录
- **Discord** - 使用 Discord 账户登录
- **GitLab** - 使用 GitLab 账户登录

## 配置方法

### 1. 环境变量配置

您可以通过环境变量配置 OAuth2 提供商：

```bash
# Google OAuth2
OAUTH2_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH2_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH2_GOOGLE_ENABLED=true

# Microsoft OAuth2
OAUTH2_MICROSOFT_CLIENT_ID=your_microsoft_client_id
OAUTH2_MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
OAUTH2_MICROSOFT_ENABLED=true

# Discord OAuth2
OAUTH2_DISCORD_CLIENT_ID=your_discord_client_id
OAUTH2_DISCORD_CLIENT_SECRET=your_discord_client_secret
OAUTH2_DISCORD_ENABLED=true

# GitLab OAuth2
OAUTH2_GITLAB_CLIENT_ID=your_gitlab_client_id
OAUTH2_GITLAB_CLIENT_SECRET=your_gitlab_client_secret
OAUTH2_GITLAB_ENABLED=true
```

### 2. 管理界面配置

1. 登录管理员账户
2. 进入 **系统设置** 页面
3. 找到 **配置通用 OAuth2 提供商** 部分
4. 填入相应的 Client ID 和 Client Secret
5. 启用相应的提供商
6. 点击 **保存通用 OAuth2 设置**

## OAuth2 应用配置

### Google OAuth2

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建 OAuth2 凭据
5. 设置授权重定向 URI：`https://your-domain.com/oauth2/callback/google`

### Microsoft OAuth2

1. 访问 [Azure Portal](https://portal.azure.com/)
2. 进入 **Azure Active Directory** > **应用注册**
3. 创建新的应用注册
4. 设置重定向 URI：`https://your-domain.com/oauth2/callback/microsoft`
5. 生成客户端密钥

### Discord OAuth2

1. 访问 [Discord Developer Portal](https://discord.com/developers/applications)
2. 创建新应用程序
3. 进入 **OAuth2** 设置
4. 添加重定向 URL：`https://your-domain.com/oauth2/callback/discord`
5. 选择适当的权限范围

### GitLab OAuth2

1. 访问 [GitLab](https://gitlab.com/) 或您的 GitLab 实例
2. 进入 **用户设置** > **应用程序**
3. 创建新应用程序
4. 设置重定向 URI：`https://your-domain.com/oauth2/callback/gitlab`
5. 选择 `read_user` 权限

## API 端点

### 获取可用提供商
```
GET /api/oauth2/providers
```

### 获取授权 URL
```
GET /api/oauth2/auth/{provider}
```

### OAuth2 回调处理
```
GET /api/oauth2/callback/{provider}
```

### 解绑 OAuth2 账户
```
POST /api/oauth2/unbind/{provider}
```

## 前端集成

### 使用 OAuth2 登录按钮组件

```jsx
import OAuth2LoginButtons from './components/OAuth2LoginButtons';

function LoginPage() {
  return (
    <div>
      {/* 其他登录表单内容 */}
      <OAuth2LoginButtons />
    </div>
  );
}
```

### 手动触发 OAuth2 登录

```javascript
import { onOAuth2Clicked } from './components/utils';

// 触发 Google 登录
onOAuth2Clicked('google');

// 触发 Microsoft 登录
onOAuth2Clicked('microsoft');
```

## 数据库结构

### 用户表扩展

OAuth2 提供商信息存储在 `users` 表的 `oauth2_providers` 字段中，使用 JSON 格式：

```json
{
  "google": "google_user_id_123",
  "microsoft": "microsoft_user_id_456",
  "discord": "discord_user_id_789"
}
```

### 向后兼容性

现有的 OAuth2 字段（如 `github_id`、`oidc_id` 等）仍然保留，确保向后兼容性。

## 安全考虑

1. **HTTPS 要求**：生产环境中必须使用 HTTPS
2. **状态参数验证**：系统自动验证 OAuth2 状态参数防止 CSRF 攻击
3. **密钥保护**：Client Secret 不会发送到前端
4. **会话管理**：使用安全的会话管理机制

## 故障排除

### 常见问题

1. **回调 URL 不匹配**
   - 确保在 OAuth2 应用中配置的回调 URL 与实际使用的 URL 完全一致
   - 格式：`https://your-domain.com/oauth2/callback/{provider}`

2. **Client ID 或 Secret 错误**
   - 检查配置中的 Client ID 和 Secret 是否正确
   - 确保没有多余的空格或特殊字符

3. **权限范围不足**
   - 确保 OAuth2 应用具有读取用户基本信息的权限
   - 不同提供商的权限范围要求可能不同

4. **网络连接问题**
   - 确保服务器能够访问 OAuth2 提供商的 API 端点
   - 检查防火墙和代理设置

### 调试模式

启用调试模式查看详细的 OAuth2 处理日志：

```bash
export GIN_MODE=debug
```

### 日志查看

OAuth2 相关的日志会记录在系统日志中，包括：
- 提供商注册信息
- 认证流程状态
- 错误信息和调试信息

## 扩展自定义提供商

如果需要支持其他 OAuth2 提供商，可以通过以下方式扩展：

### 1. 添加提供商模板

在 `service/oauth2_providers.go` 中添加新的提供商模板：

```go
"custom_provider": {
    AuthURL:     "https://custom.com/oauth/authorize",
    TokenURL:    "https://custom.com/oauth/token", 
    UserInfoURL: "https://custom.com/api/user",
    Scopes:      "read",
},
```

### 2. 添加用户信息解析器

```go
"custom_provider": func(data map[string]interface{}) (*OAuth2User, error) {
    // 解析用户信息的逻辑
    return &OAuth2User{
        ID:       data["id"].(string),
        Username: data["username"].(string),
        Email:    data["email"].(string),
    }, nil
},
```

### 3. 配置环境变量

```bash
OAUTH2_CUSTOM_PROVIDER_CLIENT_ID=your_client_id
OAUTH2_CUSTOM_PROVIDER_CLIENT_SECRET=your_client_secret
OAUTH2_CUSTOM_PROVIDER_ENABLED=true
```

## 更新日志

- **v0.4.1**: 添加通用 OAuth2 框架支持
- **v0.4.1**: 支持 Google、Microsoft、Discord、GitLab 提供商
- **v0.4.1**: 新增 OAuth2 配置管理界面
- **v0.4.1**: 改进用户数据存储结构
