{"version": 3, "sources": ["../src/symlog-scale.ts"], "names": [], "mappings": ";;;AAAA,6CAAyC;AAEzC,iDAA6C;AAC7C,iCAAmC;AACnC,qDAAmH;AACnH,yCAAqD;AACrD,qDAAgD;AAQhD,MAAa,WAAY,SAAQ,0BAAW;IAK1C;QACE,KAAK,CAAC,IAAA,cAAM,EAAC,CAAC,CAAC,EAAE,IAAA,cAAM,EAAC,CAAC,CAAC,CAAC,CAAC;QALrB,SAAI,GAAwB,gBAAS,CAAC,MAAM,CAAC;QAMpD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,KAAK;QACH,OAAO,IAAI,WAAW,EAAE;aACrB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;aAC1B,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;aACxB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;aACtB,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;aAC/B,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC;aACpC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAgB,CAAC;IAC1C,CAAC;IAID,QAAQ,CAAC,CAAU,EAAE,OAAiB;QACpC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;QAED,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,WAAW,GAAG,IAAA,cAAM,EAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAA,cAAM,EAAC,CAAC,CAAC,CAAC;QAE/B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO,CAAC,QAAgB,EAAE,EAAE,OAAkC;QAC5D,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1B,OAAO,IAAA,2BAAa,EAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAChG,CAAC;IAED,KAAK,CAAC,QAAgB,EAAE;QAEtB,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,OAAO,IAAA,gCAAkB,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7G,CAAC;IAMD,UAAU,CAAC,QAAgB,EAAE;QAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,OAAO,IAAA,qCAAuB,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACrG,CAAC;IAMD,SAAS,CAAC,IAAY;QACpB,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,OAAO,IAAA,qCAAuB,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACpG,CAAC;CACF;AAhED,kCAgEC;AAED,IAAA,cAAK,EAAC,WAAW,EAAE,6BAAY,CAAC,CAAC", "file": "symlog-scale.js", "sourcesContent": ["import { mixin } from '@visactor/vutils';\nimport type { ContinuousScaleType, NiceOptions, NiceType } from './interface';\nimport { LinearScale } from './linear-scale';\nimport { ScaleEnum } from './type';\nimport { d3TicksForLog, forceTicksBaseTransform, parseNiceOptions, ticksBaseTransform } from './utils/tick-sample';\nimport { symlog, symexp, nice } from './utils/utils';\nimport { LogNiceMixin } from './log-nice-mixin';\n\nexport interface SymlogScale extends LinearScale {\n  nice: (count?: number, option?: NiceOptions) => this;\n  niceMin: (count?: number) => this;\n  niceMax: (count?: number) => this;\n}\n\nexport class SymlogScale extends LinearScale {\n  readonly type: ContinuousScaleType = ScaleEnum.Symlog;\n\n  _const: number;\n\n  constructor() {\n    super(symlog(1), symexp(1));\n    this._const = 1;\n  }\n\n  clone(): SymlogScale {\n    return new SymlogScale()\n      .domain(this._domain, true)\n      .range(this._range, true)\n      .unknown(this._unknown)\n      .clamp(this.clamp(), null, true)\n      .interpolate(this._interpolate, true)\n      .constant(this._const) as SymlogScale;\n  }\n\n  constant(): number;\n  constant(_: number, slience?: boolean): this;\n  constant(_?: number, slience?: boolean): this | number {\n    if (!arguments.length) {\n      return this._const;\n    }\n\n    this._const = _;\n    this.transformer = symlog(_);\n    this.untransformer = symexp(_);\n\n    return this.rescale(slience);\n  }\n\n  d3Ticks(count: number = 10, options?: { noDecimals?: boolean }) {\n    const d = this.domain();\n    const u = d[0];\n    const v = d[d.length - 1];\n    return d3TicksForLog(u, v, count, this._const, this.transformer, this.untransformer, options);\n  }\n\n  ticks(count: number = 10) {\n    // return this.d3Ticks(count);\n    const d = this.calculateVisibleDomain(this._range);\n    return ticksBaseTransform(d[0], d[d.length - 1], count, this._const, this.transformer, this.untransformer);\n  }\n\n  /**\n   * 生成tick数组，这个tick数组的长度就是count的长度\n   * @param count\n   */\n  forceTicks(count: number = 10): any[] {\n    const d = this.calculateVisibleDomain(this._range);\n    return forceTicksBaseTransform(d[0], d[d.length - 1], count, this.transformer, this.untransformer);\n  }\n\n  /**\n   * 基于给定step的ticks数组生成\n   * @param step\n   */\n  stepTicks(step: number): any[] {\n    const d = this.calculateVisibleDomain(this._range);\n    return forceTicksBaseTransform(d[0], d[d.length - 1], step, this.transformer, this.untransformer);\n  }\n}\n\nmixin(SymlogScale, LogNiceMixin);\n"]}