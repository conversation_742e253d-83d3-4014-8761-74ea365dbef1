var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var IllustrationSuccessDark_exports = {};
__export(IllustrationSuccessDark_exports, {
  default: () => IllustrationSuccessDark_default
});
module.exports = __toCommonJS(IllustrationSuccessDark_exports);
var React = __toESM(require("react"));
function SvgComponent(props) {
  return /* @__PURE__ */ React.createElement("svg", __spreadValues({
    width: 200,
    height: 200,
    viewBox: "0 0 200 200",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    focusable: false,
    "aria-hidden": true
  }, props), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M170.46 42.4a11.6 11.6 0 0 0 8.44.6c6.1-1.9 9.53-8.31 7.66-14.32a11.48 11.48 0 0 0-14.43-7.45 11.53 11.53 0 0 0-8.15 11.55 10.16 10.16 0 0 0-6.55-.16 10.01 10.01 0 0 0-6.69 12.48 10.01 10.01 0 0 0 12.6 6.49c4.2-1.31 6.95-5.07 7.12-9.18Z",
    fill: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M26.17 40.77c1.79 2.07 3.58 5.92-1.38 11.02m1.38-1.1c.13-3.3.13-9.92 3.58-9.92 3.44 0 3.03 3.72 1.1 5.92a25.7 25.7 0 0 1-4.68 4Zm0 1.1c1.19.55 4.13 1.6 6.33 1.38 2.76-.28 4.55-2.48 3.17-4-1.38-1.51-7.3.28-9.5 2.62Z",
    stroke: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M96.11 9.96c3.17.83 4.15 9.8 1.38 16.95 5.39 0 10.4.03 13.03 1.67 2.61 1.64 2.44 5.84-2.01 7.47a23.44 23.44 0 0 1-4.57 1.2c1.27.09 2.8.28 4.33.62 1.31.3 2.6.96 2.79 2.04.18 1.09-.74 2.6-2.96 3.32-.9.3-1.8.52-**********.1 1.8.26 2.44.5 1.53.57 3.08 1.3 3.13 *********-.17 1.34-.54 1.9l1.15.65c7.89 4.42 16.22 9.1 19.17 16.91.8-.35 1.63-.7 2.5-1.04 17.58-6.98 44-7.68 52.92-4.22 8.9 3.46 10.08 15.06 6.38 28.6-3.69 13.53-9.43 22.2-10.25 23.2-.82 1-3.46 5.45-.47 9.08 2.99 3.64 3.85 4.62 5.2 5.87 1.35 1.26.77 2.44-.47 2.79-.97.27-2.68.26-4.86-**********.87 1.55.92 1.96.24 1.95-2.32 1.95-4.72.24a27.2 27.2 0 0 1-2.27-1.91s-.61 9-.61 18.47c0 8.4-3.7 23.06-11.29 31.63-6.18 7-15.01 10.03-25.13 9.13-22.5-2-31.93-21.59-32.47-48.9-9.34-1.81-17.57-6.99-25.17-12.34A536.1 536.1 0 0 1 59 112.2C54.07 110.42 17 89.52 13.73 86.44c-3.97 0-9.43-1.92-10.38-7.66-.96-5.74 3.57-29.3 12.18-44.14 2.34-4.03 5.15-5.85 5.65-**********.***********.63-.41 1.52-.4 2.02 3.15.17 1.18.3 2.89.46 4.78.32 3.81.69 8.36 1.45 10.77l.1.34c.46 1.53 1.04 3.48 5.58 7.04 11.22 8.8 33.41 23.25 38.48 26.55l.97.63c.73.48 3.86 2.21 7.97 4.5 8.83 4.89 22.2 12.29 25.98 15.15.15-.61.35-1.32.58-2.1 1.54-5.17 4.73-13.3 6.13-16.1-1.16-.7-29.27-18.61-45.45-28.92a10.61 10.61 0 0 1-2.64-1.68c-5.7-5.22-5.37-12.73-1.84-18.62 3.54-5.9 7.86-7.99 11.26-8.58 1.45-.25 3.03-.19 4.75-.13 2.31.09 4.87.18 7.68-.46 4.91-1.1 6.1-3 6.22-7.13l.02-.53c.14-4.6.3-9.97 5.08-8.73ZM145.8 154.3c2.76 3.18 3.59-4.6.48-13.9-2.35-7.04-3.5-8.6-3.8-8.38-.04 9.01.7 19.25 3.32 22.28Zm8.85-63.17a366.8 366.8 0 0 1 16.56-4.36c-2.64 4.2-5.2 9.3-7.35 14.36-1.84.23-13.46 3.16-14.33 3.72 2.75-6.7 4.83-12.73 5.12-13.72Z",
    fill: "#C6CACD"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M145.8 154.3c2.75 3.18 3.57-4.6.47-13.9-2.35-7.04-3.5-8.6-3.8-8.38-.04 9.01.7 19.25 3.32 22.28Z",
    fill: "#C6CACD"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M104.28 52.12c1-.07 2.1-.2 3.16-.44a4.8 4.8 0 0 0 3.05-1.9c8.2 4.61 17.23 9.35 20.32 17.55.8-.35 1.63-.7 2.5-1.04 13.67-5.42 32.68-7.06 44.66-5.89-11.38 9.21-14.73 21.94-14.42 28.28-3 .78-6.31 1.66-8.91 2.45-.29 1-2.37 7.02-5.12 13.72a110.77 110.77 0 0 1-4.76 10.2c-7.25-6.53-21.62-11.26-39.86-14.2 1.54-5.18 4.73-13.3 6.13-16.1-1.16-.71-29.27-18.62-45.45-28.93a10.61 10.61 0 0 1-2.64-1.68l2.64 1.68c8.76 4.13 29.2 4.48 36.97 2.92 6.64-1.34 6.03-4 4.89-5.28-.52-.59-1.8-1.04-3.16-1.34-2.35.16-4.23-.06-4.23-.33 0-.24 2.19-.12 4.23.33Z",
    fill: "#6B7075"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M62.94 54.14c6.22 5.69 30.9 6.35 39.6 4.6 6.64-1.34 6.04-4 4.9-5.28-1.3-1.47-7.4-2.06-7.4-1.67 0 .4 3.87.68 7.4-.11a4.8 4.8 0 0 0 3.05-1.9m-47.55 4.36c-5.7-5.22-5.37-12.73-1.84-18.62 3.54-5.9 7.86-7.99 11.26-8.58 3.4-.59 7.53.53 12.43-.59 4.91-1.1 6.1-3 6.22-7.13.15-4.7.13-10.54 5.1-9.26 3.17.83 4.15 9.8 1.38 16.95 5.39 0 10.4.03 13.03 1.67 2.61 1.64 2.44 5.84-2.01 7.47-4.45 1.64-7.57 1.44-7.6 1.25-.04-.18 3.7-.25 7.36.57 1.31.3 2.6.96 2.79 2.04.18 1.09-.74 2.6-2.96 3.32-3.98 1.3-7.86 1.25-7.86.86 0-.4 5.34-.53 7.66.33 1.53.57 3.08 1.3 3.13 *********-.17 1.34-.54 1.9m-47.55 4.37c15.52 9.89 46.87 29.86 48.1 30.6m0 0c-1.41 2.8-4.6 10.93-6.14 16.1-.23.78-.43 1.49-.58 2.1m6.71-18.2c1.2-2.38 4.36-10.6 19.78-17.4m43.5 62.2c-2.14-2.04-4.23-4.44-4.75-7.11-.85-4.35 3.7 4.51 4.75 7.1Zm0 0c1 2.44 1.23 3.78.65 4.03-.58.25-6.93-1.44-10.46-8.69-.96-2.23-1.3-4.93-.95-5.02.34-.08 2.8 6 3.87 12.16.98 5.67-9.46-4.97-9.01-12.29.18-3.06 1.3-7.44 2.98-12.25m12.92 22.06 1.38 1.29m-4.5-44.06c3.35-.78 4.34-1.2 5.62-2.12 3.01-2.17 1.74-5.64-1.69-2.85-1.1.9-2.37 2.5-3.92 4.97Zm0 0a366.8 366.8 0 0 0-16.55 4.36m16.56-4.36c-2.64 4.2-5.2 9.3-7.35 14.36m-13.36-8.56c.75-.37 2.26-.88 4.15-1.44m0 0c-.3 1-2.37 7.02-5.12 13.72m-135.8-18.4c3.29 3.07 40.35 23.97 45.27 25.75a537.02 537.02 0 0 0 22.03 16.62c7.6 5.35 15.83 10.53 25.17 12.33m-92.46-54.7c-3.97 0-9.43-1.93-10.38-7.67-.96-5.74 3.57-29.3 12.18-44.14 2.34-4.03 5.15-5.85 5.65-**********.***********m-7.58 56.41a21.48 21.48 0 0 1-4.78-7.6c-.3-1.24-.08-1.68.9-2.44 4.48-3.48 6.3-5.57 6.3-10.62 0-4.46-3.04-6-2.5-11.56.72-7.59 6.09-19.59 6.78-21.04.52-1.12.82-2.34.88-3.15m0 0c.63-.41 1.52-.4 2.02 3.15s.77 11.94 1.9 15.55c.5 1.57.8 3.55 5.69 7.38 12.33 9.67 37.9 26.15 39.45 27.18 2.27 1.5 28.4 15.45 33.95 19.65m0 0c3.53 2.66 5.22 3.64 5.22 5.6 0 1.22-2.33 2.74-4.7.36-1.36-1.35-1.05-3.7-.52-5.96Zm6.17-53.16c8.2 4.61 17.23 9.35 20.32 17.55m0 0c.8-.35 1.63-.7 2.5-1.04 17.58-6.98 44-7.68 52.92-4.22 8.9 3.46 10.08 15.06 6.38 28.6-3.69 13.53-9.43 22.2-10.25 23.2-.82 1-3.46 5.45-.47 9.08 2.99 3.64 3.85 4.62 5.2 5.87 1.35 1.26.77 2.44-.47 2.79-1.24.34-3.7.23-6.78-2.52-3.07-2.76-4.8-7.2-4.63-7.28.18-.06 7.22 8.74 7.47 10.69.24 1.95-2.32 1.95-4.72.24a27.2 27.2 0 0 1-2.27-1.91m-69.5 10.32a36 36 0 0 0 11.06.44c14.12-1.64 20.86-14.07 26.12-23.52.28-.5.57-1.04.85-1.59m-38.03 24.67c.54 27.32 9.97 46.91 32.47 48.91 10.12.9 18.95-2.14 25.13-9.13 7.59-8.57 11.29-23.22 11.29-31.63 0-9.47.61-18.47.61-18.47m-31.47-14.35-.14.72m.14-.72c1.84-3.52 3.69-7.69 5.3-11.63m-7.06 27.17c-.03 9.01.71 19.25 3.33 22.28 2.76 3.18 3.59-4.6.48-13.9-2.35-7.04-3.5-8.6-3.8-8.38Zm0 0c.19-5.3.77-10.57 1.62-14.82m0 0c3.64.88 5.75.45 7.86-1.14 2.1-1.6 4.57-4.72 6.06-6.12 1.5-1.4 2.78-2.26 3.4-2.46m0 0c.72-2.06 1.55-4.2 2.45-6.35m0 0c-1.84.23-13.46 3.16-14.33 3.72",
    stroke: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M107.7 141.58c11.58 2.2 23.7-.77 34.44-4.96l1.52-18.57c-2.27 4.02-9.7 14.83-14.19 18.57-5.78 4.83-15.7 5.08-21.76 4.96Z",
    fill: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M133.43 66.5a48.25 48.25 0 0 1 7.96-2.85c3.56-.81 5.64-1.41 10.14-1.97-1.6 1.77-4.25 4.63-6.9 6.09-6.82 3.74-10.45 2.06-11.2-1.26Z",
    fill: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M76.58 62.68c4.67.55 10.12-.63 13.08-3.17-5.37.97-15.95-.82-21.9-2.34l8.82 5.51Z",
    fill: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("ellipse", {
    cx: 44.8989,
    cy: 153.728,
    rx: 35.124,
    ry: 35.1239,
    fill: "var(--semi-color-primary)",
    fillOpacity: 0.2
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M63.16 138.9a4.4 4.4 0 0 1 1.1 6.13l-15.99 23a6.61 6.61 0 0 1-9.88 1.11L26.26 158.1a4.4 4.4 0 1 1 5.93-6.52l10.28 9.36L57.03 140a4.4 4.4 0 0 1 6.13-1.1Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M75.32 157.1c4.87-3.16 13.39-2.32 17.28 1.55 3.88 3.88-.77 7.16-3.08 3.63-2.32-3.53-1.9-9.27 1.25-12.78",
    stroke: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M142.16 63.95a9.24 9.24 0 1 1 0-18.5 9.24 9.24 0 0 1 0 18.5Z",
    fill: "#C6CACD",
    stroke: "#1C1F23",
    strokeMiterlimit: 10
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M150.33 50.4a8.93 8.93 0 0 0-8.17-4.94c-1.94 0-3.44.43-4.94 1.29l.45.12c-.4.25-.8.53-1.17.84l14.32 4.5c-.21-.64-.48-1.28-.77-1.89l.28.08Z",
    fill: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M137.9 51.18a.91.91 0 1 0 **********.91 0 0 0-1.83-.04Zm6.68 1.52a.91.91 0 1 0 **********.91 0 0 0-1.82-.04Z",
    fill: "#1C1F23"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M151.51 58.26a2.34 2.34 0 1 1 0-4.68 2.34 2.34 0 0 1 0 4.68Z",
    fill: "#C6CACD",
    stroke: "#1C1F23",
    strokeMiterlimit: 10
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M109.48 88.9c-1.81.25-2.19.95-2.28 1.2a1.4 1.4 0 0 0-.11.64c.***********.**********.22.33.35.44l.13.11-.07-.03-1.86 5.19a6.67 6.67 0 0 1-3.4-3.1 6.79 6.79 0 0 1-.32-5.84c.99-2.45 3.31-4.14 6.65-4.6 3.22-.44 7.51.21 13.14 2.3 6.53 2.42 11.84 6.32 16.71 10.4 1.57 1.3 3.07 2.61 4.55 3.9 3.22 2.8 6.33 5.52 9.78 7.96 4.93 3.47 10.3 6.18 16.96 7.25 6.67 1.08 14.85.54 25.42-2.8l1.67 5.25c-11.2 3.54-20.26 4.23-27.97 3-7.71-1.25-13.86-4.4-19.25-8.2-3.7-2.6-7.16-5.63-10.49-8.52a274.53 274.53 0 0 0-4.2-3.62c-4.72-3.94-9.45-7.36-15.09-9.45-5.24-1.95-8.55-2.27-10.48-2Z",
    fill: "#1C1F23"
  }));
}
var IllustrationSuccessDark_default = SvgComponent;
