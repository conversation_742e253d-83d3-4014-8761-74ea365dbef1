{"author": "<PERSON><PERSON> <PERSON> <<EMAIL>> (http://2x.io)", "name": "sse", "version": "0.0.8", "keywords": ["real-time", "server-sent-events", "html5", "sse", "messaging"], "repository": {"type": "git", "url": "git://github.com/einaros/sse.js.git"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "node_modules/.bin/mocha"}, "dependencies": {"options": "0.0.6"}, "devDependencies": {"mocha": "4.0.1", "expect.js": "0.3.1"}, "optionalDependencies": {}}