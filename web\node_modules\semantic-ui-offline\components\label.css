/*!
 * # Semantic UI 2.5.0 - Label
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Label
*******************************/

.ui.label {
  display: inline-block;
  line-height: 1;
  vertical-align: baseline;
  margin: 0em 0.14285714em;
  background-color: #E8E8E8;
  background-image: none;
  padding: 0.5833em 0.833em;
  color: rgba(0, 0, 0, 0.6);
  text-transform: none;
  font-weight: bold;
  border: 0px solid transparent;
  border-radius: 0.28571429rem;
  transition: background 0.1s ease;
}
.ui.label:first-child {
  margin-left: 0em;
}
.ui.label:last-child {
  margin-right: 0em;
}

/* Link */
a.ui.label {
  cursor: pointer;
}

/* Inside Link */
.ui.label > a {
  cursor: pointer;
  color: inherit;
  opacity: 0.5;
  transition: 0.1s opacity ease;
}
.ui.label > a:hover {
  opacity: 1;
}

/* Image */
.ui.label > img {
  width: auto !important;
  vertical-align: middle;
  height: 2.1666em !important;
}

/* Icon */
.ui.label > .icon {
  width: auto;
  margin: 0em 0.75em 0em 0em;
}

/* Detail */
.ui.label > .detail {
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
  margin-left: 1em;
  opacity: 0.8;
}
.ui.label > .detail .icon {
  margin: 0em 0.25em 0em 0em;
}

/* Removable label */
.ui.label > .close.icon,
.ui.label > .delete.icon {
  cursor: pointer;
  margin-right: 0em;
  margin-left: 0.5em;
  font-size: 0.92857143em;
  opacity: 0.5;
  transition: background 0.1s ease;
}
.ui.label > .delete.icon:hover {
  opacity: 1;
}

/*-------------------
       Group
--------------------*/

.ui.labels > .label {
  margin: 0em 0.5em 0.5em 0em;
}

/*-------------------
       Coupling
--------------------*/

.ui.header > .ui.label {
  margin-top: -0.29165em;
}

/* Remove border radius on attached segment */
.ui.attached.segment > .ui.top.left.attached.label,
.ui.bottom.attached.segment > .ui.top.left.attached.label {
  border-top-left-radius: 0;
}
.ui.attached.segment > .ui.top.right.attached.label,
.ui.bottom.attached.segment > .ui.top.right.attached.label {
  border-top-right-radius: 0;
}
.ui.top.attached.segment > .ui.bottom.left.attached.label {
  border-bottom-left-radius: 0;
}
.ui.top.attached.segment > .ui.bottom.right.attached.label {
  border-bottom-right-radius: 0;
}

/* Padding on next content after a label */
.ui.top.attached.label:first-child + :not(.attached),
.ui.top.attached.label + [class*="right floated"] + * {
  margin-top: 2rem !important;
}
.ui.bottom.attached.label:first-child ~ :last-child:not(.attached) {
  margin-top: 0em;
  margin-bottom: 2rem !important;
}


/*******************************
             Types
*******************************/

.ui.image.label {
  width: auto !important;
  margin-top: 0em;
  margin-bottom: 0em;
  max-width: 9999px;
  vertical-align: baseline;
  text-transform: none;
  background: #E8E8E8;
  padding: 0.5833em 0.833em 0.5833em 0.5em;
  border-radius: 0.28571429rem;
  box-shadow: none;
}
.ui.image.label img {
  display: inline-block;
  vertical-align: top;
  height: 2.1666em;
  margin: -0.5833em 0.5em -0.5833em -0.5em;
  border-radius: 0.28571429rem 0em 0em 0.28571429rem;
}
.ui.image.label .detail {
  background: rgba(0, 0, 0, 0.1);
  margin: -0.5833em -0.833em -0.5833em 0.5em;
  padding: 0.5833em 0.833em;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
}

/*-------------------
         Tag
--------------------*/

.ui.tag.labels .label,
.ui.tag.label {
  margin-left: 1em;
  position: relative;
  padding-left: 1.5em;
  padding-right: 1.5em;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
  transition: none;
}
.ui.tag.labels .label:before,
.ui.tag.label:before {
  position: absolute;
  transform: translateY(-50%) translateX(50%) rotate(-45deg);
  top: 50%;
  right: 100%;
  content: '';
  background-color: inherit;
  background-image: none;
  width: 1.56em;
  height: 1.56em;
  transition: none;
}
.ui.tag.labels .label:after,
.ui.tag.label:after {
  position: absolute;
  content: '';
  top: 50%;
  left: -0.25em;
  margin-top: -0.25em;
  background-color: #FFFFFF !important;
  width: 0.5em;
  height: 0.5em;
  box-shadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.3);
  border-radius: 500rem;
}

/*-------------------
    Corner Label
--------------------*/

.ui.corner.label {
  position: absolute;
  top: 0em;
  right: 0em;
  margin: 0em;
  padding: 0em;
  text-align: center;
  border-color: #E8E8E8;
  width: 4em;
  height: 4em;
  z-index: 1;
  transition: border-color 0.1s ease;
}

/* Icon Label */
.ui.corner.label {
  background-color: transparent !important;
}
.ui.corner.label:after {
  position: absolute;
  content: "";
  right: 0em;
  top: 0em;
  z-index: -1;
  width: 0em;
  height: 0em;
  background-color: transparent !important;
  border-top: 0em solid transparent;
  border-right: 4em solid transparent;
  border-bottom: 4em solid transparent;
  border-left: 0em solid transparent;
  border-right-color: inherit;
  transition: border-color 0.1s ease;
}
.ui.corner.label .icon {
  cursor: default;
  position: relative;
  top: 0.64285714em;
  left: 0.78571429em;
  font-size: 1.14285714em;
  margin: 0em;
}

/* Left Corner */
.ui.left.corner.label,
.ui.left.corner.label:after {
  right: auto;
  left: 0em;
}
.ui.left.corner.label:after {
  border-top: 4em solid transparent;
  border-right: 4em solid transparent;
  border-bottom: 0em solid transparent;
  border-left: 0em solid transparent;
  border-top-color: inherit;
}
.ui.left.corner.label .icon {
  left: -0.78571429em;
}

/* Segment */
.ui.segment > .ui.corner.label {
  top: -1px;
  right: -1px;
}
.ui.segment > .ui.left.corner.label {
  right: auto;
  left: -1px;
}

/*-------------------
       Ribbon
--------------------*/

.ui.ribbon.label {
  position: relative;
  margin: 0em;
  min-width: -webkit-max-content;
  min-width: -moz-max-content;
  min-width: max-content;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
  border-color: rgba(0, 0, 0, 0.15);
}
.ui.ribbon.label:after {
  position: absolute;
  content: '';
  top: 100%;
  left: 0%;
  background-color: transparent !important;
  border-style: solid;
  border-width: 0em 1.2em 1.2em 0em;
  border-color: transparent;
  border-right-color: inherit;
  width: 0em;
  height: 0em;
}

/* Positioning */
.ui.ribbon.label {
  left: calc(-1rem - 1.2em);
  margin-right: -1.2em;
  padding-left: calc(1rem + 1.2em);
  padding-right: 1.2em;
}
.ui[class*="right ribbon"].label {
  left: calc(100% + 1rem + 1.2em);
  padding-left: 1.2em;
  padding-right: calc(1rem + 1.2em);
}

/* Right Ribbon */
.ui[class*="right ribbon"].label {
  text-align: left;
  transform: translateX(-100%);
  border-radius: 0.28571429rem 0em 0em 0.28571429rem;
}
.ui[class*="right ribbon"].label:after {
  left: auto;
  right: 0%;
  border-style: solid;
  border-width: 1.2em 1.2em 0em 0em;
  border-color: transparent;
  border-top-color: inherit;
}

/* Inside Table */
.ui.image > .ribbon.label,
.ui.card .image > .ribbon.label {
  position: absolute;
  top: 1rem;
}
.ui.card .image > .ui.ribbon.label,
.ui.image > .ui.ribbon.label {
  left: calc(--0.05rem - 1.2em);
}
.ui.card .image > .ui[class*="right ribbon"].label,
.ui.image > .ui[class*="right ribbon"].label {
  left: calc(100% + -0.05rem + 1.2em);
  padding-left: 0.833em;
}

/* Inside Table */
.ui.table td > .ui.ribbon.label {
  left: calc(-0.78571429em - 1.2em);
}
.ui.table td > .ui[class*="right ribbon"].label {
  left: calc(100% + 0.78571429em + 1.2em);
  padding-left: 0.833em;
}

/*-------------------
      Attached
--------------------*/

.ui[class*="top attached"].label,
.ui.attached.label {
  width: 100%;
  position: absolute;
  margin: 0em;
  top: 0em;
  left: 0em;
  padding: 0.75em 1em;
  border-radius: 0.21428571rem 0.21428571rem 0em 0em;
}
.ui[class*="bottom attached"].label {
  top: auto;
  bottom: 0em;
  border-radius: 0em 0em 0.21428571rem 0.21428571rem;
}
.ui[class*="top left attached"].label {
  width: auto;
  margin-top: 0em !important;
  border-radius: 0.21428571rem 0em 0.28571429rem 0em;
}
.ui[class*="top right attached"].label {
  width: auto;
  left: auto;
  right: 0em;
  border-radius: 0em 0.21428571rem 0em 0.28571429rem;
}
.ui[class*="bottom left attached"].label {
  width: auto;
  top: auto;
  bottom: 0em;
  border-radius: 0em 0.28571429rem 0em 0.21428571rem;
}
.ui[class*="bottom right attached"].label {
  top: auto;
  bottom: 0em;
  left: auto;
  right: 0em;
  width: auto;
  border-radius: 0.28571429rem 0em 0.21428571rem 0em;
}


/*******************************
             States
*******************************/


/*-------------------
      Disabled
--------------------*/

.ui.label.disabled {
  opacity: 0.5;
}

/*-------------------
        Hover
--------------------*/

a.ui.labels .label:hover,
a.ui.label:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  background-image: none;
  color: rgba(0, 0, 0, 0.8);
}
.ui.labels a.label:hover:before,
a.ui.label:hover:before {
  color: rgba(0, 0, 0, 0.8);
}

/*-------------------
        Active
--------------------*/

.ui.active.label {
  background-color: #D0D0D0;
  border-color: #D0D0D0;
  background-image: none;
  color: rgba(0, 0, 0, 0.95);
}
.ui.active.label:before {
  background-color: #D0D0D0;
  background-image: none;
  color: rgba(0, 0, 0, 0.95);
}

/*-------------------
     Active Hover
--------------------*/

a.ui.labels .active.label:hover,
a.ui.active.label:hover {
  background-color: #C8C8C8;
  border-color: #C8C8C8;
  background-image: none;
  color: rgba(0, 0, 0, 0.95);
}
.ui.labels a.active.label:ActiveHover:before,
a.ui.active.label:ActiveHover:before {
  background-color: #C8C8C8;
  background-image: none;
  color: rgba(0, 0, 0, 0.95);
}

/*-------------------
      Visible
--------------------*/

.ui.labels.visible .label,
.ui.label.visible:not(.dropdown) {
  display: inline-block !important;
}

/*-------------------
      Hidden
--------------------*/

.ui.labels.hidden .label,
.ui.label.hidden {
  display: none !important;
}


/*******************************
           Variations
*******************************/


/*-------------------
       Colors
--------------------*/


/*--- Red ---*/

.ui.red.labels .label,
.ui.red.label {
  background-color: #DB2828 !important;
  border-color: #DB2828 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.red.labels .label:hover,
a.ui.red.label:hover {
  background-color: #d01919 !important;
  border-color: #d01919 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.red.corner.label,
.ui.red.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.red.ribbon.label {
  border-color: #b21e1e !important;
}

/* Basic */
.ui.basic.red.label {
  background: none #FFFFFF !important;
  color: #DB2828 !important;
  border-color: #DB2828 !important;
}
.ui.basic.red.labels a.label:hover,
a.ui.basic.red.label:hover {
  background-color: #FFFFFF !important;
  color: #d01919 !important;
  border-color: #d01919 !important;
}

/*--- Orange ---*/

.ui.orange.labels .label,
.ui.orange.label {
  background-color: #F2711C !important;
  border-color: #F2711C !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.orange.labels .label:hover,
a.ui.orange.label:hover {
  background-color: #f26202 !important;
  border-color: #f26202 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.orange.corner.label,
.ui.orange.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.orange.ribbon.label {
  border-color: #cf590c !important;
}

/* Basic */
.ui.basic.orange.label {
  background: none #FFFFFF !important;
  color: #F2711C !important;
  border-color: #F2711C !important;
}
.ui.basic.orange.labels a.label:hover,
a.ui.basic.orange.label:hover {
  background-color: #FFFFFF !important;
  color: #f26202 !important;
  border-color: #f26202 !important;
}

/*--- Yellow ---*/

.ui.yellow.labels .label,
.ui.yellow.label {
  background-color: #FBBD08 !important;
  border-color: #FBBD08 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.yellow.labels .label:hover,
a.ui.yellow.label:hover {
  background-color: #eaae00 !important;
  border-color: #eaae00 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.yellow.corner.label,
.ui.yellow.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.yellow.ribbon.label {
  border-color: #cd9903 !important;
}

/* Basic */
.ui.basic.yellow.label {
  background: none #FFFFFF !important;
  color: #FBBD08 !important;
  border-color: #FBBD08 !important;
}
.ui.basic.yellow.labels a.label:hover,
a.ui.basic.yellow.label:hover {
  background-color: #FFFFFF !important;
  color: #eaae00 !important;
  border-color: #eaae00 !important;
}

/*--- Olive ---*/

.ui.olive.labels .label,
.ui.olive.label {
  background-color: #B5CC18 !important;
  border-color: #B5CC18 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.olive.labels .label:hover,
a.ui.olive.label:hover {
  background-color: #a7bd0d !important;
  border-color: #a7bd0d !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.olive.corner.label,
.ui.olive.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.olive.ribbon.label {
  border-color: #198f35 !important;
}

/* Basic */
.ui.basic.olive.label {
  background: none #FFFFFF !important;
  color: #B5CC18 !important;
  border-color: #B5CC18 !important;
}
.ui.basic.olive.labels a.label:hover,
a.ui.basic.olive.label:hover {
  background-color: #FFFFFF !important;
  color: #a7bd0d !important;
  border-color: #a7bd0d !important;
}

/*--- Green ---*/

.ui.green.labels .label,
.ui.green.label {
  background-color: #21BA45 !important;
  border-color: #21BA45 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.green.labels .label:hover,
a.ui.green.label:hover {
  background-color: #16ab39 !important;
  border-color: #16ab39 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.green.corner.label,
.ui.green.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.green.ribbon.label {
  border-color: #198f35 !important;
}

/* Basic */
.ui.basic.green.label {
  background: none #FFFFFF !important;
  color: #21BA45 !important;
  border-color: #21BA45 !important;
}
.ui.basic.green.labels a.label:hover,
a.ui.basic.green.label:hover {
  background-color: #FFFFFF !important;
  color: #16ab39 !important;
  border-color: #16ab39 !important;
}

/*--- Teal ---*/

.ui.teal.labels .label,
.ui.teal.label {
  background-color: #00B5AD !important;
  border-color: #00B5AD !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.teal.labels .label:hover,
a.ui.teal.label:hover {
  background-color: #009c95 !important;
  border-color: #009c95 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.teal.corner.label,
.ui.teal.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.teal.ribbon.label {
  border-color: #00827c !important;
}

/* Basic */
.ui.basic.teal.label {
  background: none #FFFFFF !important;
  color: #00B5AD !important;
  border-color: #00B5AD !important;
}
.ui.basic.teal.labels a.label:hover,
a.ui.basic.teal.label:hover {
  background-color: #FFFFFF !important;
  color: #009c95 !important;
  border-color: #009c95 !important;
}

/*--- Blue ---*/

.ui.blue.labels .label,
.ui.blue.label {
  background-color: #2185D0 !important;
  border-color: #2185D0 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.blue.labels .label:hover,
a.ui.blue.label:hover {
  background-color: #1678c2 !important;
  border-color: #1678c2 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.blue.corner.label,
.ui.blue.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.blue.ribbon.label {
  border-color: #1a69a4 !important;
}

/* Basic */
.ui.basic.blue.label {
  background: none #FFFFFF !important;
  color: #2185D0 !important;
  border-color: #2185D0 !important;
}
.ui.basic.blue.labels a.label:hover,
a.ui.basic.blue.label:hover {
  background-color: #FFFFFF !important;
  color: #1678c2 !important;
  border-color: #1678c2 !important;
}

/*--- Violet ---*/

.ui.violet.labels .label,
.ui.violet.label {
  background-color: #6435C9 !important;
  border-color: #6435C9 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.violet.labels .label:hover,
a.ui.violet.label:hover {
  background-color: #5829bb !important;
  border-color: #5829bb !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.violet.corner.label,
.ui.violet.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.violet.ribbon.label {
  border-color: #502aa1 !important;
}

/* Basic */
.ui.basic.violet.label {
  background: none #FFFFFF !important;
  color: #6435C9 !important;
  border-color: #6435C9 !important;
}
.ui.basic.violet.labels a.label:hover,
a.ui.basic.violet.label:hover {
  background-color: #FFFFFF !important;
  color: #5829bb !important;
  border-color: #5829bb !important;
}

/*--- Purple ---*/

.ui.purple.labels .label,
.ui.purple.label {
  background-color: #A333C8 !important;
  border-color: #A333C8 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.purple.labels .label:hover,
a.ui.purple.label:hover {
  background-color: #9627ba !important;
  border-color: #9627ba !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.purple.corner.label,
.ui.purple.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.purple.ribbon.label {
  border-color: #82299f !important;
}

/* Basic */
.ui.basic.purple.label {
  background: none #FFFFFF !important;
  color: #A333C8 !important;
  border-color: #A333C8 !important;
}
.ui.basic.purple.labels a.label:hover,
a.ui.basic.purple.label:hover {
  background-color: #FFFFFF !important;
  color: #9627ba !important;
  border-color: #9627ba !important;
}

/*--- Pink ---*/

.ui.pink.labels .label,
.ui.pink.label {
  background-color: #E03997 !important;
  border-color: #E03997 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.pink.labels .label:hover,
a.ui.pink.label:hover {
  background-color: #e61a8d !important;
  border-color: #e61a8d !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.pink.corner.label,
.ui.pink.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.pink.ribbon.label {
  border-color: #c71f7e !important;
}

/* Basic */
.ui.basic.pink.label {
  background: none #FFFFFF !important;
  color: #E03997 !important;
  border-color: #E03997 !important;
}
.ui.basic.pink.labels a.label:hover,
a.ui.basic.pink.label:hover {
  background-color: #FFFFFF !important;
  color: #e61a8d !important;
  border-color: #e61a8d !important;
}

/*--- Brown ---*/

.ui.brown.labels .label,
.ui.brown.label {
  background-color: #A5673F !important;
  border-color: #A5673F !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.brown.labels .label:hover,
a.ui.brown.label:hover {
  background-color: #975b33 !important;
  border-color: #975b33 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.brown.corner.label,
.ui.brown.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.brown.ribbon.label {
  border-color: #805031 !important;
}

/* Basic */
.ui.basic.brown.label {
  background: none #FFFFFF !important;
  color: #A5673F !important;
  border-color: #A5673F !important;
}
.ui.basic.brown.labels a.label:hover,
a.ui.basic.brown.label:hover {
  background-color: #FFFFFF !important;
  color: #975b33 !important;
  border-color: #975b33 !important;
}

/*--- Grey ---*/

.ui.grey.labels .label,
.ui.grey.label {
  background-color: #767676 !important;
  border-color: #767676 !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.grey.labels .label:hover,
a.ui.grey.label:hover {
  background-color: #838383 !important;
  border-color: #838383 !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.grey.corner.label,
.ui.grey.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.grey.ribbon.label {
  border-color: #805031 !important;
}

/* Basic */
.ui.basic.grey.label {
  background: none #FFFFFF !important;
  color: #767676 !important;
  border-color: #767676 !important;
}
.ui.basic.grey.labels a.label:hover,
a.ui.basic.grey.label:hover {
  background-color: #FFFFFF !important;
  color: #838383 !important;
  border-color: #838383 !important;
}

/*--- Black ---*/

.ui.black.labels .label,
.ui.black.label {
  background-color: #1B1C1D !important;
  border-color: #1B1C1D !important;
  color: #FFFFFF !important;
}

/* Link */
.ui.black.labels .label:hover,
a.ui.black.label:hover {
  background-color: #27292a !important;
  border-color: #27292a !important;
  color: #FFFFFF !important;
}

/* Corner */
.ui.black.corner.label,
.ui.black.corner.label:hover {
  background-color: transparent !important;
}

/* Ribbon */
.ui.black.ribbon.label {
  border-color: #805031 !important;
}

/* Basic */
.ui.basic.black.label {
  background: none #FFFFFF !important;
  color: #1B1C1D !important;
  border-color: #1B1C1D !important;
}
.ui.basic.black.labels a.label:hover,
a.ui.basic.black.label:hover {
  background-color: #FFFFFF !important;
  color: #27292a !important;
  border-color: #27292a !important;
}

/*-------------------
        Basic
--------------------*/

.ui.basic.label {
  background: none #FFFFFF;
  border: 1px solid rgba(34, 36, 38, 0.15);
  color: rgba(0, 0, 0, 0.87);
  box-shadow: none;
}

/* Link */
a.ui.basic.label:hover {
  text-decoration: none;
  background: none #FFFFFF;
  color: #1e70bf;
  box-shadow: 1px solid rgba(34, 36, 38, 0.15);
  box-shadow: none;
}

/* Pointing */
.ui.basic.pointing.label:before {
  border-color: inherit;
}

/*-------------------
       Fluid
--------------------*/

.ui.label.fluid,
.ui.fluid.labels > .label {
  width: 100%;
  box-sizing: border-box;
}

/*-------------------
       Inverted
--------------------*/

.ui.inverted.labels .label,
.ui.inverted.label {
  color: rgba(255, 255, 255, 0.9) !important;
}

/*-------------------
     Horizontal
--------------------*/

.ui.horizontal.labels .label,
.ui.horizontal.label {
  margin: 0em 0.5em 0em 0em;
  padding: 0.4em 0.833em;
  min-width: 3em;
  text-align: center;
}

/*-------------------
       Circular
--------------------*/

.ui.circular.labels .label,
.ui.circular.label {
  min-width: 2em;
  min-height: 2em;
  padding: 0.5em !important;
  line-height: 1em;
  text-align: center;
  border-radius: 500rem;
}
.ui.empty.circular.labels .label,
.ui.empty.circular.label {
  min-width: 0em;
  min-height: 0em;
  overflow: hidden;
  width: 0.5em;
  height: 0.5em;
  vertical-align: baseline;
}

/*-------------------
       Pointing
--------------------*/

.ui.pointing.label {
  position: relative;
}
.ui.attached.pointing.label {
  position: absolute;
}
.ui.pointing.label:before {
  background-color: inherit;
  background-image: inherit;
  border-width: none;
  border-style: solid;
  border-color: inherit;
}

/* Arrow */
.ui.pointing.label:before {
  position: absolute;
  content: '';
  transform: rotate(45deg);
  background-image: none;
  z-index: 2;
  width: 0.6666em;
  height: 0.6666em;
  transition: background 0.1s ease;
}

/*--- Above ---*/

.ui.pointing.label,
.ui[class*="pointing above"].label {
  margin-top: 1em;
}
.ui.pointing.label:before,
.ui[class*="pointing above"].label:before {
  border-width: 1px 0px 0px 1px;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  top: 0%;
  left: 50%;
}

/*--- Below ---*/

.ui[class*="bottom pointing"].label,
.ui[class*="pointing below"].label {
  margin-top: 0em;
  margin-bottom: 1em;
}
.ui[class*="bottom pointing"].label:before,
.ui[class*="pointing below"].label:before {
  border-width: 0px 1px 1px 0px;
  top: auto;
  right: auto;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  top: 100%;
  left: 50%;
}

/*--- Left ---*/

.ui[class*="left pointing"].label {
  margin-top: 0em;
  margin-left: 0.6666em;
}
.ui[class*="left pointing"].label:before {
  border-width: 0px 0px 1px 1px;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  bottom: auto;
  right: auto;
  top: 50%;
  left: 0em;
}

/*--- Right ---*/

.ui[class*="right pointing"].label {
  margin-top: 0em;
  margin-right: 0.6666em;
}
.ui[class*="right pointing"].label:before {
  border-width: 1px 1px 0px 0px;
  transform: translateX(50%) translateY(-50%) rotate(45deg);
  top: 50%;
  right: 0%;
  bottom: auto;
  left: auto;
}

/* Basic Pointing */

/*--- Above ---*/

.ui.basic.pointing.label:before,
.ui.basic[class*="pointing above"].label:before {
  margin-top: -1px;
}

/*--- Below ---*/

.ui.basic[class*="bottom pointing"].label:before,
.ui.basic[class*="pointing below"].label:before {
  bottom: auto;
  top: 100%;
  margin-top: 1px;
}

/*--- Left ---*/

.ui.basic[class*="left pointing"].label:before {
  top: 50%;
  left: -1px;
}

/*--- Right ---*/

.ui.basic[class*="right pointing"].label:before {
  top: 50%;
  right: -1px;
}

/*------------------
   Floating Label
-------------------*/

.ui.floating.label {
  position: absolute;
  z-index: 100;
  top: -1em;
  left: 100%;
  margin: 0em 0em 0em -1.5em !important;
}

/*-------------------
        Sizes
--------------------*/

.ui.mini.labels .label,
.ui.mini.label {
  font-size: 0.64285714rem;
}
.ui.tiny.labels .label,
.ui.tiny.label {
  font-size: 0.71428571rem;
}
.ui.small.labels .label,
.ui.small.label {
  font-size: 0.78571429rem;
}
.ui.labels .label,
.ui.label {
  font-size: 0.85714286rem;
}
.ui.large.labels .label,
.ui.large.label {
  font-size: 1rem;
}
.ui.big.labels .label,
.ui.big.label {
  font-size: 1.28571429rem;
}
.ui.huge.labels .label,
.ui.huge.label {
  font-size: 1.42857143rem;
}
.ui.massive.labels .label,
.ui.massive.label {
  font-size: 1.71428571rem;
}


/*******************************
         Theme Overrides
*******************************/



/*******************************
         Site Overrides
*******************************/

