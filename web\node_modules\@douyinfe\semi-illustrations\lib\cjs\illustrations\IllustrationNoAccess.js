var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var IllustrationNoAccess_exports = {};
__export(IllustrationNoAccess_exports, {
  default: () => IllustrationNoAccess_default
});
module.exports = __toCommonJS(IllustrationNoAccess_exports);
var React = __toESM(require("react"));
function SvgComponent(props) {
  return /* @__PURE__ */ React.createElement("svg", __spreadValues({
    width: 200,
    height: 200,
    viewBox: "0 0 200 200",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    focusable: false,
    "aria-hidden": true
  }, props), /* @__PURE__ */ React.createElement("rect", {
    width: 200,
    height: 200,
    fill: "transparent"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M109.53 54.91a40.4 40.4 0 1 1 80.8 0v113.7h-80.8V54.92Z",
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M145.5 92.62a9.47 9.47 0 0 0 10.6-8.16l4.36-33.14a9.47 9.47 0 1 0-18.76-2.47l-4.36 33.14a9.47 9.47 0 0 0 8.15 10.63Zm7.52-57.39h.02a14.98 14.98 0 0 1 12.9 16.81l-4.36 33.14a14.98 14.98 0 0 1-16.8 12.9h-.02a14.98 14.98 0 0 1-12.9-16.8l4.36-33.15a14.98 14.98 0 0 1 16.8-12.9Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M135.9 60.08c-5.94-.68-11.3 3.42-11.96 9.16l-2.39 20.77c-.66 5.75 3.63 10.96 9.58 11.64l25.85 2.97c5.95.68 11.3-3.42 11.97-9.16l2.38-20.77c.66-5.75-3.63-10.96-9.58-11.64l-25.84-2.97Z",
    fill: "var(--semi-color-primary-light-default)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M148.62 80.9a4.67 4.67 0 1 0-3.29-.43l-4.17 7.63a1 1 0 0 0 .75 1.47l7.65 1.01a1 1 0 0 0 1.1-1.23l-2.04-8.45Z",
    fill: "var(--semi-color-primary)"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M180.37 95.24a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z",
    fill: "#E6E8EA"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M88.47 52.87h-.04l-.04.02c-.37.23-.83.47-1.36.74l-1.04.54c-.94.5-1.98 1.12-2.93 1.95-1.88 1.63-3.4 4.05-3.2 7.92-2.2.35-5.42 1.34-8.14 3.22-2.78 1.9-5.04 4.74-5.13 8.74v.05l.04.04c4.26 5.01 18.1 12.42 39.5 2.34l.08-.03v-.09c.02-1.7-.4-4.33-1.73-6.7a9.9 9.9 0 0 0-7-5.09c.38-2.17.4-5.4-.79-8.17a8.75 8.75 0 0 0-2.86-3.7 9.83 9.83 0 0 0-5.36-1.78Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M33.22 38.73C46.6 47.75 67.28 61.68 75.28 66.8",
    stroke: "#EE2F2F",
    strokeWidth: 3
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M112.1 185.6c-7.72 1.83-19.84 2.4-29.06 1.54-3.13-.24-9.65-1.04-10.76-2.36-1.38-1.64-3.68-6.76-3.94-11.91-.27-5.15 2.03-23.08 3.76-27.51-2.42-.68-8.08-3.12-12.12-5.25a289.8 289.8 0 0 1-11.32-6.28c.25 6.71.04 12.95-1.08 18.14-1.29 5.97-3.75 11.3-5.82 16.06-1.75 4.04-.35 6.49 3.27 9.59 2.57 2.2 6.89 2.95 9.22 ********* 1.51 1.23 1.53 **********-.87 1-2.12 1.4-1.53.5-3.59.87-5.18 1.06-9.37 1.1-29.55 1.64-35.97-1.73-.65-.34-1.68-1.01-.78-6.5 1.71-10.37 7.64-27.22 8.46-32.06 1.02-6.05-1.16-11.92-2.42-16.72-1.26-4.8-3.6-22.67 0-32.82.49-1.39 1.1-2.87 1.81-4.42a71.08 71.08 0 0 1 17.73-23.3c-10.33-7.37-23.14-18.28-24.1-28-.96-9.7 7.85-20.62 16.3-20.16 8.64.46 29.33 12.88 43.22 28.83 4.81-6.9 14.63-8.64 20.7-5.28 3.58 1.98.72 4.5-.5 5.26-.97.62-11.77 12.45-16.78 17.65 5.5 3.46 17.76 10 21.47 19.94 1.3 3.45 1.38 4.95 1.63 8.1.54 6.84 2.57 21.55 2.57 23.95 2.32-3.28 4.7-5.83 9.38-10.66 4.69-4.83 8.49-12.39 9.94-16.94 1.33-4.2 2-8.09 4.46-7.47 2.38.6 2.05 10.5 1.44 15.34 4.25-.33 15.6-.28 22.69 1.63 7.08 1.9 8.24 4.37 8.14 4.97-.1.6-1.36 2.5-10.2 2.2.3 1.02-.04 3.19-3.88 3.75 2.03 1.24 2.2 1.94 2.16 2.67-.03.73-.44 2-4.93 ********** 1.45 2.37-2.43 3.63-3.89 1.26-9.62.37-9.97.34-1.87 6.27-7.23 18.73-15.8 24.83-5.36 3.81-11.91 3.95-14.22 3.95.66 13.61.25 20.85 2.22 24.47 2.76 5.05 20.35 8.8 11.28 10.94Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "m78.15 68.61-2-1.23C69.45 63.17 58.55 55.8 48.49 49l-8.92-6.02c-7.1 5.64-8.43 14.8-8.89 21.08 2.23 1.8 4.48 3.48 6.63 5.01a71.08 71.08 0 0 0-17.73 23.3c-2.24 4.84-2.8 6.98-3.43 12 0 7.8 25.31 14.54 50.1 ********* 6.26 3.8 6.26 3.8l.23 5.49c11.84 0 24.1-5.82 27.4-11.12l-.15-1.33c-.27-2.4-.63-5.54-.74-6.98l-.02-.25c-.23-3-.35-4.5-1.6-7.86C94.3 78.45 84.2 72.29 78.14 68.6Zm-5.78 28.38-6.1 8.57 6.24 3.82c-.14-4-.14-7.65-.14-11.68V97Z",
    fill: "#E6E8EA"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M98.6 150.19c.66 13.61.25 20.85 2.22 24.47 2.76 5.05 20.35 8.8 11.28 10.94-7.72 1.83-19.84 2.4-29.06 1.54-3.13-.24-9.65-1.04-10.76-2.36-1.38-1.64-3.68-6.76-3.94-11.91-.27-5.15 2.03-23.08 3.76-27.51m26.5 4.83c2.3 0 8.86-.14 14.22-3.95 8.58-6.1 13.93-18.57 15.8-*********** 6.08.92 9.97-.34 3.88-1.26 3.05-2.69 2.43-3.63M98.6 150.19c-2.64 0-12.21-.41-19.36-10.08a30.3 30.3 0 0 1-4.4-8.95c-1.47-5.14-2.07-13.49-2.32-21.72m-.42 35.92c1.74-4.44 3.88-3.34 4.6-2.64.71.68 1.58 2.62 0 3.06-1.68.47-4.08-.28-4.6-.42Zm0 0c-2.42-.68-8.08-3.12-12.12-5.25a289.8 289.8 0 0 1-11.32-6.28M28.34 120.1c6.25 5.11 13.95 10 20.32 13.73m0 0c.25 6.71.04 12.95-1.08 18.14-1.29 5.97-3.75 11.3-5.82 16.06-1.75 4.04-.35 6.49 3.27 9.59 2.57 2.2 6.89 2.95 9.22 ********* 1.51 1.23 1.53 **********-.87 1-2.12 1.4m-5.9-123.86a65.01 65.01 0 0 0-10.45 7.37m16.35 116.5c-1.53.48-3.59.86-5.18 1.05-9.37 1.1-29.55 1.64-35.97-1.73-.65-.34-1.68-1.01-.78-6.5 1.71-10.37 7.64-27.22 8.46-32.06 1.02-6.05-1.16-11.92-2.42-16.72-1.26-4.8-3.6-22.67 0-32.82.49-1.39 1.1-2.87 1.81-4.42a71.08 71.08 0 0 1 17.73-23.3m16.35 116.5c.16-1.9-1.37-4.1-4.73-3.82M37.3 69.08c-10.33-7.37-23.14-18.28-24.1-28-.96-9.7 7.85-20.62 16.3-20.16 8.64.46 29.33 12.88 43.22 28.83 4.81-6.9 14.63-8.64 20.7-5.28 3.58 1.98.72 4.5-.5 5.26-.97.62-11.77 12.45-16.78 17.65m0 0C65.7 60.8 44.98 46.54 33.2 38.73m42.95 28.65c5.5 3.46 17.76 10 21.47 19.94 1.3 3.45 1.38 4.95 1.63 8.1.54 6.84 2.57 21.55 2.57 23.95m0 0c0 1.85.77 8.03-1.87 8.03-2.44 0-.4-4.93 1.87-8.03Zm0 0c2.32-3.28 4.7-5.83 9.38-10.66 4.69-4.83 8.49-12.39 9.94-16.94 1.33-4.2 2-8.09 4.46-7.47 2.38.6 2.05 10.5 1.44 15.34m0 0-2.66.26m2.66-.26c4.25-.33 15.6-.28 22.69 1.63 7.08 1.9 8.24 4.37 8.14 4.97-.1.6-1.36 2.5-10.2 2.2m0 0c-7.62-.25-10.66-1.56-10.76-2.11-.1-.55 2.82-1.1 5.38-.6 2.56.5 5.03 1.36 5.38 2.72Zm0 0c.3 1.02-.04 3.19-3.88 3.75m0 0c-4.8.7-5.63.28-5.67 0-.03-.29 3.98-.83 5.67 0Zm0 0c2.03 1.24 2.2 1.94 2.16 2.67-.03.73-.44 2-4.93 2.58m0 0c-4.49.57-4.59-.63-3.33-.88 1.26-.24 2.97.41 3.33.88ZM72.38 89.58 72.36 97m0 0-6.1 8.57 6.26 3.87M72.36 97c0 3.8.03 8.1.16 12.44",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M72.23 97.11v-7l-.3.33a12.64 12.64 0 0 0-2.98 10.24l.14.87 3.14-4.44Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M48.62 148.14c-.87-9.7-11.15-14.82-18.68-26.25 7.45 5.14 13.85 8.6 19.21 11.73 0 0-.08 9.49-.53 14.52Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M97.1 56a8.64 8.64 0 0 1-7.94 9.29c-4.8.42-9-3.1-9.41-7.79a8.64 8.64 0 0 1 7.94-9.29c4.8-.41 9 3.1 9.41 7.8Z",
    fill: "white",
    stroke: "#515151",
    strokeWidth: 3
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M91.3 54.15a.41.41 0 0 0-.58-.07 8.14 8.14 0 0 1-3 ********** 0 1 0 .16.8c.6-.11 2.09-.6 3.35-1.59.18-.14.21-.4.07-.58Zm-1.78 3.68a.81.81 0 1 1 1.57-.44.81.81 0 0 1-1.57.44Zm6.23-2.16a.81.81 0 1 0 .44 ********** 0 0 0-.44-1.56Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M76.82 48.05a5.32 5.32 0 1 0 1.37-10.55 5.32 5.32 0 0 0-1.37 10.55Zm10.68-1.4c-3.39.3-6.26 2.16-7.92 4.81l-1.06-.12c-.36-.04-.74-.1-1.16-.13a15.88 15.88 0 0 0-3.52-.03c-2.47.29-5.09 1.44-7.1 4.76-4.05-1.86-10.53-.9-15.08 5.52l-.05.07.03.07c4.04 8.83 11.93 18.8 28.78 21h.08l.05-.06c.97-1.4 2.09-3.81 2.32-6.51a9.9 9.9 0 0 0-2.93-8.14 15.3 15.3 0 0 0 3.93-7.22 8.75 8.75 0 0 0-.32-4.78c1.46-.01 3.22-.75 4.74-1.9a9.8 9.8 0 0 0 3.37-4.34 8.67 8.67 0 0 0 5.42 2.03h.25l-.13-.2a10.36 10.36 0 0 0-9.7-4.83Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("circle", {
    cx: 80.6886,
    cy: 61.9548,
    r: 6.1823,
    transform: "rotate(-4.93845 80.6886 61.9548)",
    stroke: "#C6CACD"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M174.02 106c-.5 1.8-2.7 5.73-7.47 7.05",
    stroke: "var(--semi-color-primary)",
    strokeLinecap: "round"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M181.21 110.24c-.87 2.88-4.54 9.14-12.27 11.13",
    stroke: "var(--semi-color-primary)",
    strokeLinecap: "round"
  }));
}
var IllustrationNoAccess_default = SvgComponent;
