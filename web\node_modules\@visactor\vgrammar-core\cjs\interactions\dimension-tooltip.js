"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.DimensionTooltip = void 0;

const vutils_1 = require("@visactor/vutils"), base_tooltip_1 = require("./base-tooltip"), base_tooltip_2 = require("./base-tooltip"), constants_1 = require("../graph/constants"), util_1 = require("../parse/util"), isEqualTooltipDatum = (current, previous) => {
    const currentDatum = (0, vutils_1.array)(current), previousDatum = (0, vutils_1.array)(previous);
    return currentDatum.length === previousDatum.length && (currentDatum.every((datum => previousDatum.includes(datum))) && previousDatum.every((datum => currentDatum.includes(datum))));
}, computeTooltipFilterValue = (point, scale, type, groupSize, tooltipCenter) => {
    if ("x" === type) return scale.invert(point.x);
    if ("y" === type) return scale.invert(point.y);
    if ("radius" === type) {
        const center = null != tooltipCenter ? tooltipCenter : {
            x: groupSize.width / 2,
            y: groupSize.height / 2
        }, radius = Math.sqrt((center.x - point.x) ** 2 + (center.y - point.y) ** 2);
        return scale.invert(radius);
    }
    if ("angle" === type) {
        const center = null != tooltipCenter ? tooltipCenter : {
            x: groupSize.width / 2,
            y: groupSize.height / 2
        }, angle = (0, vutils_1.getAngleByPoint)(center, point);
        return scale.invert(angle < 0 ? angle + 2 * Math.PI : angle);
    }
    return scale.invert(point.x);
};

class DimensionTooltip extends base_tooltip_1.BaseTooltip {
    constructor(view, options) {
        var _a, _b, _c;
        super(view, options), this.type = DimensionTooltip.type, this._avoidMarks = [], 
        this._tooltipDataFilter = null, this.handleTooltipShow = (0, vutils_1.throttle)((event => {
            var _a, _b, _c;
            if (!this._tooltipComponent) return;
            const scale = ((0, vutils_1.isString)(this.options.scale) ? this.view.getScaleById(this.options.scale) : this.options.scale).getScale(), groupGraphicItem = this._container.getGroupGraphicItem(), point = {
                x: 0,
                y: 0
            };
            groupGraphicItem.globalTransMatrix.transformPoint(event.canvas, point);
            const groupSize = {
                width: groupGraphicItem.attribute.width,
                height: groupGraphicItem.attribute.height
            };
            if (point.x < 0 || point.x > groupGraphicItem.attribute.width || point.y < 0 || point.y > groupGraphicItem.attribute.height) return void this._tooltipComponent.hideAll();
            const eventTargetMark = null === (_b = null === (_a = event.target) || void 0 === _a ? void 0 : _a[constants_1.BridgeElementKey]) || void 0 === _b ? void 0 : _b.mark;
            if (this._avoidMarks.includes(eventTargetMark)) return void this._tooltipComponent.hideAll();
            const target = null === (_c = this.options.target) || void 0 === _c ? void 0 : _c.data, lastDataGrammar = target ? (0, 
            vutils_1.isString)(target) ? this.view.getDataById(target) : target : null, data = lastDataGrammar ? lastDataGrammar.getValue() : [], filterValue = computeTooltipFilterValue(point, scale, this.options.tooltipType, groupSize, this.options.center), tooltipDatum = this._tooltipDataFilter ? data.filter((datum => this._tooltipDataFilter(datum, filterValue))) : [];
            if (this._tooltipComponent.showAll(), isEqualTooltipDatum(tooltipDatum, this._lastDatum)) return void this._tooltipComponent.setAttributes({
                pointerX: point.x,
                pointerY: point.y
            });
            this._lastDatum = tooltipDatum;
            const boundsStart = {
                x: 0,
                y: 0
            };
            groupGraphicItem.globalTransMatrix.transformPoint({
                x: 0,
                y: 0
            }, boundsStart);
            const boundsEnd = {
                x: 0,
                y: 0
            };
            groupGraphicItem.globalTransMatrix.transformPoint({
                x: this.view.getSignalById("width").getValue(),
                y: this.view.getSignalById("height").getValue()
            }, boundsEnd);
            const bounds = (new vutils_1.AABBBounds).set(boundsStart.x, boundsStart.y, boundsEnd.x, boundsEnd.y), {title: title, content: content} = this._computeTitleContent(tooltipDatum), theme = this.view.getCurrentTheme(), addition = (0, 
            util_1.invokeFunctionType)(this.options.attributes, this.parameters(), {}), attributes = (0, 
            base_tooltip_2.generateTooltipAttributes)(point, title, content, bounds, theme, addition);
            this._tooltipComponent.setAttributes(attributes);
        }), 10), this.handleTooltipHide = event => {
            this._tooltipComponent && this._tooltipComponent.hideAll();
        }, this.options = Object.assign({}, DimensionTooltip.defaultOptions, options), this._marks = view.getMarksBySelector(this.options.selector), 
        this._avoidMarks = null !== (_a = view.getMarksBySelector(this.options.avoidMark)) && void 0 !== _a ? _a : [], 
        this._container = null !== (_c = null === (_b = view.getMarksBySelector(this.options.container)) || void 0 === _b ? void 0 : _b[0]) && void 0 !== _c ? _c : view.rootMark;
    }
    getEvents() {
        const filter = this.options.target.filter;
        return this._tooltipDataFilter = (0, vutils_1.isString)(filter) ? (datum, filterValue) => filterValue === datum[filter] : filter, 
        [ {
            type: this.options.trigger,
            handler: this.handleTooltipShow
        }, {
            type: this.options.triggerOff,
            handler: this.handleTooltipHide
        } ];
    }
}

exports.DimensionTooltip = DimensionTooltip, DimensionTooltip.type = "dimension-tooltip", 
DimensionTooltip.defaultOptions = {
    trigger: "pointermove",
    triggerOff: "pointerleave"
};
//# sourceMappingURL=dimension-tooltip.js.map