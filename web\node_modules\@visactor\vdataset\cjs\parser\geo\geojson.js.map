{"version": 3, "sources": ["../src/parser/geo/geojson.ts"], "names": [], "mappings": ";;;;;;AAAA,mCAAiC;AACjC,+CAAgD;AAGhD,uCAAgD;AAChD,0DAAsC;AACtC,4DAAoC;AACpC,6CAA4C;AAE5C,MAAM,eAAe,GAAG,IAAA,gBAAO,GAAE,CAAC;AAYrB,QAAA,uBAAuB,GAAG;IACrC,QAAQ,EAAE,KAAK;IACf,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,KAAK;IACX,MAAM,EAAE,KAAK;CACd,CAAC;AACK,MAAM,aAAa,GAAG,CAAC,OAAY,EAAE,EAAE;IAC5C,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;QAC7C,MAAM,CAAC,GAAG,IAAA,iBAAO,EAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvC,uCAAY,CAAC,GAAK,CAAC,CAAC,UAAU,EAAG;KAClC;IACD,uCAAY,OAAO,GAAK,OAAO,CAAC,UAAU,EAAG;AAC/C,CAAC,CAAC;AANW,QAAA,aAAa,iBAMxB;AACK,MAAM,cAAc,GAAG,CAAC,IAAW,EAAE,EAAE;IAC5C,MAAM,WAAW,GAAU,EAAE,CAAC;IAC9B,IAAI,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;QACzB,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,EAAE;YAErC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,EAAE;gBACrC,WAAW,CAAC,IAAI,CAAC,IAAA,qBAAa,EAAC,OAAO,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;SACJ;aAAM;YAEL,WAAW,CAAC,IAAI,CAAC,IAAA,qBAAa,EAAC,IAAI,CAAC,CAAC,CAAC;SACvC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAdW,QAAA,cAAc,kBAczB;AASK,MAAM,aAAa,GAAW,CAAC,IAAS,EAAE,UAA2B,EAAE,EAAE,QAAkB,EAAE,EAAE;IACpG,QAAQ,CAAC,IAAI,GAAG,yBAAa,CAAC,GAAG,CAAC;IAElC,MAAM,YAAY,GAAoB,IAAA,mBAAc,EAAC,+BAAuB,EAAE,OAAO,CAAC,CAAC;IAEvF,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC;IACtD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACvB,OAAO,IAAA,sBAAc,EAAC,IAAI,CAAC,CAAC;KAC7B;IACD,IAAI,QAAQ,GAAU,IAAI,CAAC,QAAQ,CAAC;IACpC,IAAI,MAAM,EAAE;QACV,QAAQ,GAAG,IAAA,gBAAU,EAAC,IAAI,EAAE,EAAE,OAAO,EAAE,IAAA,iBAAQ,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC;KAC7F;IACD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,QAAQ,EAAE;YACZ,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACnD,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChC,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;SACjC;QAED,IAAI,IAAI,EAAE;YACR,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;SACxC;QAED,IAAI,IAAI,EAAE;YACR,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC7C,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;SACrB;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAhCW,QAAA,aAAa,iBAgCxB", "file": "geojson.js", "sourcesContent": ["import { geoPath } from 'd3-geo';\nimport { DATAVIEW_TYPE } from '../../constants';\nimport type { DataView } from '../../data-view';\nimport type { Parser } from '..';\nimport { mergeDeepImmer } from '../../utils/js';\nimport trufRewind from '@turf/rewind';\nimport flatten from '@turf/flatten';\nimport { isObject } from '@visactor/vutils';\n\nconst geoPathInstance = geoPath();\nexport interface IGeoJSONOptions {\n  centroid?: boolean;\n  name?: boolean;\n  bbox?: boolean;\n  rewind?:\n    | boolean\n    | {\n        reverse?: boolean;\n      };\n}\n\nexport const DEFAULT_GEOJSON_OPTIONS = {\n  centroid: false,\n  name: false,\n  bbox: false,\n  rewind: false\n};\nexport const MultiToSingle = (feature: any) => {\n  if (feature.geometry.type.startsWith('Multi')) {\n    const f = flatten(feature).features[0];\n    return { ...f, ...f.properties };\n  }\n  return { ...feature, ...feature.properties };\n};\nexport const flattenFeature = (data: any[]) => {\n  const featuresArr: any[] = [];\n  data.forEach((item: any) => {\n    if (item.type === 'FeatureCollection') {\n      // featureCollection\n      item.features.forEach((feature: any) => {\n        featuresArr.push(MultiToSingle(feature));\n      });\n    } else {\n      // feature\n      featuresArr.push(MultiToSingle(item));\n    }\n  });\n  return featuresArr;\n};\n\n/**\n * 解析geojson\n * @param data\n * @param _options\n * @param dataView\n * @returns\n */\nexport const geoJSONParser: Parser = (data: any, options: IGeoJSONOptions = {}, dataView: DataView) => {\n  dataView.type = DATAVIEW_TYPE.GEO;\n\n  const mergeOptions: IGeoJSONOptions = mergeDeepImmer(DEFAULT_GEOJSON_OPTIONS, options);\n\n  const { centroid, name, bbox, rewind } = mergeOptions;\n  if (Array.isArray(data)) {\n    return flattenFeature(data);\n  }\n  let features: any[] = data.features;\n  if (rewind) {\n    features = trufRewind(data, { reverse: isObject(rewind) ? rewind.reverse : true }).features;\n  }\n  features.forEach(feature => {\n    if (centroid) {\n      const centroid = geoPathInstance.centroid(feature);\n      feature.centroidX = centroid[0];\n      feature.centroidY = centroid[1];\n    }\n\n    if (name) {\n      feature.name = feature.properties.name;\n    }\n\n    if (bbox) {\n      const bbox = geoPathInstance.bounds(feature);\n      feature.bbox = bbox;\n    }\n  });\n\n  data.features = features;\n  return data;\n};\n"]}