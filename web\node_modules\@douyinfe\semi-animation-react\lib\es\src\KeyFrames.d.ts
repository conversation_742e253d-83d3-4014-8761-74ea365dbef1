import React, { Component } from 'react';
import PropTypes from 'prop-types';
import noop from './utils/noop';
export interface KeyFramesProps {
    frames?: any[];
    loop?: boolean;
    children?: React.ReactNode | ((KeyFramesProps: any) => React.ReactNode);
    forwardInstance?: (value: any) => void;
    onFrame?: (value: any) => void;
    onKeyRest?: (value: Record<string, any>) => void;
    onRest?: (value: Record<string, any>) => void;
}
export interface KeyFramesStates {
    currentStyle: Record<string, any>;
    frameIndex: number;
}
export default class KeyFrames extends Component<KeyFramesProps, KeyFramesStates> {
    static propTypes: {
        frames: PropTypes.Requireable<any[]>;
        loop: PropTypes.Requireable<boolean>;
        onFrame: PropTypes.Requireable<(...args: any[]) => any>;
        onKeyRest: PropTypes.Requireable<(...args: any[]) => any>;
        onRest: PropTypes.Requireable<(...args: any[]) => any>;
    };
    static defaultProps: {
        frames: any[];
        loop: boolean;
        onKeyRest: typeof noop;
        onRest: typeof noop;
        onFrame: typeof noop;
    };
    instance: any;
    constructor(props?: {});
    onFrame: (props?: {}) => void;
    next: () => void;
    forwardInstance: (instance: any) => void;
    componentDidMount(): void;
    componentWillUnmount(): void;
    render(): React.JSX.Element;
}
