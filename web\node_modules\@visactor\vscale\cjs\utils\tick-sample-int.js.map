{"version": 3, "sources": ["../src/utils/tick-sample-int.ts"], "names": [], "mappings": ";;;AAAA,6CAA2C;AAE3C,SAAS,aAAa,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY,EAAE,OAAgB;IAChF,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,OAAO,GAAG,IAAI,IAAI,EAAE;QAClB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChB,GAAG,IAAI,IAAI,CAAC;KACb;IACD,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,EAAE,CAAC;KACjB;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAUD,SAAgB,KAAK,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,cAAwB;IACxF,IAAI,OAAgB,CAAC;IACrB,IAAI,IAAY,CAAC;IAEjB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC3B,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC3B,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,EAAE,CAAC;KACX;IACD,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;IACD,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;QAC5B,MAAM,CAAC,GAAG,KAAK,CAAC;QAChB,KAAK,GAAG,IAAI,CAAC;QACb,IAAI,GAAG,CAAC,CAAC;KACV;IAED,IAAI,aAAa,GAAG,IAAA,gBAAO,EAAC,CAAC,EAAE,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACxD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;IACtD,IAAI,CAAC,cAAc,EAAE;QACnB,OACE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK;YAC5C,aAAa,GAAG,CAAC,EACjB;YACA,aAAa,IAAI,CAAC,CAAC;YACnB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC;SACnD;KACF;IAED,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC;AAhCD,sBAgCC;AASD,SAAgB,SAAS,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY;IACjE,IAAI,OAAgB,CAAC;IAErB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC3B,IAAI,GAAG,IAAA,gBAAO,EAAC,CAAC,EAAE,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACvD,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;QAC5B,MAAM,CAAC,GAAG,KAAK,CAAC;QAChB,KAAK,GAAG,IAAI,CAAC;QACb,IAAI,GAAG,CAAC,CAAC;KACV;IACD,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC;AAZD,8BAYC", "file": "tick-sample-int.js", "sourcesContent": ["import { clamper } from '@visactor/vutils';\n\nfunction generateTicks(start: number, stop: number, step: number, reverse: boolean) {\n  const ticks: number[] = [];\n  let ptr = start;\n  while (ptr <= stop) {\n    ticks.push(ptr);\n    ptr += step;\n  }\n  if (reverse) {\n    ticks.reverse();\n  }\n\n  return ticks;\n}\n\n/**\n * 根据start、stop、count进行分割，不要求count完全准确，但是保证均匀，输出为整数数组\n * @param start\n * @param stop\n * @param count\n * @param allowExcessive 如果为true，实际输出的tick数 >= count，否则实际输出的tick数 <= count\n * @returns\n */\nexport function ticks(start: number, stop: number, count: number, allowExcessive?: boolean) {\n  let reverse: boolean;\n  let step: number;\n\n  stop = Math.floor(+stop);\n  start = Math.floor(+start);\n  count = Math.floor(+count);\n  if (!count) {\n    return [];\n  }\n  if (start === stop) {\n    return [start];\n  }\n  if ((reverse = stop < start)) {\n    const n = start;\n    start = stop;\n    stop = n;\n  }\n\n  let expectedCount = clamper(1, stop - start + 1)(count);\n  step = Math.floor((stop - start + 1) / expectedCount);\n  if (!allowExcessive) {\n    while (\n      Math.ceil((stop - start + 1) / step) > count && // 估算实际的tick数量，根据数量调整step\n      expectedCount > 1\n    ) {\n      expectedCount -= 1;\n      step = Math.floor((stop - start) / expectedCount);\n    }\n  }\n\n  return generateTicks(start, stop, step, reverse);\n}\n\n/**\n * 给定step的ticks分割\n * @param start\n * @param stop\n * @param step\n * @returns\n */\nexport function stepTicks(start: number, stop: number, step: number) {\n  let reverse: boolean;\n\n  stop = Math.floor(+stop);\n  start = Math.floor(+start);\n  step = clamper(1, stop - start + 1)(Math.floor(+step));\n  if ((reverse = stop < start)) {\n    const n = start;\n    start = stop;\n    stop = n;\n  }\n  return generateTicks(start, stop, step, reverse);\n}\n"]}