/*!
 * # Semantic UI 2.5.0 - Image
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
             Image
*******************************/

.ui.image {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: 100%;
  background-color: transparent;
}
img.ui.image {
  display: block;
}
.ui.image svg,
.ui.image img {
  display: block;
  max-width: 100%;
  height: auto;
}


/*******************************
            States
*******************************/

.ui.hidden.images,
.ui.hidden.image {
  display: none;
}
.ui.hidden.transition.images,
.ui.hidden.transition.image {
  display: block;
  visibility: hidden;
}
.ui.images > .hidden.transition {
  display: inline-block;
  visibility: hidden;
}
.ui.disabled.images,
.ui.disabled.image {
  cursor: default;
  opacity: 0.45;
}


/*******************************
          Variations
*******************************/


/*--------------
     Inline
---------------*/

.ui.inline.image,
.ui.inline.image svg,
.ui.inline.image img {
  display: inline-block;
}

/*------------------
  Vertical Aligned
-------------------*/

.ui.top.aligned.images .image,
.ui.top.aligned.image,
.ui.top.aligned.image svg,
.ui.top.aligned.image img {
  display: inline-block;
  vertical-align: top;
}
.ui.middle.aligned.images .image,
.ui.middle.aligned.image,
.ui.middle.aligned.image svg,
.ui.middle.aligned.image img {
  display: inline-block;
  vertical-align: middle;
}
.ui.bottom.aligned.images .image,
.ui.bottom.aligned.image,
.ui.bottom.aligned.image svg,
.ui.bottom.aligned.image img {
  display: inline-block;
  vertical-align: bottom;
}

/*--------------
     Rounded
---------------*/

.ui.rounded.images .image,
.ui.rounded.image,
.ui.rounded.images .image > *,
.ui.rounded.image > * {
  border-radius: 0.3125em;
}

/*--------------
    Bordered
---------------*/

.ui.bordered.images .image,
.ui.bordered.images img,
.ui.bordered.images svg,
.ui.bordered.image img,
.ui.bordered.image svg,
img.ui.bordered.image {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/*--------------
    Circular
---------------*/

.ui.circular.images,
.ui.circular.image {
  overflow: hidden;
}
.ui.circular.images .image,
.ui.circular.image,
.ui.circular.images .image > *,
.ui.circular.image > * {
  border-radius: 500rem;
}

/*--------------
     Fluid
---------------*/

.ui.fluid.images,
.ui.fluid.image,
.ui.fluid.images img,
.ui.fluid.images svg,
.ui.fluid.image svg,
.ui.fluid.image img {
  display: block;
  width: 100%;
  height: auto;
}

/*--------------
     Avatar
---------------*/

.ui.avatar.images .image,
.ui.avatar.images img,
.ui.avatar.images svg,
.ui.avatar.image img,
.ui.avatar.image svg,
.ui.avatar.image {
  margin-right: 0.25em;
  display: inline-block;
  width: 2em;
  height: 2em;
  border-radius: 500rem;
}

/*-------------------
       Spaced
--------------------*/

.ui.spaced.image {
  display: inline-block !important;
  margin-left: 0.5em;
  margin-right: 0.5em;
}
.ui[class*="left spaced"].image {
  margin-left: 0.5em;
  margin-right: 0em;
}
.ui[class*="right spaced"].image {
  margin-left: 0em;
  margin-right: 0.5em;
}

/*-------------------
       Floated
--------------------*/

.ui.floated.image,
.ui.floated.images {
  float: left;
  margin-right: 1em;
  margin-bottom: 1em;
}
.ui.right.floated.images,
.ui.right.floated.image {
  float: right;
  margin-right: 0em;
  margin-bottom: 1em;
  margin-left: 1em;
}
.ui.floated.images:last-child,
.ui.floated.image:last-child {
  margin-bottom: 0em;
}
.ui.centered.images,
.ui.centered.image {
  margin-left: auto;
  margin-right: auto;
}

/*--------------
     Sizes
---------------*/

.ui.mini.images .image,
.ui.mini.images img,
.ui.mini.images svg,
.ui.mini.image {
  width: 35px;
  height: auto;
  font-size: 0.78571429rem;
}
.ui.tiny.images .image,
.ui.tiny.images img,
.ui.tiny.images svg,
.ui.tiny.image {
  width: 80px;
  height: auto;
  font-size: 0.85714286rem;
}
.ui.small.images .image,
.ui.small.images img,
.ui.small.images svg,
.ui.small.image {
  width: 150px;
  height: auto;
  font-size: 0.92857143rem;
}
.ui.medium.images .image,
.ui.medium.images img,
.ui.medium.images svg,
.ui.medium.image {
  width: 300px;
  height: auto;
  font-size: 1rem;
}
.ui.large.images .image,
.ui.large.images img,
.ui.large.images svg,
.ui.large.image {
  width: 450px;
  height: auto;
  font-size: 1.14285714rem;
}
.ui.big.images .image,
.ui.big.images img,
.ui.big.images svg,
.ui.big.image {
  width: 600px;
  height: auto;
  font-size: 1.28571429rem;
}
.ui.huge.images .image,
.ui.huge.images img,
.ui.huge.images svg,
.ui.huge.image {
  width: 800px;
  height: auto;
  font-size: 1.42857143rem;
}
.ui.massive.images .image,
.ui.massive.images img,
.ui.massive.images svg,
.ui.massive.image {
  width: 960px;
  height: auto;
  font-size: 1.71428571rem;
}


/*******************************
              Groups
*******************************/

.ui.images {
  font-size: 0em;
  margin: 0em -0.25rem 0rem;
}
.ui.images .image,
.ui.images > img,
.ui.images > svg {
  display: inline-block;
  margin: 0em 0.25rem 0.5rem;
}


/*******************************
         Theme Overrides
*******************************/



/*******************************
         Site Overrides
*******************************/

