!function(C,e,T){"use strict";e=void 0!==e&&e.Math==Math?e:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),C.fn.rating=function(m){var f,v=C(this),p=v.selector||"",b=(new Date).getTime(),h=[],y=m,x="string"==typeof y,R=[].slice.call(arguments,1);return v.each(function(){var e,i=C.isPlainObject(m)?C.extend(!0,{},C.fn.rating.settings,m):C.extend({},C.fn.rating.settings),n=i.namespace,a=i.className,t=i.metadata,o=i.selector,r=(i.error,"."+n),s="module-"+n,l=this,c=C(this).data(s),u=C(this),d=u.find(o.icon),g={initialize:function(){g.verbose("Initializing rating module",i),0===d.length&&g.setup.layout(),i.interactive?g.enable():g.disable(),g.set.initialLoad(),g.set.rating(g.get.initialRating()),g.remove.initialLoad(),g.instantiate()},instantiate:function(){g.verbose("Instantiating module",i),c=g,u.data(s,g)},destroy:function(){g.verbose("Destroying previous instance",c),g.remove.events(),u.removeData(s)},refresh:function(){d=u.find(o.icon)},setup:{layout:function(){var e=g.get.maxRating(),n=C.fn.rating.settings.templates.icon(e);g.debug("Generating icon html dynamically"),u.html(n),g.refresh()}},event:{mouseenter:function(){var e=C(this);e.nextAll().removeClass(a.selected),u.addClass(a.selected),e.addClass(a.selected).prevAll().addClass(a.selected)},mouseleave:function(){u.removeClass(a.selected),d.removeClass(a.selected)},click:function(){var e=C(this),n=g.get.rating(),t=d.index(e)+1;("auto"==i.clearable?1===d.length:i.clearable)&&n==t?g.clearRating():g.set.rating(t)}},clearRating:function(){g.debug("Clearing current rating"),g.set.rating(0)},bind:{events:function(){g.verbose("Binding events"),u.on("mouseenter"+r,o.icon,g.event.mouseenter).on("mouseleave"+r,o.icon,g.event.mouseleave).on("click"+r,o.icon,g.event.click)}},remove:{events:function(){g.verbose("Removing events"),u.off(r)},initialLoad:function(){e=!1}},enable:function(){g.debug("Setting rating to interactive mode"),g.bind.events(),u.removeClass(a.disabled)},disable:function(){g.debug("Setting rating to read-only mode"),g.remove.events(),u.addClass(a.disabled)},is:{initialLoad:function(){return e}},get:{initialRating:function(){return u.data(t.rating)!==T?(u.removeData(t.rating),u.data(t.rating)):i.initialRating},maxRating:function(){return u.data(t.maxRating)!==T?(u.removeData(t.maxRating),u.data(t.maxRating)):i.maxRating},rating:function(){var e=d.filter("."+a.active).length;return g.verbose("Current rating retrieved",e),e}},set:{rating:function(e){var n=0<=e-1?e-1:0,t=d.eq(n);u.removeClass(a.selected),d.removeClass(a.selected).removeClass(a.active),0<e&&(g.verbose("Setting current rating to",e),t.prevAll().addBack().addClass(a.active)),g.is.initialLoad()||i.onRate.call(l,e)},initialLoad:function(){e=!0}},setting:function(e,n){if(g.debug("Changing setting",e,n),C.isPlainObject(e))C.extend(!0,i,e);else{if(n===T)return i[e];C.isPlainObject(i[e])?C.extend(!0,i[e],n):i[e]=n}},internal:function(e,n){if(C.isPlainObject(e))C.extend(!0,g,e);else{if(n===T)return g[e];g[e]=n}},debug:function(){!i.silent&&i.debug&&(i.performance?g.performance.log(arguments):(g.debug=Function.prototype.bind.call(console.info,console,i.name+":"),g.debug.apply(console,arguments)))},verbose:function(){!i.silent&&i.verbose&&i.debug&&(i.performance?g.performance.log(arguments):(g.verbose=Function.prototype.bind.call(console.info,console,i.name+":"),g.verbose.apply(console,arguments)))},error:function(){i.silent||(g.error=Function.prototype.bind.call(console.error,console,i.name+":"),g.error.apply(console,arguments))},performance:{log:function(e){var n,t;i.performance&&(t=(n=(new Date).getTime())-(b||n),b=n,h.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:l,"Execution Time":t})),clearTimeout(g.performance.timer),g.performance.timer=setTimeout(g.performance.display,500)},display:function(){var e=i.name+":",t=0;b=!1,clearTimeout(g.performance.timer),C.each(h,function(e,n){t+=n["Execution Time"]}),e+=" "+t+"ms",p&&(e+=" '"+p+"'"),1<v.length&&(e+=" ("+v.length+")"),(console.group!==T||console.table!==T)&&0<h.length&&(console.groupCollapsed(e),console.table?console.table(h):C.each(h,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),h=[]}},invoke:function(i,e,n){var a,o,t,r=c;return e=e||R,n=l||n,"string"==typeof i&&r!==T&&(i=i.split(/[\. ]/),a=i.length-1,C.each(i,function(e,n){var t=e!=a?n+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(C.isPlainObject(r[t])&&e!=a)r=r[t];else{if(r[t]!==T)return o=r[t],!1;if(!C.isPlainObject(r[n])||e==a)return r[n]!==T&&(o=r[n]),!1;r=r[n]}})),C.isFunction(o)?t=o.apply(n,e):o!==T&&(t=o),C.isArray(f)?f.push(t):f!==T?f=[f,t]:t!==T&&(f=t),o}};x?(c===T&&g.initialize(),g.invoke(y)):(c!==T&&c.invoke("destroy"),g.initialize())}),f!==T?f:this},C.fn.rating.settings={name:"Rating",namespace:"rating",slent:!1,debug:!1,verbose:!1,performance:!0,initialRating:0,interactive:!0,maxRating:4,clearable:"auto",fireOnInit:!1,onRate:function(e){},error:{method:"The method you called is not defined",noMaximum:"No maximum rating specified. Cannot generate HTML automatically"},metadata:{rating:"rating",maxRating:"maxRating"},className:{active:"active",disabled:"disabled",selected:"selected",loading:"loading"},selector:{icon:".icon"},templates:{icon:function(e){for(var n=1,t="";n<=e;)t+='<i class="icon"></i>',n++;return t}}}}(jQuery,window,void document);