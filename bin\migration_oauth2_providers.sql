-- Migration script to add OAuth2Providers field to users table
-- This migration adds support for generic OAuth2 provider storage while maintaining backward compatibility

-- Add the new oauth2_providers column to store OAuth2 provider mappings in JSON format
ALTER TABLE users ADD COLUMN oauth2_providers TEXT DEFAULT '';

-- Create index for better performance when searching OAuth2 providers
CREATE INDEX idx_users_oauth2_providers ON users(oauth2_providers);

-- Optional: Migrate existing OAuth2 data to the new format
-- This is commented out by default to avoid data loss
-- Uncomment and run manually if you want to migrate existing data

/*
-- Migrate existing GitHub IDs
UPDATE users 
SET oauth2_providers = JSON_SET(
    CASE WHEN oauth2_providers = '' THEN '{}' ELSE oauth2_providers END,
    '$.github', github_id
)
WHERE github_id IS NOT NULL AND github_id != '';

-- Migrate existing OIDC IDs
UPDATE users 
SET oauth2_providers = JSON_SET(
    CASE WHEN oauth2_providers = '' THEN '{}' ELSE oauth2_providers END,
    '$.oidc', oidc_id
)
WHERE oidc_id IS NOT NULL AND oidc_id != '';

-- Migrate existing LinuxDO IDs
UPDATE users 
SET oauth2_providers = JSON_SET(
    CASE WHEN oauth2_providers = '' THEN '{}' ELSE oauth2_providers END,
    '$.linuxdo', linux_do_id
)
WHERE linux_do_id IS NOT NULL AND linux_do_id != '';

-- Migrate existing WeChat IDs
UPDATE users 
SET oauth2_providers = JSON_SET(
    CASE WHEN oauth2_providers = '' THEN '{}' ELSE oauth2_providers END,
    '$.wechat', wechat_id
)
WHERE wechat_id IS NOT NULL AND wechat_id != '';

-- Migrate existing Telegram IDs
UPDATE users 
SET oauth2_providers = JSON_SET(
    CASE WHEN oauth2_providers = '' THEN '{}' ELSE oauth2_providers END,
    '$.telegram', telegram_id
)
WHERE telegram_id IS NOT NULL AND telegram_id != '';
*/

-- Note: The old columns (github_id, oidc_id, etc.) are kept for backward compatibility
-- They can be removed in a future migration after ensuring all systems use the new format
