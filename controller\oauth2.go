// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.

package controller

import (
	"fmt"
	"net/http"
	"strings"
	"veloera/common"
	"veloera/model"
	"veloera/service"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

// OAuth2Auth 通用OAuth2认证处理器
func OAuth2Auth(c *gin.Context) {
	providerName := c.<PERSON>m("provider")
	if providerName == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少OAuth2提供商参数",
		})
		return
	}

	session := sessions.Default(c)
	
	// 验证state参数
	state := c.Query("state")
	if state == "" || session.Get("oauth_state") == nil || state != session.Get("oauth_state").(string) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "state参数无效或已过期",
		})
		return
	}

	// 检查是否有错误参数
	errorCode := c.Query("error")
	if errorCode != "" {
		errorDescription := c.Query("error_description")
		if errorDescription == "" {
			errorDescription = "OAuth2认证失败"
		}
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": errorDescription,
		})
		return
	}

	// 检查用户是否已登录（绑定模式）
	username := session.Get("username")
	if username != nil {
		OAuth2Bind(c)
		return
	}

	// 验证提供商是否启用
	err := service.GlobalOAuth2Manager.ValidateProviderConfig(providerName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("管理员未开启通过 %s 登录以及注册", providerName),
		})
		return
	}

	// 获取授权码
	code := c.Query("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少授权码",
		})
		return
	}

	// 构建回调URL
	scheme := "http"
	if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/api/oauth/%s", scheme, c.Request.Host, providerName)

	// 通过OAuth2服务获取用户信息
	oauth2User, err := service.GlobalOAuth2Manager.GetService().ExchangeCodeForUser(providerName, code, redirectURI)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 查找或创建用户
	user, err := model.FindUserByOAuth2Provider(providerName, oauth2User.ID)
	if err != nil {
		// 用户不存在，创建新用户
		if !common.RegisterEnabled {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "管理员关闭了新用户注册",
			})
			return
		}

		user = &model.User{
			Username:    generateUniqueUsername(oauth2User.Username, oauth2User.Email),
			DisplayName: oauth2User.DisplayName,
			Email:       oauth2User.Email,
		}

		// 设置OAuth2提供商ID
		err = user.SetOAuth2Provider(providerName, oauth2User.ID)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "设置OAuth2提供商信息失败",
			})
			return
		}

		// 处理邀请码
		affCode := session.Get("aff")
		inviterId := 0
		if affCode != nil {
			inviterId = model.GetUserIdByAffCode(affCode.(string))
		}

		err = user.Insert(inviterId)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	}

	// 检查用户状态
	if user.Status != common.UserStatusEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户已被封禁",
		})
		return
	}

	// 设置登录会话
	setupLogin(user, c)
}

// OAuth2Bind 绑定OAuth2账户到现有用户
func OAuth2Bind(c *gin.Context) {
	providerName := c.Param("provider")
	if providerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少OAuth2提供商参数",
		})
		return
	}

	// 验证提供商是否启用
	err := service.GlobalOAuth2Manager.ValidateProviderConfig(providerName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("管理员未开启通过 %s 登录以及注册", providerName),
		})
		return
	}

	// 获取授权码
	code := c.Query("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少授权码",
		})
		return
	}

	// 构建回调URL
	scheme := "http"
	if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/api/oauth/%s", scheme, c.Request.Host, providerName)

	// 通过OAuth2服务获取用户信息
	oauth2User, err := service.GlobalOAuth2Manager.GetService().ExchangeCodeForUser(providerName, code, redirectURI)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 检查该OAuth2账户是否已被绑定
	if model.IsOAuth2ProviderIdAlreadyTaken(providerName, oauth2User.ID) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("该 %s 账户已被绑定", providerName),
		})
		return
	}

	// 获取当前用户
	session := sessions.Default(c)
	id := session.Get("id")
	if id == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	user := &model.User{Id: id.(int)}
	err = user.FillUserById()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 绑定OAuth2提供商
	err = user.SetOAuth2Provider(providerName, oauth2User.ID)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "设置OAuth2提供商信息失败",
		})
		return
	}

	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "绑定成功",
	})
}

// OAuth2Unbind 解绑OAuth2账户
func OAuth2Unbind(c *gin.Context) {
	providerName := c.Param("provider")
	if providerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少OAuth2提供商参数",
		})
		return
	}

	// 获取当前用户
	session := sessions.Default(c)
	id := session.Get("id")
	if id == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	user := &model.User{Id: id.(int)}
	err := user.FillUserById()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 检查是否绑定了该提供商
	if !user.HasOAuth2Provider(providerName) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("未绑定 %s 账户", providerName),
		})
		return
	}

	// 解绑OAuth2提供商
	err = user.RemoveOAuth2Provider(providerName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "解绑失败",
		})
		return
	}

	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "解绑成功",
	})
}

// OAuth2GetAuthURL 获取OAuth2授权URL
func OAuth2GetAuthURL(c *gin.Context) {
	providerName := c.Param("provider")
	if providerName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少OAuth2提供商参数",
		})
		return
	}

	// 验证提供商是否启用
	err := service.GlobalOAuth2Manager.ValidateProviderConfig(providerName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("OAuth2提供商 '%s' 未启用或配置不完整", providerName),
		})
		return
	}

	// 生成state参数
	session := sessions.Default(c)
	state := common.GetRandomString(12)

	// 处理邀请码
	affCode := c.Query("aff")
	if affCode != "" {
		session.Set("aff", affCode)
	}

	session.Set("oauth_state", state)
	err = session.Save()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "保存会话状态失败",
		})
		return
	}

	// 构建回调URL
	scheme := "http"
	if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/api/oauth/%s", scheme, c.Request.Host, providerName)

	// 生成授权URL
	authURL, err := service.GlobalOAuth2Manager.GetService().GenerateAuthURL(providerName, state, redirectURI)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"auth_url": authURL,
			"state":    state,
		},
	})
}

// OAuth2GetProviders 获取可用的OAuth2提供商列表
func OAuth2GetProviders(c *gin.Context) {
	providers := service.GlobalOAuth2Manager.GetProviderInfo()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    providers,
	})
}

// generateUniqueUsername 生成唯一的用户名
func generateUniqueUsername(preferredUsername, email string) string {
	// 优先使用OAuth2提供的用户名
	if preferredUsername != "" {
		username := preferredUsername
		// 检查用户名是否已存在
		if !model.IsUsernameAlreadyTaken(username) {
			return username
		}

		// 如果已存在，尝试添加数字后缀
		for i := 1; i <= 999; i++ {
			candidate := fmt.Sprintf("%s%d", username, i)
			if !model.IsUsernameAlreadyTaken(candidate) {
				return candidate
			}
		}
	}

	// 如果OAuth2用户名不可用，尝试使用邮箱前缀
	if email != "" {
		emailPrefix := email
		if atIndex := strings.Index(email, "@"); atIndex > 0 {
			emailPrefix = email[:atIndex]
		}

		if !model.IsUsernameAlreadyTaken(emailPrefix) {
			return emailPrefix
		}

		// 如果邮箱前缀已存在，尝试添加数字后缀
		for i := 1; i <= 999; i++ {
			candidate := fmt.Sprintf("%s%d", emailPrefix, i)
			if !model.IsUsernameAlreadyTaken(candidate) {
				return candidate
			}
		}
	}

	// 最后生成随机用户名
	for i := 0; i < 10; i++ {
		randomUsername := "user_" + common.GetRandomString(8)
		if !model.IsUsernameAlreadyTaken(randomUsername) {
			return randomUsername
		}
	}

	// 如果所有方法都失败，使用时间戳
	return fmt.Sprintf("user_%d", common.GetTimestamp())
}
