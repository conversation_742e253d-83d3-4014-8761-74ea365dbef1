# OAuth2 配置示例
# 复制此文件为 .env 并填入您的实际配置值

# ===========================================
# 内置 OAuth2 提供商配置
# ===========================================

# GitHub OAuth2 (传统配置方式，仍然支持)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# LinuxDO OAuth2 (传统配置方式，仍然支持)
LINUXDO_CLIENT_ID=your_linuxdo_client_id
LINUXDO_CLIENT_SECRET=your_linuxdo_client_secret

# ===========================================
# 通用 OAuth2 提供商配置
# ===========================================

# Google OAuth2
# 获取地址: https://console.cloud.google.com/
# 回调URL: https://your-domain.com/oauth2/callback/google
OAUTH2_GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
OAUTH2_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH2_GOOGLE_ENABLED=true

# Microsoft OAuth2 (Azure AD)
# 获取地址: https://portal.azure.com/
# 回调URL: https://your-domain.com/oauth2/callback/microsoft
OAUTH2_MICROSOFT_CLIENT_ID=your_microsoft_application_id
OAUTH2_MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
OAUTH2_MICROSOFT_ENABLED=true

# Discord OAuth2
# 获取地址: https://discord.com/developers/applications
# 回调URL: https://your-domain.com/oauth2/callback/discord
OAUTH2_DISCORD_CLIENT_ID=your_discord_application_id
OAUTH2_DISCORD_CLIENT_SECRET=your_discord_client_secret
OAUTH2_DISCORD_ENABLED=true

# GitLab OAuth2
# 获取地址: https://gitlab.com/-/profile/applications (或您的GitLab实例)
# 回调URL: https://your-domain.com/oauth2/callback/gitlab
OAUTH2_GITLAB_CLIENT_ID=your_gitlab_application_id
OAUTH2_GITLAB_CLIENT_SECRET=your_gitlab_client_secret
OAUTH2_GITLAB_ENABLED=true

# ===========================================
# 自定义 OAuth2 提供商配置示例
# ===========================================

# 如果您需要支持其他 OAuth2 提供商，可以使用以下格式：
# OAUTH2_PROVIDER_NAME_CLIENT_ID=client_id
# OAUTH2_PROVIDER_NAME_CLIENT_SECRET=client_secret
# OAUTH2_PROVIDER_NAME_AUTH_URL=https://provider.com/oauth/authorize
# OAUTH2_PROVIDER_NAME_TOKEN_URL=https://provider.com/oauth/token
# OAUTH2_PROVIDER_NAME_USER_INFO_URL=https://provider.com/api/user
# OAUTH2_PROVIDER_NAME_SCOPES=read,email
# OAUTH2_PROVIDER_NAME_ENABLED=true

# 示例：自定义提供商
# OAUTH2_CUSTOM_CLIENT_ID=your_custom_client_id
# OAUTH2_CUSTOM_CLIENT_SECRET=your_custom_client_secret
# OAUTH2_CUSTOM_AUTH_URL=https://custom.example.com/oauth/authorize
# OAUTH2_CUSTOM_TOKEN_URL=https://custom.example.com/oauth/token
# OAUTH2_CUSTOM_USER_INFO_URL=https://custom.example.com/api/user
# OAUTH2_CUSTOM_SCOPES=read,profile
# OAUTH2_CUSTOM_ENABLED=true

# ===========================================
# 其他相关配置
# ===========================================

# 服务器地址 (用于生成回调URL)
SERVER_ADDRESS=https://your-domain.com

# 会话密钥 (建议使用随机生成的32位字符串)
SESSION_SECRET=your_random_session_secret_32_chars

# 是否允许新用户注册
REGISTER_ENABLED=true

# 新用户默认配额
QUOTA_FOR_NEW_USER=500000

# ===========================================
# 配置说明
# ===========================================

# 1. 所有的 CLIENT_SECRET 都是敏感信息，请妥善保管
# 2. 回调URL必须与OAuth2应用中配置的完全一致
# 3. 启用OAuth2提供商前，请确保已正确配置CLIENT_ID和CLIENT_SECRET
# 4. 生产环境中请使用HTTPS
# 5. 建议定期轮换CLIENT_SECRET以提高安全性

# ===========================================
# 获取OAuth2应用配置的链接
# ===========================================

# Google Cloud Console: https://console.cloud.google.com/
# Microsoft Azure Portal: https://portal.azure.com/
# Discord Developer Portal: https://discord.com/developers/applications
# GitLab Applications: https://gitlab.com/-/profile/applications
# GitHub Developer Settings: https://github.com/settings/developers
# LinuxDO Connect: https://connect.linux.do/
