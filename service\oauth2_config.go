// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.

package service

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"veloera/common"
)

// OAuth2ConfigManager OAuth2配置管理器
type OAuth2ConfigManager struct {
	configs map[string]OAuth2Config
}

// NewOAuth2ConfigManager 创建OAuth2配置管理器
func NewOAuth2ConfigManager() *OAuth2ConfigManager {
	return &OAuth2ConfigManager{
		configs: make(map[string]OAuth2Config),
	}
}

// LoadFromEnvironment 从环境变量加载OAuth2配置
func (m *OAuth2ConfigManager) LoadFromEnvironment() {
	// 加载通用OAuth2提供商配置
	// 环境变量格式: OAUTH2_PROVIDER_NAME_FIELD=value
	// 例如: OAUTH2_GOOGLE_CLIENT_ID=xxx, OAUTH2_GOOGLE_CLIENT_SECRET=xxx
	
	envVars := os.Environ()
	providerConfigs := make(map[string]map[string]string)
	
	for _, env := range envVars {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) != 2 {
			continue
		}
		
		key := parts[0]
		value := parts[1]
		
		if strings.HasPrefix(key, "OAUTH2_") {
			// 解析环境变量名: OAUTH2_PROVIDER_FIELD
			keyParts := strings.Split(key, "_")
			if len(keyParts) >= 3 {
				provider := strings.ToLower(keyParts[1])
				field := strings.ToLower(strings.Join(keyParts[2:], "_"))
				
				if providerConfigs[provider] == nil {
					providerConfigs[provider] = make(map[string]string)
				}
				providerConfigs[provider][field] = value
			}
		}
	}
	
	// 转换为OAuth2Config结构
	for provider, config := range providerConfigs {
		oauth2Config := OAuth2Config{
			ClientID:     config["client_id"],
			ClientSecret: config["client_secret"],
			AuthURL:      config["auth_url"],
			TokenURL:     config["token_url"],
			UserInfoURL:  config["user_info_url"],
			Scopes:       config["scopes"],
			Enabled:      config["enabled"] == "true",
		}
		
		// 如果没有指定URL，尝试从模板获取
		if oauth2Config.AuthURL == "" || oauth2Config.TokenURL == "" || oauth2Config.UserInfoURL == "" {
			if template, exists := OAuth2ProviderTemplates[provider]; exists {
				if oauth2Config.AuthURL == "" {
					oauth2Config.AuthURL = template.AuthURL
				}
				if oauth2Config.TokenURL == "" {
					oauth2Config.TokenURL = template.TokenURL
				}
				if oauth2Config.UserInfoURL == "" {
					oauth2Config.UserInfoURL = template.UserInfoURL
				}
				if oauth2Config.Scopes == "" {
					oauth2Config.Scopes = template.Scopes
				}
			}
		}
		
		// 只有当ClientID和ClientSecret都存在时才添加配置
		if oauth2Config.ClientID != "" && oauth2Config.ClientSecret != "" {
			m.configs[provider] = oauth2Config
		}
	}
}

// LoadFromOptions 从选项系统加载OAuth2配置
func (m *OAuth2ConfigManager) LoadFromOptions() {
	// 从OptionMap加载配置
	common.OptionMapRWMutex.RLock()
	defer common.OptionMapRWMutex.RUnlock()
	
	providerConfigs := make(map[string]map[string]string)
	
	for key, value := range common.OptionMap {
		if strings.HasPrefix(key, "oauth2.") {
			// 解析选项键: oauth2.provider.field
			keyParts := strings.Split(key, ".")
			if len(keyParts) >= 3 {
				provider := keyParts[1]
				field := strings.Join(keyParts[2:], ".")
				
				if providerConfigs[provider] == nil {
					providerConfigs[provider] = make(map[string]string)
				}
				providerConfigs[provider][field] = value
			}
		}
	}
	
	// 转换为OAuth2Config结构
	for provider, config := range providerConfigs {
		oauth2Config := OAuth2Config{
			ClientID:     config["client_id"],
			ClientSecret: config["client_secret"],
			AuthURL:      config["auth_url"],
			TokenURL:     config["token_url"],
			UserInfoURL:  config["user_info_url"],
			Scopes:       config["scopes"],
			Enabled:      config["enabled"] == "true",
		}
		
		// 如果没有指定URL，尝试从模板获取
		if oauth2Config.AuthURL == "" || oauth2Config.TokenURL == "" || oauth2Config.UserInfoURL == "" {
			if template, exists := OAuth2ProviderTemplates[provider]; exists {
				if oauth2Config.AuthURL == "" {
					oauth2Config.AuthURL = template.AuthURL
				}
				if oauth2Config.TokenURL == "" {
					oauth2Config.TokenURL = template.TokenURL
				}
				if oauth2Config.UserInfoURL == "" {
					oauth2Config.UserInfoURL = template.UserInfoURL
				}
				if oauth2Config.Scopes == "" {
					oauth2Config.Scopes = template.Scopes
				}
			}
		}
		
		// 只有当ClientID和ClientSecret都存在时才添加配置
		if oauth2Config.ClientID != "" && oauth2Config.ClientSecret != "" {
			m.configs[provider] = oauth2Config
		}
	}
}

// GetConfig 获取指定提供商的配置
func (m *OAuth2ConfigManager) GetConfig(provider string) (OAuth2Config, bool) {
	config, exists := m.configs[provider]
	return config, exists
}

// SetConfig 设置指定提供商的配置
func (m *OAuth2ConfigManager) SetConfig(provider string, config OAuth2Config) {
	m.configs[provider] = config
}

// GetAllConfigs 获取所有配置
func (m *OAuth2ConfigManager) GetAllConfigs() map[string]OAuth2Config {
	return m.configs
}

// RegisterDynamicProviders 注册动态配置的OAuth2提供商
func (m *OAuth2ConfigManager) RegisterDynamicProviders(manager *OAuth2Manager) {
	for provider, config := range m.configs {
		// 跳过内置提供商
		if provider == "github" || provider == "linuxdo" || provider == "nodeloc" || provider == "oidc" {
			continue
		}
		
		// 获取用户信息解析器
		parser, exists := OAuth2UserParsers[provider]
		if !exists {
			// 如果没有专用解析器，使用通用解析器
			parser = func(data map[string]interface{}) (*OAuth2User, error) {
				user := &OAuth2User{}
				
				// 尝试常见的字段名
				if id, ok := data["id"]; ok {
					user.ID = fmt.Sprintf("%v", id)
				} else if sub, ok := data["sub"]; ok {
					user.ID = fmt.Sprintf("%v", sub)
				}
				
				if username, ok := data["username"].(string); ok {
					user.Username = username
				} else if login, ok := data["login"].(string); ok {
					user.Username = login
				} else if preferredUsername, ok := data["preferred_username"].(string); ok {
					user.Username = preferredUsername
				}
				
				if email, ok := data["email"].(string); ok {
					user.Email = email
				}
				
				if name, ok := data["name"].(string); ok {
					user.DisplayName = name
				} else if displayName, ok := data["display_name"].(string); ok {
					user.DisplayName = displayName
				}
				
				if avatarURL, ok := data["avatar_url"].(string); ok {
					user.AvatarURL = avatarURL
				} else if picture, ok := data["picture"].(string); ok {
					user.AvatarURL = picture
				}
				
				if user.ID == "" {
					return nil, fmt.Errorf("%s用户信息不完整，缺少用户ID", provider)
				}
				
				return user, nil
			}
		}
		
		manager.RegisterGenericProvider(provider, config, parser)
		common.SysLog(fmt.Sprintf("Registered dynamic OAuth2 provider: %s", provider))
	}
}

// SaveToOptions 保存配置到选项系统
func (m *OAuth2ConfigManager) SaveToOptions(provider string, config OAuth2Config) error {
	prefix := fmt.Sprintf("oauth2.%s.", provider)
	
	// 构建选项键值对
	options := map[string]string{
		prefix + "client_id":     config.ClientID,
		prefix + "client_secret": config.ClientSecret,
		prefix + "auth_url":      config.AuthURL,
		prefix + "token_url":     config.TokenURL,
		prefix + "user_info_url": config.UserInfoURL,
		prefix + "scopes":        config.Scopes,
		prefix + "enabled":       fmt.Sprintf("%t", config.Enabled),
	}
	
	// 保存到OptionMap
	common.OptionMapRWMutex.Lock()
	for key, value := range options {
		common.OptionMap[key] = value
	}
	common.OptionMapRWMutex.Unlock()
	
	// 更新本地配置
	m.configs[provider] = config
	
	return nil
}

// RemoveConfig 移除指定提供商的配置
func (m *OAuth2ConfigManager) RemoveConfig(provider string) {
	delete(m.configs, provider)
	
	// 从OptionMap中移除相关配置
	prefix := fmt.Sprintf("oauth2.%s.", provider)
	common.OptionMapRWMutex.Lock()
	for key := range common.OptionMap {
		if strings.HasPrefix(key, prefix) {
			delete(common.OptionMap, key)
		}
	}
	common.OptionMapRWMutex.Unlock()
}

// 全局OAuth2配置管理器
var GlobalOAuth2ConfigManager *OAuth2ConfigManager

// ReloadOAuth2Config 重新加载OAuth2配置
func ReloadOAuth2Config() {
	if GlobalOAuth2ConfigManager != nil && GlobalOAuth2Manager != nil {
		// 重新加载配置
		GlobalOAuth2ConfigManager.LoadFromOptions()
		GlobalOAuth2ConfigManager.LoadFromEnvironment()

		// 重新注册动态提供商
		GlobalOAuth2ConfigManager.RegisterDynamicProviders(GlobalOAuth2Manager)

		common.SysLog("OAuth2 configuration reloaded")
	}
}

// InitOAuth2ConfigManager 初始化OAuth2配置管理器
func InitOAuth2ConfigManager() {
	GlobalOAuth2ConfigManager = NewOAuth2ConfigManager()

	// 从环境变量加载配置
	GlobalOAuth2ConfigManager.LoadFromEnvironment()

	// 从选项系统加载配置
	GlobalOAuth2ConfigManager.LoadFromOptions()

	common.SysLog("OAuth2 config manager initialized")
}
