"use strict";

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.ElementActiveByLegend = void 0;

const enums_1 = require("../graph/enums"), base_1 = require("./base"), vrender_components_1 = require("@visactor/vrender-components"), vutils_1 = require("@visactor/vutils"), utils_1 = require("./utils");

class ElementActiveByLegend extends base_1.BaseInteraction {
    constructor(view, options) {
        super(view, options), this.type = ElementActiveByLegend.type, this.handleStart = e => {
            var _a, _b;
            this.start(null === (_b = null === (_a = e.detail) || void 0 === _a ? void 0 : _a.data) || void 0 === _b ? void 0 : _b.id);
        }, this.handleReset = e => {
            this.resetAll();
        }, this.options = Object.assign({}, ElementActiveByLegend.defaultOptions, options), 
        this._marks = view.getMarksBySelector(this.options.selector);
    }
    getEvents() {
        return [ {
            type: vrender_components_1.LegendEvent.legendItemHover,
            handler: this.handleStart
        }, {
            type: vrender_components_1.LegendEvent.legendItemUnHover,
            handler: this.handleReset
        } ];
    }
    getStartState() {
        return this.options.state;
    }
    start(element) {
        const itemKey = element;
        if ((0, vutils_1.isNil)(itemKey)) return;
        const filterValue = (0, utils_1.generateFilterValue)(this.options);
        this._marks.forEach((mark => {
            mark.elements.forEach((el => {
                filterValue(el) === itemKey ? el.addState(this.options.state) : el.removeState(this.options.state);
            }));
        }));
    }
    resetAll() {
        this._marks.forEach((mark => {
            mark.elements.forEach((el => {
                el.removeState(this.options.state);
            }));
        }));
    }
    reset(element) {
        element ? this._marks && this._marks.includes(element.mark) && element.removeState(this.options.state) : this.resetAll();
    }
}

exports.ElementActiveByLegend = ElementActiveByLegend, ElementActiveByLegend.type = "element-active-by-legend", 
ElementActiveByLegend.defaultOptions = {
    state: enums_1.InteractionStateEnum.active,
    filterType: "groupKey"
};
//# sourceMappingURL=element-active-by-legend.js.map