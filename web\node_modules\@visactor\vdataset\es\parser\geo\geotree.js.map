{"version": 3, "sources": ["../src/parser/geo/geotree.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAsChD,MAAM,CAAC,MAAM,aAAa,GAAW,CAAC,IAAa,EAAE,UAA2B,EAAE,EAAE,QAAkB,EAAE,EAAE;IACxG,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC;IAClC,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC,CAAC", "file": "geotree.js", "sourcesContent": ["// import getPointAtLength from 'point-at-length';\n// import { geoPath } from 'd3-geo';\nimport { cloneDeep } from '@visactor/vutils';\nimport { DATAVIEW_TYPE } from '../../constants';\nimport type { DataView } from '../../data-view';\nimport type { Parser } from '..';\n\n// const geoPathGenerator = geoPath();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IGeoTreeOptions {\n  // nothing\n}\n\ntype TreeID = string;\n\ntype GeoTree = {\n  parent: TreeID;\n  treeID: TreeID;\n  name: string;\n  treeName: string;\n  payload: any;\n  children: any;\n  version: any;\n};\n\n// parent\tnull\n// treeID\t\"1\"\n// name\t\"江浙沪包邮区\"\n// treeName\t\"江浙沪包邮区_1\"\n// payload\t{…}\n// children\t{…}\n// version\t\"1.0.0\"\n\n/**\n * WIP: 解析GeoTree\n * @param data\n * @param _options\n * @param dataView\n * @returns\n */\nexport const GeoTreeParser: Parser = (data: GeoTree, options: IGeoTreeOptions = {}, dataView: DataView) => {\n  dataView.type = DATAVIEW_TYPE.GEO;\n  return cloneDeep(data);\n};\n"]}