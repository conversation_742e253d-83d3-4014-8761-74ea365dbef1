// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.

package service

import (
	"testing"
)

func TestOAuth2Service(t *testing.T) {
	// 创建OAuth2服务
	service := NewOAuth2Service()
	
	// 测试注册提供商
	config := OAuth2Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
		AuthURL:      "https://example.com/oauth/authorize",
		TokenURL:     "https://example.com/oauth/token",
		UserInfoURL:  "https://example.com/oauth/userinfo",
		Scopes:       "read",
		Enabled:      true,
	}
	
	provider := NewGenericOAuth2Provider("test", config, func(data map[string]interface{}) (*OAuth2User, error) {
		return &OAuth2User{
			ID:       "123",
			Username: "testuser",
			Email:    "<EMAIL>",
		}, nil
	})
	
	service.RegisterProvider(provider)
	
	// 测试获取提供商
	retrievedProvider, err := service.GetProvider("test")
	if err != nil {
		t.Errorf("获取提供商失败: %v", err)
	}
	
	if retrievedProvider.GetName() != "test" {
		t.Errorf("提供商名称不匹配，期望: test, 实际: %s", retrievedProvider.GetName())
	}
	
	// 测试生成授权URL
	authURL, err := service.GenerateAuthURL("test", "state123", "http://localhost/callback")
	if err != nil {
		t.Errorf("生成授权URL失败: %v", err)
	}
	
	if authURL == "" {
		t.Error("授权URL不能为空")
	}
	
	// 测试获取启用的提供商
	enabledProviders := service.GetEnabledProviders()
	if len(enabledProviders) != 1 {
		t.Errorf("启用的提供商数量不正确，期望: 1, 实际: %d", len(enabledProviders))
	}
}

func TestOAuth2ConfigManager(t *testing.T) {
	// 创建配置管理器
	manager := NewOAuth2ConfigManager()
	
	// 测试设置配置
	config := OAuth2Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
		AuthURL:      "https://example.com/oauth/authorize",
		TokenURL:     "https://example.com/oauth/token",
		UserInfoURL:  "https://example.com/oauth/userinfo",
		Scopes:       "read",
		Enabled:      true,
	}
	
	manager.SetConfig("test", config)
	
	// 测试获取配置
	retrievedConfig, exists := manager.GetConfig("test")
	if !exists {
		t.Error("配置不存在")
	}
	
	if retrievedConfig.ClientID != config.ClientID {
		t.Errorf("Client ID不匹配，期望: %s, 实际: %s", config.ClientID, retrievedConfig.ClientID)
	}
	
	// 测试获取所有配置
	allConfigs := manager.GetAllConfigs()
	if len(allConfigs) != 1 {
		t.Errorf("配置数量不正确，期望: 1, 实际: %d", len(allConfigs))
	}
}

func TestOAuth2Providers(t *testing.T) {
	// 测试GitHub提供商
	githubProvider := NewGitHubProvider()
	if githubProvider.GetName() != "github" {
		t.Errorf("GitHub提供商名称不正确，期望: github, 实际: %s", githubProvider.GetName())
	}
	
	// 测试LinuxDO提供商
	linuxdoProvider := NewLinuxDOProvider()
	if linuxdoProvider.GetName() != "linuxdo" {
		t.Errorf("LinuxDO提供商名称不正确，期望: linuxdo, 实际: %s", linuxdoProvider.GetName())
	}
	
	// 测试OIDC提供商
	oidcProvider := NewOIDCProvider()
	if oidcProvider.GetName() != "oidc" {
		t.Errorf("OIDC提供商名称不正确，期望: oidc, 实际: %s", oidcProvider.GetName())
	}
}

func TestOAuth2UserParsers(t *testing.T) {
	// 测试Google用户解析器
	googleParser := OAuth2UserParsers["google"]
	if googleParser == nil {
		t.Error("Google用户解析器不存在")
	}
	
	googleData := map[string]interface{}{
		"id":      "123456789",
		"email":   "<EMAIL>",
		"name":    "Test User",
		"picture": "https://example.com/avatar.jpg",
	}
	
	user, err := googleParser(googleData)
	if err != nil {
		t.Errorf("Google用户解析失败: %v", err)
	}
	
	if user.ID != "123456789" {
		t.Errorf("用户ID不正确，期望: 123456789, 实际: %s", user.ID)
	}
	
	if user.Email != "<EMAIL>" {
		t.Errorf("用户邮箱不正确，期望: <EMAIL>, 实际: %s", user.Email)
	}
	
	// 测试Microsoft用户解析器
	microsoftParser := OAuth2UserParsers["microsoft"]
	if microsoftParser == nil {
		t.Error("Microsoft用户解析器不存在")
	}
	
	microsoftData := map[string]interface{}{
		"id":                "abcd-1234-efgh-5678",
		"userPrincipalName": "<EMAIL>",
		"displayName":       "Test User",
	}
	
	user, err = microsoftParser(microsoftData)
	if err != nil {
		t.Errorf("Microsoft用户解析失败: %v", err)
	}
	
	if user.ID != "abcd-1234-efgh-5678" {
		t.Errorf("用户ID不正确，期望: abcd-1234-efgh-5678, 实际: %s", user.ID)
	}
	
	if user.Username != "<EMAIL>" {
		t.Errorf("用户名不正确，期望: <EMAIL>, 实际: %s", user.Username)
	}
}

func TestOAuth2ProviderTemplates(t *testing.T) {
	// 测试Google模板
	googleTemplate, exists := OAuth2ProviderTemplates["google"]
	if !exists {
		t.Error("Google OAuth2模板不存在")
	}
	
	if googleTemplate.AuthURL != "https://accounts.google.com/o/oauth2/v2/auth" {
		t.Errorf("Google授权URL不正确，期望: https://accounts.google.com/o/oauth2/v2/auth, 实际: %s", googleTemplate.AuthURL)
	}
	
	// 测试Microsoft模板
	microsoftTemplate, exists := OAuth2ProviderTemplates["microsoft"]
	if !exists {
		t.Error("Microsoft OAuth2模板不存在")
	}
	
	if microsoftTemplate.TokenURL != "https://login.microsoftonline.com/common/oauth2/v2.0/token" {
		t.Errorf("Microsoft令牌URL不正确")
	}
	
	// 测试Discord模板
	discordTemplate, exists := OAuth2ProviderTemplates["discord"]
	if !exists {
		t.Error("Discord OAuth2模板不存在")
	}
	
	if discordTemplate.Scopes != "identify email" {
		t.Errorf("Discord作用域不正确，期望: identify email, 实际: %s", discordTemplate.Scopes)
	}
	
	// 测试GitLab模板
	gitlabTemplate, exists := OAuth2ProviderTemplates["gitlab"]
	if !exists {
		t.Error("GitLab OAuth2模板不存在")
	}
	
	if gitlabTemplate.UserInfoURL != "https://gitlab.com/api/v4/user" {
		t.Errorf("GitLab用户信息URL不正确，期望: https://gitlab.com/api/v4/user, 实际: %s", gitlabTemplate.UserInfoURL)
	}
}

func TestBaseOAuth2Provider(t *testing.T) {
	config := OAuth2Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
		AuthURL:      "https://example.com/oauth/authorize",
		TokenURL:     "https://example.com/oauth/token",
		UserInfoURL:  "https://example.com/oauth/userinfo",
		Scopes:       "read write",
		Enabled:      true,
	}
	
	provider := &BaseOAuth2Provider{
		Name:   "test",
		Config: config,
	}
	
	// 测试基本方法
	if provider.GetName() != "test" {
		t.Errorf("提供商名称不正确，期望: test, 实际: %s", provider.GetName())
	}
	
	if provider.GetClientID() != "test_client_id" {
		t.Errorf("Client ID不正确，期望: test_client_id, 实际: %s", provider.GetClientID())
	}
	
	if !provider.IsEnabled() {
		t.Error("提供商应该是启用状态")
	}
	
	// 测试生成授权URL
	authURL := provider.GetAuthURL("state123", "http://localhost/callback")
	if authURL == "" {
		t.Error("授权URL不能为空")
	}
	
	// 验证授权URL包含必要参数
	if !contains(authURL, "client_id=test_client_id") {
		t.Error("授权URL应该包含client_id参数")
	}
	
	if !contains(authURL, "state=state123") {
		t.Error("授权URL应该包含state参数")
	}
	
	if !contains(authURL, "scope=read+write") {
		t.Error("授权URL应该包含scope参数")
	}
}

// 辅助函数：检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || containsAt(s, substr)))
}

func containsAt(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
