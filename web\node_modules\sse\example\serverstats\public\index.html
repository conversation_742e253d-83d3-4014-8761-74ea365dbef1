<!DOCTYPE html>
<html>
  <head>
    <style>
      body {
        font-family: Tahoma, Geneva, sans-serif;
      }

      div {
        display: inline;
      }
    </style>
    <script>
      function updateStats(memuse) {
        document.querySelector('div#rss').innerText = memuse.rss;
        document.querySelector('div#heapTotal').innerText = memuse.heapTotal;
        document.querySelector('div#heapUsed').innerText = memuse.heapUsed;
      }

      var es = new EventSource("/sse");
      es.onmessage = function (event) {
        updateStats(JSON.parse(event.data));
      };
    </script>
  </head>
  <body>
    <strong>Server Stats</strong><br>
    RSS: <div id='rss'></div><br>
    Heap total: <div id='heapTotal'></div><br>
    Heap used: <div id='heapUsed'></div><br>
  </body>
</html>
