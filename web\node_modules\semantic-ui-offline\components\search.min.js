!function(q,A,E,D){"use strict";A=void 0!==A&&A.Math==Math?A:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),q.fn.search=function(o){var C,x=q(this),w=x.selector||"",S=(new Date).getTime(),F=[],j=o,T="string"==typeof j,k=[].slice.call(arguments,1);return q(this).each(function(){var u=q.isPlainObject(o)?q.extend(!0,{},q.fn.search.settings,o):q.extend({},q.fn.search.settings),f=u.className,l=u.metadata,d=u.regExp,i=u.fields,g=u.selector,h=u.error,e=u.namespace,n="."+e,t=e+"-module",p=q(this),m=p.find(g.prompt),s=p.find(g.searchButton),r=p.find(g.results),a=p.find(g.result),v=(p.find(g.category),this),c=p.data(t),y=!1,b=!1,R={initialize:function(){R.verbose("Initializing module"),R.get.settings(),R.determine.searchFields(),R.bind.events(),R.set.type(),R.create.results(),R.instantiate()},instantiate:function(){R.verbose("Storing instance of module",R),c=R,p.data(t,R)},destroy:function(){R.verbose("Destroying instance"),p.off(n).removeData(t)},refresh:function(){R.debug("Refreshing selector cache"),m=p.find(g.prompt),s=p.find(g.searchButton),p.find(g.category),r=p.find(g.results),a=p.find(g.result)},refreshResults:function(){r=p.find(g.results),a=p.find(g.result)},bind:{events:function(){R.verbose("Binding events to search"),u.automatic&&(p.on(R.get.inputEvent()+n,g.prompt,R.event.input),m.attr("autocomplete","off")),p.on("focus"+n,g.prompt,R.event.focus).on("blur"+n,g.prompt,R.event.blur).on("keydown"+n,g.prompt,R.handleKeyboard).on("click"+n,g.searchButton,R.query).on("mousedown"+n,g.results,R.event.result.mousedown).on("mouseup"+n,g.results,R.event.result.mouseup).on("click"+n,g.result,R.event.result.click)}},determine:{searchFields:function(){o&&o.searchFields!==D&&(u.searchFields=o.searchFields)}},event:{input:function(){u.searchDelay?(clearTimeout(R.timer),R.timer=setTimeout(function(){R.is.focused()&&R.query()},u.searchDelay)):R.query()},focus:function(){R.set.focus(),u.searchOnFocus&&R.has.minimumCharacters()&&R.query(function(){R.can.show()&&R.showResults()})},blur:function(e){function t(){R.cancel.query(),R.remove.focus(),R.timer=setTimeout(R.hideResults,u.hideDelay)}var s=E.activeElement===this;s||(b=!1,R.resultsClicked?(R.debug("Determining if user action caused search to close"),p.one("click.close"+n,g.results,function(e){R.is.inMessage(e)||y?m.focus():(y=!1,R.is.animating()||R.is.hidden()||t())})):(R.debug("Input blurred without user action, closing results"),t()))},result:{mousedown:function(){R.resultsClicked=!0},mouseup:function(){R.resultsClicked=!1},click:function(e){R.debug("Search result selected");var t=q(this),s=t.find(g.title).eq(0),n=t.is("a[href]")?t:t.find("a[href]").eq(0),r=n.attr("href")||!1,i=n.attr("target")||!1,a=(s.html(),0<s.length&&s.text()),c=R.get.results(),o=t.data(l.result)||R.get.result(a,c);if(q.isFunction(u.onSelect)&&!1===u.onSelect.call(v,o,c))return R.debug("Custom onSelect callback cancelled default select action"),void(y=!0);R.hideResults(),a&&R.set.value(a),r&&(R.verbose("Opening search link found in result",n),"_blank"==i||e.ctrlKey?A.open(r):A.location.href=r)}}},handleKeyboard:function(e){var t,s=p.find(g.result),n=p.find(g.category),r=s.filter("."+f.active),i=s.index(r),a=s.length,c=0<r.length,o=e.which,u=13,l=38,d=40;if(o==27&&(R.verbose("Escape key pressed, blurring search field"),R.hideResults(),b=!0),R.is.visible())if(o==u){if(R.verbose("Enter key pressed, selecting active result"),0<s.filter("."+f.active).length)return R.event.result.click.call(s.filter("."+f.active),e),e.preventDefault(),!1}else o==l&&c?(R.verbose("Up key pressed, changing active result"),t=i-1<0?i:i-1,n.removeClass(f.active),s.removeClass(f.active).eq(t).addClass(f.active).closest(n).addClass(f.active),e.preventDefault()):o==d&&(R.verbose("Down key pressed, changing active result"),t=a<=i+1?i:i+1,n.removeClass(f.active),s.removeClass(f.active).eq(t).addClass(f.active).closest(n).addClass(f.active),e.preventDefault());else o==u&&(R.verbose("Enter key pressed, executing query"),R.query(),R.set.buttonPressed(),m.one("keyup",R.remove.buttonFocus))},setup:{api:function(t,s){var e={debug:u.debug,on:!1,cache:u.cache,action:"search",urlData:{query:t},onSuccess:function(e){R.parse.response.call(v,e,t),s()},onFailure:function(){R.displayMessage(h.serverError),s()},onAbort:function(e){},onError:R.error};q.extend(!0,e,u.apiSettings),R.verbose("Setting up API request",e),p.api(e)}},can:{useAPI:function(){return q.fn.api!==D},show:function(){return R.is.focused()&&!R.is.visible()&&!R.is.empty()},transition:function(){return u.transition&&q.fn.transition!==D&&p.transition("is supported")}},is:{animating:function(){return r.hasClass(f.animating)},hidden:function(){return r.hasClass(f.hidden)},inMessage:function(e){if(e.target){var t=q(e.target);return q.contains(E.documentElement,e.target)&&0<t.closest(g.message).length}},empty:function(){return""===r.html()},visible:function(){return 0<r.filter(":visible").length},focused:function(){return 0<m.filter(":focus").length}},get:{settings:function(){q.isPlainObject(o)&&o.searchFullText&&(u.fullTextSearch=o.searchFullText,R.error(u.error.oldSearchSyntax,v))},inputEvent:function(){var e=m[0];return e!==D&&e.oninput!==D?"input":e!==D&&e.onpropertychange!==D?"propertychange":"keyup"},value:function(){return m.val()},results:function(){return p.data(l.results)},result:function(s,e){var n=["title","id"],r=!1;return s=s!==D?s:R.get.value(),e=e!==D?e:R.get.results(),"category"===u.type?(R.debug("Finding result that matches",s),q.each(e,function(e,t){if(q.isArray(t.results)&&(r=R.search.object(s,t.results,n)[0]))return!1})):(R.debug("Finding result in results object",s),r=R.search.object(s,e,n)[0]),r||!1}},select:{firstResult:function(){R.verbose("Selecting first result"),a.first().addClass(f.active)}},set:{focus:function(){p.addClass(f.focus)},loading:function(){p.addClass(f.loading)},value:function(e){R.verbose("Setting search input value",e),m.val(e)},type:function(e){e=e||u.type,"category"==u.type&&p.addClass(u.type)},buttonPressed:function(){s.addClass(f.pressed)}},remove:{loading:function(){p.removeClass(f.loading)},focus:function(){p.removeClass(f.focus)},buttonPressed:function(){s.removeClass(f.pressed)}},query:function(e){e=q.isFunction(e)?e:function(){};var t=R.get.value(),s=R.read.cache(t);e=e||function(){},R.has.minimumCharacters()?(s?(R.debug("Reading result from cache",t),R.save.results(s.results),R.addResults(s.html),R.inject.id(s.results),e()):(R.debug("Querying for",t),q.isPlainObject(u.source)||q.isArray(u.source)?(R.search.local(t),e()):R.can.useAPI()?R.search.remote(t,e):(R.error(h.source),e())),u.onSearchQuery.call(v,t)):R.hideResults()},search:{local:function(e){var t,s=R.search.object(e,u.content);R.set.loading(),R.save.results(s),R.debug("Returned full local search results",s),0<u.maxResults&&(R.debug("Using specified max results",s),s=s.slice(0,u.maxResults)),"category"==u.type&&(s=R.create.categoryResults(s)),t=R.generateResults({results:s}),R.remove.loading(),R.addResults(t),R.inject.id(s),R.write.cache(e,{html:t,results:s})},remote:function(e,t){t=q.isFunction(t)?t:function(){},p.api("is loading")&&p.api("abort"),R.setup.api(e,t),p.api("query")},object:function(n,t,e){function r(e,t){var s=-1==q.inArray(t,i),n=-1==q.inArray(t,c),r=-1==q.inArray(t,a);s&&n&&r&&e.push(t)}var i=[],a=[],c=[],s=n.toString().replace(d.escape,"\\$&"),o=new RegExp(d.beginsWith+s,"i");return t=t||u.source,e=e!==D?e:u.searchFields,q.isArray(e)||(e=[e]),t===D||!1===t?(R.error(h.source),[]):(q.each(e,function(e,s){q.each(t,function(e,t){"string"==typeof t[s]&&(-1!==t[s].search(o)?r(i,t):"exact"===u.fullTextSearch&&R.exactSearch(n,t[s])?r(a,t):1==u.fullTextSearch&&R.fuzzySearch(n,t[s])&&r(c,t))})}),q.merge(a,c),q.merge(i,a),i)}},exactSearch:function(e,t){return e=e.toLowerCase(),-1<(t=t.toLowerCase()).indexOf(e)},fuzzySearch:function(e,t){var s=t.length,n=e.length;if("string"!=typeof e)return!1;if(e=e.toLowerCase(),t=t.toLowerCase(),s<n)return!1;if(n===s)return e===t;e:for(var r=0,i=0;r<n;r++){for(var a=e.charCodeAt(r);i<s;)if(t.charCodeAt(i++)===a)continue e;return!1}return!0},parse:{response:function(e,t){var s=R.generateResults(e);R.verbose("Parsing server response",e),e!==D&&t!==D&&e[i.results]!==D&&(R.addResults(s),R.inject.id(e[i.results]),R.write.cache(t,{html:s,results:e[i.results]}),R.save.results(e[i.results]))}},cancel:{query:function(){R.can.useAPI()&&p.api("abort")}},has:{minimumCharacters:function(){return R.get.value().length>=u.minCharacters},results:function(){return 0!==r.length&&""!=r.html()}},clear:{cache:function(e){var t=p.data(l.cache);e?e&&t&&t[e]&&(R.debug("Removing value from cache",e),delete t[e],p.data(l.cache,t)):(R.debug("Clearing cache",e),p.removeData(l.cache))}},read:{cache:function(e){var t=p.data(l.cache);return!!u.cache&&(R.verbose("Checking cache for generated html for query",e),"object"==typeof t&&t[e]!==D&&t[e])}},create:{categoryResults:function(e){var s={};return q.each(e,function(e,t){t.category&&(s[t.category]===D?(R.verbose("Creating new category of results",t.category),s[t.category]={name:t.category,results:[t]}):s[t.category].results.push(t))}),s},id:function(e,t){var s,n=e+1;return t!==D?(s=String.fromCharCode(97+t)+n,R.verbose("Creating category result id",s)):(s=n,R.verbose("Creating result id",s)),s},results:function(){0===r.length&&(r=q("<div />").addClass(f.results).appendTo(p))}},inject:{result:function(e,t,s){R.verbose("Injecting result into results");var n=s!==D?r.children().eq(s).children(g.results).first().children(g.result).eq(t):r.children(g.result).eq(t);R.verbose("Injecting results metadata",n),n.data(l.result,e)},id:function(n){R.debug("Injecting unique ids into results");var r=0,i=0;return"category"===u.type?q.each(n,function(e,n){i=0,q.each(n.results,function(e,t){var s=n.results[e];s.id===D&&(s.id=R.create.id(i,r)),R.inject.result(s,i,r),i++}),r++}):q.each(n,function(e,t){var s=n[e];s.id===D&&(s.id=R.create.id(i)),R.inject.result(s,i),i++}),n}},save:{results:function(e){R.verbose("Saving current search results to metadata",e),p.data(l.results,e)}},write:{cache:function(e,t){var s=p.data(l.cache)!==D?p.data(l.cache):{};u.cache&&(R.verbose("Writing generated html to cache",e,t),s[e]=t,p.data(l.cache,s))}},addResults:function(e){if(q.isFunction(u.onResultsAdd)&&!1===u.onResultsAdd.call(r,e))return R.debug("onResultsAdd callback cancelled default action"),!1;e?(r.html(e),R.refreshResults(),u.selectFirstResult&&R.select.firstResult(),R.showResults()):R.hideResults(function(){r.empty()})},showResults:function(e){e=q.isFunction(e)?e:function(){},b||!R.is.visible()&&R.has.results()&&(R.can.transition()?(R.debug("Showing results with css animations"),r.transition({animation:u.transition+" in",debug:u.debug,verbose:u.verbose,duration:u.duration,onComplete:function(){e()},queue:!0})):(R.debug("Showing results with javascript"),r.stop().fadeIn(u.duration,u.easing)),u.onResultsOpen.call(r))},hideResults:function(e){e=q.isFunction(e)?e:function(){},R.is.visible()&&(R.can.transition()?(R.debug("Hiding results with css animations"),r.transition({animation:u.transition+" out",debug:u.debug,verbose:u.verbose,duration:u.duration,onComplete:function(){e()},queue:!0})):(R.debug("Hiding results with javascript"),r.stop().fadeOut(u.duration,u.easing)),u.onResultsClose.call(r))},generateResults:function(e){R.debug("Generating html from response",e);var t=u.templates[u.type],s=q.isPlainObject(e[i.results])&&!q.isEmptyObject(e[i.results]),n=q.isArray(e[i.results])&&0<e[i.results].length,r="";return s||n?(0<u.maxResults&&(s?"standard"==u.type&&R.error(h.maxResults):e[i.results]=e[i.results].slice(0,u.maxResults)),q.isFunction(t)?r=t(e,i):R.error(h.noTemplate,!1)):u.showNoResults&&(r=R.displayMessage(h.noResults,"empty")),u.onResults.call(v,e),r},displayMessage:function(e,t){return t=t||"standard",R.debug("Displaying message",e,t),R.addResults(u.templates.message(e,t)),u.templates.message(e,t)},setting:function(e,t){if(q.isPlainObject(e))q.extend(!0,u,e);else{if(t===D)return u[e];u[e]=t}},internal:function(e,t){if(q.isPlainObject(e))q.extend(!0,R,e);else{if(t===D)return R[e];R[e]=t}},debug:function(){!u.silent&&u.debug&&(u.performance?R.performance.log(arguments):(R.debug=Function.prototype.bind.call(console.info,console,u.name+":"),R.debug.apply(console,arguments)))},verbose:function(){!u.silent&&u.verbose&&u.debug&&(u.performance?R.performance.log(arguments):(R.verbose=Function.prototype.bind.call(console.info,console,u.name+":"),R.verbose.apply(console,arguments)))},error:function(){u.silent||(R.error=Function.prototype.bind.call(console.error,console,u.name+":"),R.error.apply(console,arguments))},performance:{log:function(e){var t,s;u.performance&&(s=(t=(new Date).getTime())-(S||t),S=t,F.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:v,"Execution Time":s})),clearTimeout(R.performance.timer),R.performance.timer=setTimeout(R.performance.display,500)},display:function(){var e=u.name+":",s=0;S=!1,clearTimeout(R.performance.timer),q.each(F,function(e,t){s+=t["Execution Time"]}),e+=" "+s+"ms",w&&(e+=" '"+w+"'"),1<x.length&&(e+=" ("+x.length+")"),(console.group!==D||console.table!==D)&&0<F.length&&(console.groupCollapsed(e),console.table?console.table(F):q.each(F,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),F=[]}},invoke:function(n,e,t){var r,i,s,a=c;return e=e||k,t=v||t,"string"==typeof n&&a!==D&&(n=n.split(/[\. ]/),r=n.length-1,q.each(n,function(e,t){var s=e!=r?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(q.isPlainObject(a[s])&&e!=r)a=a[s];else{if(a[s]!==D)return i=a[s],!1;if(!q.isPlainObject(a[t])||e==r)return a[t]!==D&&(i=a[t]),!1;a=a[t]}})),q.isFunction(i)?s=i.apply(t,e):i!==D&&(s=i),q.isArray(C)?C.push(s):C!==D?C=[C,s]:s!==D&&(C=s),i}};T?(c===D&&R.initialize(),R.invoke(j)):(c!==D&&c.invoke("destroy"),R.initialize())}),C!==D?C:this},q.fn.search.settings={name:"Search",namespace:"search",silent:!1,debug:!1,verbose:!1,performance:!0,type:"standard",minCharacters:1,selectFirstResult:!1,apiSettings:!1,source:!1,searchOnFocus:!0,searchFields:["title","description"],displayField:"",fullTextSearch:"exact",automatic:!0,hideDelay:0,searchDelay:200,maxResults:7,cache:!0,showNoResults:!0,transition:"scale",duration:200,easing:"easeOutExpo",onSelect:!1,onResultsAdd:!1,onSearchQuery:function(e){},onResults:function(e){},onResultsOpen:function(){},onResultsClose:function(){},className:{animating:"animating",active:"active",empty:"empty",focus:"focus",hidden:"hidden",loading:"loading",results:"results",pressed:"down"},error:{source:"Cannot search. No source used, and Semantic API module was not included",noResults:"Your search returned no results",logging:"Error in debug logging, exiting.",noEndpoint:"No search endpoint was specified",noTemplate:"A valid template name was not specified.",oldSearchSyntax:"searchFullText setting has been renamed fullTextSearch for consistency, please adjust your settings.",serverError:"There was an issue querying the server.",maxResults:"Results must be an array to use maxResults setting",method:"The method you called is not defined."},metadata:{cache:"cache",results:"results",result:"result"},regExp:{escape:/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,beginsWith:"(?:s|^)"},fields:{categories:"results",categoryName:"name",categoryResults:"results",description:"description",image:"image",price:"price",results:"results",title:"title",url:"url",action:"action",actionText:"text",actionURL:"url"},selector:{prompt:".prompt",searchButton:".search.button",results:".results",message:".results > .message",category:".category",result:".result",title:".title, .name"},templates:{escape:function(e){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};return/[&<>"'`]/.test(e)?e.replace(/[&<>"'`]/g,function(e){return t[e]}):e},message:function(e,t){var s="";return e!==D&&t!==D&&(s+='<div class="message '+t+'">',s+="empty"==t?'<div class="header">No Results</div class="header"><div class="description">'+e+'</div class="description">':' <div class="description">'+e+"</div>",s+="</div>"),s},category:function(e,s){var n="";q.fn.search.settings.templates.escape;return e[s.categoryResults]!==D&&(q.each(e[s.categoryResults],function(e,t){t[s.results]!==D&&0<t.results.length&&(n+='<div class="category">',t[s.categoryName]!==D&&(n+='<div class="name">'+t[s.categoryName]+"</div>"),n+='<div class="results">',q.each(t.results,function(e,t){t[s.url]?n+='<a class="result" href="'+t[s.url]+'">':n+='<a class="result">',t[s.image]!==D&&(n+='<div class="image"> <img src="'+t[s.image]+'"></div>'),n+='<div class="content">',t[s.price]!==D&&(n+='<div class="price">'+t[s.price]+"</div>"),t[s.title]!==D&&(n+='<div class="title">'+t[s.title]+"</div>"),t[s.description]!==D&&(n+='<div class="description">'+t[s.description]+"</div>"),n+="</div>",n+="</a>"}),n+="</div>",n+="</div>")}),e[s.action]&&(n+='<a href="'+e[s.action][s.actionURL]+'" class="action">'+e[s.action][s.actionText]+"</a>"),n)},standard:function(e,s){var n="";return e[s.results]!==D&&(q.each(e[s.results],function(e,t){t[s.url]?n+='<a class="result" href="'+t[s.url]+'">':n+='<a class="result">',t[s.image]!==D&&(n+='<div class="image"> <img src="'+t[s.image]+'"></div>'),n+='<div class="content">',t[s.price]!==D&&(n+='<div class="price">'+t[s.price]+"</div>"),t[s.title]!==D&&(n+='<div class="title">'+t[s.title]+"</div>"),t[s.description]!==D&&(n+='<div class="description">'+t[s.description]+"</div>"),n+="</div>",n+="</a>"}),e[s.action]&&(n+='<a href="'+e[s.action][s.actionURL]+'" class="action">'+e[s.action][s.actionText]+"</a>"),n)}}}}(jQuery,window,document);