!function(H,e,T,Z){"use strict";e=void 0!==e&&e.Math==Math?e:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),H.fn.shape=function(v){var b,x=H(this),y=(H("body"),(new Date).getTime()),S=[],w=v,C="string"==typeof w,W=[].slice.call(arguments,1),F=e.requestAnimationFrame||e.mozRequestAnimationFrame||e.webkitRequestAnimationFrame||e.msRequestAnimationFrame||function(e){setTimeout(e,0)};return x.each(function(){var n,a,t=x.selector||"",o=H.isPlainObject(v)?H.extend(!0,{},H.fn.shape.settings,v):H.extend({},H.fn.shape.settings),e=o.namespace,r=o.selector,i=o.error,s=o.className,d="."+e,l="module-"+e,u=H(this),c=u.find(r.sides),g=u.find(r.side),f=!1,m=this,h=u.data(l),p={initialize:function(){p.verbose("Initializing module for",m),p.set.defaultSide(),p.instantiate()},instantiate:function(){p.verbose("Storing instance of module",p),h=p,u.data(l,h)},destroy:function(){p.verbose("Destroying previous module for",m),u.removeData(l).off(d)},refresh:function(){p.verbose("Refreshing selector cache for",m),u=H(m),c=H(this).find(r.shape),g=H(this).find(r.side)},repaint:function(){p.verbose("Forcing repaint event");(c[0]||T.createElement("div")).offsetWidth},animate:function(e,t){p.verbose("Animating box with properties",e),t=t||function(e){p.verbose("Executing animation callback"),e!==Z&&e.stopPropagation(),p.reset(),p.set.active()},o.beforeChange.call(a[0]),p.get.transitionEvent()?(p.verbose("Starting CSS animation"),u.addClass(s.animating),c.css(e).one(p.get.transitionEvent(),t),p.set.duration(o.duration),F(function(){u.addClass(s.animating),n.addClass(s.hidden)})):t()},queue:function(e){p.debug("Queueing animation of",e),c.one(p.get.transitionEvent(),function(){p.debug("Executing queued animation"),setTimeout(function(){u.shape(e)},0)})},reset:function(){p.verbose("Animating states reset"),u.removeClass(s.animating).attr("style","").removeAttr("style"),c.attr("style","").removeAttr("style"),g.attr("style","").removeAttr("style").removeClass(s.hidden),a.removeClass(s.animating).attr("style","").removeAttr("style")},is:{complete:function(){return g.filter("."+s.active)[0]==a[0]},animating:function(){return u.hasClass(s.animating)}},set:{defaultSide:function(){n=u.find("."+o.className.active),a=0<n.next(r.side).length?n.next(r.side):u.find(r.side).first(),f=!1,p.verbose("Active side set to",n),p.verbose("Next side set to",a)},duration:function(e){e="number"==typeof(e=e||o.duration)?e+"ms":e,p.verbose("Setting animation duration",e),!o.duration&&0!==o.duration||c.add(g).css({"-webkit-transition-duration":e,"-moz-transition-duration":e,"-ms-transition-duration":e,"-o-transition-duration":e,"transition-duration":e})},currentStageSize:function(){var e=u.find("."+o.className.active),t=e.outerWidth(!0),i=e.outerHeight(!0);u.css({width:t,height:i})},stageSize:function(){var e=u.clone().addClass(s.loading),t=e.find("."+o.className.active),i=f?e.find(r.side).eq(f):0<t.next(r.side).length?t.next(r.side):e.find(r.side).first(),n="next"==o.width?i.outerWidth(!0):"initial"==o.width?u.width():o.width,a="next"==o.height?i.outerHeight(!0):"initial"==o.height?u.height():o.height;t.removeClass(s.active),i.addClass(s.active),e.insertAfter(u),e.remove(),"auto"!=o.width&&(u.css("width",n+o.jitter),p.verbose("Specifying width during animation",n)),"auto"!=o.height&&(u.css("height",a+o.jitter),p.verbose("Specifying height during animation",a))},nextSide:function(e){f=e,a=g.filter(e),f=g.index(a),0===a.length&&(p.set.defaultSide(),p.error(i.side)),p.verbose("Next side manually set to",a)},active:function(){p.verbose("Setting new side to active",a),g.removeClass(s.active),a.addClass(s.active),o.onChange.call(a[0]),p.set.defaultSide()}},flip:{up:function(){var e;!p.is.complete()||p.is.animating()||o.allowRepeats?p.is.animating()?p.queue("flip up"):(p.debug("Flipping up",a),e=p.get.transform.up(),p.set.stageSize(),p.stage.above(),p.animate(e)):p.debug("Side already visible",a)},down:function(){var e;!p.is.complete()||p.is.animating()||o.allowRepeats?p.is.animating()?p.queue("flip down"):(p.debug("Flipping down",a),e=p.get.transform.down(),p.set.stageSize(),p.stage.below(),p.animate(e)):p.debug("Side already visible",a)},left:function(){var e;!p.is.complete()||p.is.animating()||o.allowRepeats?p.is.animating()?p.queue("flip left"):(p.debug("Flipping left",a),e=p.get.transform.left(),p.set.stageSize(),p.stage.left(),p.animate(e)):p.debug("Side already visible",a)},right:function(){var e;!p.is.complete()||p.is.animating()||o.allowRepeats?p.is.animating()?p.queue("flip right"):(p.debug("Flipping right",a),e=p.get.transform.right(),p.set.stageSize(),p.stage.right(),p.animate(e)):p.debug("Side already visible",a)},over:function(){!p.is.complete()||p.is.animating()||o.allowRepeats?p.is.animating()?p.queue("flip over"):(p.debug("Flipping over",a),p.set.stageSize(),p.stage.behind(),p.animate(p.get.transform.over())):p.debug("Side already visible",a)},back:function(){!p.is.complete()||p.is.animating()||o.allowRepeats?p.is.animating()?p.queue("flip back"):(p.debug("Flipping back",a),p.set.stageSize(),p.stage.behind(),p.animate(p.get.transform.back())):p.debug("Side already visible",a)}},get:{transform:{up:function(){return{transform:"translateY("+-(n.outerHeight(!0)-a.outerHeight(!0))/2+"px) translateZ("+-n.outerHeight(!0)/2+"px) rotateX(-90deg)"}},down:function(){return{transform:"translateY("+-(n.outerHeight(!0)-a.outerHeight(!0))/2+"px) translateZ("+-n.outerHeight(!0)/2+"px) rotateX(90deg)"}},left:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) translateZ("+-n.outerWidth(!0)/2+"px) rotateY(90deg)"}},right:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) translateZ("+-n.outerWidth(!0)/2+"px) rotateY(-90deg)"}},over:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) rotateY(180deg)"}},back:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) rotateY(-180deg)"}}},transitionEvent:function(){var e,t=T.createElement("element"),i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in i)if(t.style[e]!==Z)return i[e]},nextSide:function(){return 0<n.next(r.side).length?n.next(r.side):u.find(r.side).first()}},stage:{above:function(){var e={origin:(n.outerHeight(!0)-a.outerHeight(!0))/2,depth:{active:a.outerHeight(!0)/2,next:n.outerHeight(!0)/2}};p.verbose("Setting the initial animation position as above",a,e),c.css({transform:"translateZ(-"+e.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+e.depth.active+"px)"}),a.addClass(s.animating).css({top:e.origin+"px",transform:"rotateX(90deg) translateZ("+e.depth.next+"px)"})},below:function(){var e={origin:(n.outerHeight(!0)-a.outerHeight(!0))/2,depth:{active:a.outerHeight(!0)/2,next:n.outerHeight(!0)/2}};p.verbose("Setting the initial animation position as below",a,e),c.css({transform:"translateZ(-"+e.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+e.depth.active+"px)"}),a.addClass(s.animating).css({top:e.origin+"px",transform:"rotateX(-90deg) translateZ("+e.depth.next+"px)"})},left:function(){var e=n.outerWidth(!0),t=a.outerWidth(!0),i={origin:(e-t)/2,depth:{active:t/2,next:e/2}};p.verbose("Setting the initial animation position as left",a,i),c.css({transform:"translateZ(-"+i.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+i.depth.active+"px)"}),a.addClass(s.animating).css({left:i.origin+"px",transform:"rotateY(-90deg) translateZ("+i.depth.next+"px)"})},right:function(){var e=n.outerWidth(!0),t=a.outerWidth(!0),i={origin:(e-t)/2,depth:{active:t/2,next:e/2}};p.verbose("Setting the initial animation position as left",a,i),c.css({transform:"translateZ(-"+i.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+i.depth.active+"px)"}),a.addClass(s.animating).css({left:i.origin+"px",transform:"rotateY(90deg) translateZ("+i.depth.next+"px)"})},behind:function(){var e=n.outerWidth(!0),t=a.outerWidth(!0),i={origin:(e-t)/2,depth:{active:t/2,next:e/2}};p.verbose("Setting the initial animation position as behind",a,i),n.css({transform:"rotateY(0deg)"}),a.addClass(s.animating).css({left:i.origin+"px",transform:"rotateY(-180deg)"})}},setting:function(e,t){if(p.debug("Changing setting",e,t),H.isPlainObject(e))H.extend(!0,o,e);else{if(t===Z)return o[e];H.isPlainObject(o[e])?H.extend(!0,o[e],t):o[e]=t}},internal:function(e,t){if(H.isPlainObject(e))H.extend(!0,p,e);else{if(t===Z)return p[e];p[e]=t}},debug:function(){!o.silent&&o.debug&&(o.performance?p.performance.log(arguments):(p.debug=Function.prototype.bind.call(console.info,console,o.name+":"),p.debug.apply(console,arguments)))},verbose:function(){!o.silent&&o.verbose&&o.debug&&(o.performance?p.performance.log(arguments):(p.verbose=Function.prototype.bind.call(console.info,console,o.name+":"),p.verbose.apply(console,arguments)))},error:function(){o.silent||(p.error=Function.prototype.bind.call(console.error,console,o.name+":"),p.error.apply(console,arguments))},performance:{log:function(e){var t,i;o.performance&&(i=(t=(new Date).getTime())-(y||t),y=t,S.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:m,"Execution Time":i})),clearTimeout(p.performance.timer),p.performance.timer=setTimeout(p.performance.display,500)},display:function(){var e=o.name+":",i=0;y=!1,clearTimeout(p.performance.timer),H.each(S,function(e,t){i+=t["Execution Time"]}),e+=" "+i+"ms",t&&(e+=" '"+t+"'"),1<x.length&&(e+=" ("+x.length+")"),(console.group!==Z||console.table!==Z)&&0<S.length&&(console.groupCollapsed(e),console.table?console.table(S):H.each(S,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),S=[]}},invoke:function(n,e,t){var a,o,i,r=h;return e=e||W,t=m||t,"string"==typeof n&&r!==Z&&(n=n.split(/[\. ]/),a=n.length-1,H.each(n,function(e,t){var i=e!=a?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(H.isPlainObject(r[i])&&e!=a)r=r[i];else{if(r[i]!==Z)return o=r[i],!1;if(!H.isPlainObject(r[t])||e==a)return r[t]!==Z&&(o=r[t]),!1;r=r[t]}})),H.isFunction(o)?i=o.apply(t,e):o!==Z&&(i=o),H.isArray(b)?b.push(i):b!==Z?b=[b,i]:i!==Z&&(b=i),o}};C?(h===Z&&p.initialize(),p.invoke(w)):(h!==Z&&h.invoke("destroy"),p.initialize())}),b!==Z?b:this},H.fn.shape.settings={name:"Shape",silent:!1,debug:!1,verbose:!1,jitter:0,performance:!0,namespace:"shape",width:"initial",height:"initial",beforeChange:function(){},onChange:function(){},allowRepeats:!1,duration:!1,error:{side:"You tried to switch to a side that does not exist.",method:"The method you called is not defined"},className:{animating:"animating",hidden:"hidden",loading:"loading",active:"active"},selector:{sides:".sides",side:".side"}}}(jQuery,window,document);