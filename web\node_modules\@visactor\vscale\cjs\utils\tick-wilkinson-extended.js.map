{"version": 3, "sources": ["../src/utils/tick-wilkinson-extended.ts"], "names": [], "mappings": ";;;AAEa,QAAA,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAE9C,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAE5B,QAAA,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAE3D,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;AAGjC,SAAS,YAAY,CAAC,CAAS;IAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7D,CAAC;AAED,SAAS,GAAG,CAAC,CAAS,EAAE,CAAS;IAC/B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,KAAK,CAAC,CAAS;IACtB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACrC,CAAC;AAED,SAAS,UAAU,CAAC,CAAS,EAAE,CAAW,EAAE,CAAS,EAAE,IAAY,EAAE,IAAY,EAAE,KAAa;IAC9F,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACnB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC3B,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;QAC1D,CAAC,GAAG,CAAC,CAAC;KACP;IACD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,aAAa,CAAC,CAAS,EAAE,CAAW,EAAE,CAAS;IACtD,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACnB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,GAAG,CAAC,CAAC;IACZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,OAAO,CAAC,CAAS,EAAE,CAAS,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;IAC3F,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACnE,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,UAAU,CAAC,CAAS,EAAE,CAAS;IACtC,IAAI,CAAC,IAAI,CAAC,EAAE;QACV,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9B;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,QAAQ,CAAC,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;IACtE,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1B,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AACpF,CAAC;AAED,SAAS,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,IAAY;IAC3D,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1B,IAAI,IAAI,GAAG,KAAK,EAAE;QAChB,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;KAC3C;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,UAAU;IACjB,OAAO,CAAC,CAAC;AACX,CAAC;AAYM,MAAM,iBAAiB,GAA+B,CAAC,IAAY,EAAE,IAAY,EAAE,IAAY,CAAC,EAAE,OAAO,EAAE,EAAE;IAClH,MAAM,EAAE,SAAS,GAAG,IAAI,EAAE,CAAC,GAAG,iBAAS,EAAE,CAAC,GAAG,SAAS,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;IACzE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE;QAC1G,OAAO,EAAE,CAAC;KACX;IAGD,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE;QAClC,OAAO,CAAC,IAAI,CAAC,CAAC;KACf;IAED,MAAM,IAAI,GAAG;QACX,KAAK,EAAE,CAAC,CAAC;QACT,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,KAAK,EAAE,CAAC;KACT,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,QAAQ,EAAE;QAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;gBAC/C,CAAC,GAAG,QAAQ,CAAC;gBACb,MAAM;aACP;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,OAAO,CAAC,GAAG,QAAQ,EAAE;gBACnB,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;oBACpD,MAAM;iBACP;gBAED,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAErC,OAAO,CAAC,GAAG,QAAQ,EAAE;oBACnB,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBAC7B,MAAM,EAAE,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAEnD,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;wBACzD,MAAM;qBACP;oBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;oBAE5C,IAAI,QAAQ,IAAI,QAAQ,EAAE;wBACxB,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;wBAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE;4BAClC,MAAM,KAAK,GAAG,QAAQ,GAAG,CAAC,CAAC;4BAC3B,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;4BAChC,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;4BACnC,MAAM,KAAK,GAAG,IAAI,CAAC;4BAEnB,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;4BACjD,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;4BAC3C,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;4BAChD,MAAM,CAAC,GAAG,UAAU,EAAE,CAAC;4BAEvB,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;4BACxD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE;gCACxE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gCACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gCACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gCACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;6BACpB;yBACF;qBACF;oBACD,CAAC,IAAI,CAAC,CAAC;iBACR;gBACD,CAAC,IAAI,CAAC,CAAC;aACR;SACF;QACD,CAAC,IAAI,CAAC,CAAC;KACR;IAGD,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAIvC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/D,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;IAGnC,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE;QACrC,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;KAC/C;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAlGW,QAAA,iBAAiB,qBAkG5B", "file": "tick-wil<PERSON><PERSON>-extended.js", "sourcesContent": ["import type { WilkinsonExtendedTicksFunc } from '../interface';\n\nexport const DEFAULT_Q = [1, 5, 2, 2.5, 4, 3];\n\nconst DEFAULT_W = [0.25, 0.2, 0.5, 0.05];\n\nexport const ALL_Q = [1, 5, 2, 2.5, 4, 3, 1.5, 7, 6, 8, 9];\n\nconst eps = Number.EPSILON * 100;\n\n// 为了解决 js 运算的精度问题\nfunction prettyNumber(n: number) {\n  return Math.abs(n) < 1e-14 ? n : parseFloat(n.toFixed(14));\n}\n\nfunction mod(n: number, m: number) {\n  return ((n % m) + m) % m;\n}\n\nfunction round(n: number) {\n  return Math.round(n * 1e12) / 1e12;\n}\n\nfunction simplicity(q: number, Q: number[], j: number, lmin: number, lmax: number, lstep: number) {\n  const n = Q.length;\n  const i = Q.indexOf(q);\n  let v = 0;\n  const m = mod(lmin, lstep);\n  if ((m < eps || lstep - m < eps) && lmin <= 0 && lmax >= 0) {\n    v = 1;\n  }\n  return 1 - i / (n - 1) - j + v;\n}\n\nfunction simplicityMax(q: number, Q: number[], j: number) {\n  const n = Q.length;\n  const i = Q.indexOf(q);\n  const v = 1;\n  return 1 - i / (n - 1) - j + v;\n}\n\nfunction density(k: number, m: number, dMin: number, dMax: number, lMin: number, lMax: number) {\n  const r = (k - 1) / (lMax - lMin);\n  const rt = (m - 1) / (Math.max(lMax, dMax) - Math.min(dMin, lMin));\n  return 2 - Math.max(r / rt, rt / r);\n}\n\nfunction densityMax(k: number, m: number) {\n  if (k >= m) {\n    return 2 - (k - 1) / (m - 1);\n  }\n  return 1;\n}\n\nfunction coverage(dMin: number, dMax: number, lMin: number, lMax: number) {\n  const range = dMax - dMin;\n  return 1 - (0.5 * ((dMax - lMax) ** 2 + (dMin - lMin) ** 2)) / (0.1 * range) ** 2;\n}\n\nfunction coverageMax(dMin: number, dMax: number, span: number) {\n  const range = dMax - dMin;\n  if (span > range) {\n    const half = (span - range) / 2;\n    return 1 - half ** 2 / (0.1 * range) ** 2;\n  }\n  return 1;\n}\n\nfunction legibility() {\n  return 1;\n}\n\n/**\n * An Extension of Wilkinson's Algorithm for Position Tick Labels on Axes\n * https://www.yuque.com/preview/yuque/0/2019/pdf/185317/1546999150858-45c3b9c2-4e86-4223-bf1a-8a732e8195ed.pdf\n * @param dMin 最小值\n * @param dMax 最大值\n * @param n tick个数\n * @param onlyLoose 是否允许扩展min、max，不绝对强制，例如[3, 97]\n * @param Q nice numbers集合\n * @param w 四个优化组件的权重\n */\nexport const wilkinsonExtended: WilkinsonExtendedTicksFunc = (dMin: number, dMax: number, n: number = 5, options) => {\n  const { onlyLoose = true, Q = DEFAULT_Q, w = DEFAULT_W } = options || {};\n  const m = n < 0 ? 0 : Math.round(n);\n  // nan 也会导致异常\n  if (Number.isNaN(dMin) || Number.isNaN(dMax) || typeof dMin !== 'number' || typeof dMax !== 'number' || !m) {\n    return [];\n  }\n\n  // js 极大值极小值问题，差值小于 1e-15 会导致计算出错\n  if (dMax - dMin < 1e-15 || m === 1) {\n    return [dMin];\n  }\n\n  const best = {\n    score: -2,\n    lmin: 0,\n    lmax: 0,\n    lstep: 0\n  };\n\n  let j = 1;\n  while (j < Infinity) {\n    // for (const q of Q)\n    for (let i = 0; i < Q.length; i += 1) {\n      const q = Q[i];\n      const sm = simplicityMax(q, Q, j);\n      if (w[0] * sm + w[1] + w[2] + w[3] < best.score) {\n        j = Infinity;\n        break;\n      }\n      let k = 2;\n      while (k < Infinity) {\n        const dm = densityMax(k, m);\n        if (w[0] * sm + w[1] + w[2] * dm + w[3] < best.score) {\n          break;\n        }\n\n        const delta = (dMax - dMin) / (k + 1) / j / q;\n        let z = Math.ceil(Math.log10(delta));\n\n        while (z < Infinity) {\n          const step = j * q * 10 ** z;\n          const cm = coverageMax(dMin, dMax, step * (k - 1));\n\n          if (w[0] * sm + w[1] * cm + w[2] * dm + w[3] < best.score) {\n            break;\n          }\n\n          const minStart = Math.floor(dMax / step) * j - (k - 1) * j;\n          const maxStart = Math.ceil(dMin / step) * j;\n\n          if (minStart <= maxStart) {\n            const count = maxStart - minStart;\n            for (let i = 0; i <= count; i += 1) {\n              const start = minStart + i;\n              const lMin = start * (step / j);\n              const lMax = lMin + step * (k - 1);\n              const lStep = step;\n\n              const s = simplicity(q, Q, j, lMin, lMax, lStep);\n              const c = coverage(dMin, dMax, lMin, lMax);\n              const g = density(k, m, dMin, dMax, lMin, lMax);\n              const l = legibility();\n\n              const score = w[0] * s + w[1] * c + w[2] * g + w[3] * l;\n              if (score > best.score && (!onlyLoose || (lMin <= dMin && lMax >= dMax))) {\n                best.lmin = lMin;\n                best.lmax = lMax;\n                best.lstep = lStep;\n                best.score = score;\n              }\n            }\n          }\n          z += 1;\n        }\n        k += 1;\n      }\n    }\n    j += 1;\n  }\n\n  // 处理精度问题，保证这三个数没有精度问题\n  const lmax = prettyNumber(best.lmax);\n  const lmin = prettyNumber(best.lmin);\n  const lstep = prettyNumber(best.lstep);\n\n  // 加 round 是为处理 extended(0.94, 1, 5)\n  // 保证生成的 tickCount 没有精度问题\n  const tickCount = Math.floor(round((lmax - lmin) / lstep)) + 1;\n  const ticks = new Array(tickCount);\n\n  // 少用乘法：防止出现 -1.2 + 1.2 * 3 = 2.3999999999999995 的情况\n  ticks[0] = prettyNumber(lmin);\n  for (let i = 1; i < tickCount; i += 1) {\n    ticks[i] = prettyNumber(ticks[i - 1] + lstep);\n  }\n\n  return ticks;\n};\n"]}