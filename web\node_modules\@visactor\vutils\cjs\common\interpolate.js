"use strict";

function interpolateNumber(a, b) {
    return t => a * (1 - t) + b * t;
}

function interpolateNumberRound(a, b) {
    return function(t) {
        return Math.round(a * (1 - t) + b * t);
    };
}

function interpolateDate(a, b) {
    const aVal = a.valueOf(), bVal = b.valueOf(), d = new Date;
    return t => (d.setTime(aVal * (1 - t) + bVal * t), d);
}

Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.interpolateString = exports.interpolateDate = exports.interpolateNumberRound = exports.interpolateNumber = void 0, 
exports.interpolateNumber = interpolateNumber, exports.interpolateNumberRound = interpolateNumberRound, 
exports.interpolateDate = interpolateDate;

const reA = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g, reB = new RegExp(reA.source, "g");

function zero(b) {
    return function() {
        return b;
    };
}

function one(b) {
    return function(t) {
        return b(t) + "";
    };
}

function interpolateString(a, b) {
    let am, bm, bs, bi = reA.lastIndex = reB.lastIndex = 0, i = -1;
    const s = [], q = [];
    for (a += "", b += ""; (am = reA.exec(a)) && (bm = reB.exec(b)); ) (bs = bm.index) > bi && (bs = b.slice(bi, bs), 
    s[i] ? s[i] += bs : s[++i] = bs), (am = am[0]) === (bm = bm[0]) ? s[i] ? s[i] += bm : s[++i] = bm : (s[++i] = null, 
    q.push({
        i: i,
        x: interpolateNumber(am, bm)
    })), bi = reB.lastIndex;
    return bi < b.length && (bs = b.slice(bi), s[i] ? s[i] += bs : s[++i] = bs), s.length < 2 ? q[0] ? one(q[0].x) : zero(b) : (b = q.length, 
    function(t) {
        for (let o, i = 0; i < b; ++i) s[(o = q[i]).i] = o.x(t);
        return s.join("");
    });
}

exports.interpolateString = interpolateString;
//# sourceMappingURL=interpolate.js.map