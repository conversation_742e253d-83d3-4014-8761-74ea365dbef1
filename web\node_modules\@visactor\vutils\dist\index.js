(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
	typeof define === 'function' && define.amd ? define(['exports'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.VUtils = {}));
})(this, (function (exports) { 'use strict';

	function getDefaultExportFromCjs (x) {
		return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
	}

	var eventemitter3 = {exports: {}};

	(function (module) {

	  var has = Object.prototype.hasOwnProperty,
	    prefix = '~';

	  /**
	   * Constructor to create a storage for our `EE` objects.
	   * An `Events` instance is a plain object whose properties are event names.
	   *
	   * @constructor
	   * @private
	   */
	  function Events() {}

	  //
	  // We try to not inherit from `Object.prototype`. In some engines creating an
	  // instance in this way is faster than calling `Object.create(null)` directly.
	  // If `Object.create(null)` is not supported we prefix the event names with a
	  // character to make sure that the built-in object properties are not
	  // overridden or used as an attack vector.
	  //
	  if (Object.create) {
	    Events.prototype = Object.create(null);

	    //
	    // This hack is needed because the `__proto__` property is still inherited in
	    // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.
	    //
	    if (!new Events().__proto__) prefix = false;
	  }

	  /**
	   * Representation of a single event listener.
	   *
	   * @param {Function} fn The listener function.
	   * @param {*} context The context to invoke the listener with.
	   * @param {Boolean} [once=false] Specify if the listener is a one-time listener.
	   * @constructor
	   * @private
	   */
	  function EE(fn, context, once) {
	    this.fn = fn;
	    this.context = context;
	    this.once = once || false;
	  }

	  /**
	   * Add a listener for a given event.
	   *
	   * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.
	   * @param {(String|Symbol)} event The event name.
	   * @param {Function} fn The listener function.
	   * @param {*} context The context to invoke the listener with.
	   * @param {Boolean} once Specify if the listener is a one-time listener.
	   * @returns {EventEmitter}
	   * @private
	   */
	  function addListener(emitter, event, fn, context, once) {
	    if (typeof fn !== 'function') {
	      throw new TypeError('The listener must be a function');
	    }
	    var listener = new EE(fn, context || emitter, once),
	      evt = prefix ? prefix + event : event;
	    if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);else emitter._events[evt] = [emitter._events[evt], listener];
	    return emitter;
	  }

	  /**
	   * Clear event by name.
	   *
	   * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.
	   * @param {(String|Symbol)} evt The Event name.
	   * @private
	   */
	  function clearEvent(emitter, evt) {
	    if (--emitter._eventsCount === 0) emitter._events = new Events();else delete emitter._events[evt];
	  }

	  /**
	   * Minimal `EventEmitter` interface that is molded against the Node.js
	   * `EventEmitter` interface.
	   *
	   * @constructor
	   * @public
	   */
	  function EventEmitter() {
	    this._events = new Events();
	    this._eventsCount = 0;
	  }

	  /**
	   * Return an array listing the events for which the emitter has registered
	   * listeners.
	   *
	   * @returns {Array}
	   * @public
	   */
	  EventEmitter.prototype.eventNames = function eventNames() {
	    var names = [],
	      events,
	      name;
	    if (this._eventsCount === 0) return names;
	    for (name in events = this._events) {
	      if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
	    }
	    if (Object.getOwnPropertySymbols) {
	      return names.concat(Object.getOwnPropertySymbols(events));
	    }
	    return names;
	  };

	  /**
	   * Return the listeners registered for a given event.
	   *
	   * @param {(String|Symbol)} event The event name.
	   * @returns {Array} The registered listeners.
	   * @public
	   */
	  EventEmitter.prototype.listeners = function listeners(event) {
	    var evt = prefix ? prefix + event : event,
	      handlers = this._events[evt];
	    if (!handlers) return [];
	    if (handlers.fn) return [handlers.fn];
	    for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
	      ee[i] = handlers[i].fn;
	    }
	    return ee;
	  };

	  /**
	   * Return the number of listeners listening to a given event.
	   *
	   * @param {(String|Symbol)} event The event name.
	   * @returns {Number} The number of listeners.
	   * @public
	   */
	  EventEmitter.prototype.listenerCount = function listenerCount(event) {
	    var evt = prefix ? prefix + event : event,
	      listeners = this._events[evt];
	    if (!listeners) return 0;
	    if (listeners.fn) return 1;
	    return listeners.length;
	  };

	  /**
	   * Calls each of the listeners registered for a given event.
	   *
	   * @param {(String|Symbol)} event The event name.
	   * @returns {Boolean} `true` if the event had listeners, else `false`.
	   * @public
	   */
	  EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
	    var evt = prefix ? prefix + event : event;
	    if (!this._events[evt]) return false;
	    var listeners = this._events[evt],
	      len = arguments.length,
	      args,
	      i;
	    if (listeners.fn) {
	      if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);
	      switch (len) {
	        case 1:
	          return listeners.fn.call(listeners.context), true;
	        case 2:
	          return listeners.fn.call(listeners.context, a1), true;
	        case 3:
	          return listeners.fn.call(listeners.context, a1, a2), true;
	        case 4:
	          return listeners.fn.call(listeners.context, a1, a2, a3), true;
	        case 5:
	          return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
	        case 6:
	          return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
	      }
	      for (i = 1, args = new Array(len - 1); i < len; i++) {
	        args[i - 1] = arguments[i];
	      }
	      listeners.fn.apply(listeners.context, args);
	    } else {
	      var length = listeners.length,
	        j;
	      for (i = 0; i < length; i++) {
	        if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);
	        switch (len) {
	          case 1:
	            listeners[i].fn.call(listeners[i].context);
	            break;
	          case 2:
	            listeners[i].fn.call(listeners[i].context, a1);
	            break;
	          case 3:
	            listeners[i].fn.call(listeners[i].context, a1, a2);
	            break;
	          case 4:
	            listeners[i].fn.call(listeners[i].context, a1, a2, a3);
	            break;
	          default:
	            if (!args) for (j = 1, args = new Array(len - 1); j < len; j++) {
	              args[j - 1] = arguments[j];
	            }
	            listeners[i].fn.apply(listeners[i].context, args);
	        }
	      }
	    }
	    return true;
	  };

	  /**
	   * Add a listener for a given event.
	   *
	   * @param {(String|Symbol)} event The event name.
	   * @param {Function} fn The listener function.
	   * @param {*} [context=this] The context to invoke the listener with.
	   * @returns {EventEmitter} `this`.
	   * @public
	   */
	  EventEmitter.prototype.on = function on(event, fn, context) {
	    return addListener(this, event, fn, context, false);
	  };

	  /**
	   * Add a one-time listener for a given event.
	   *
	   * @param {(String|Symbol)} event The event name.
	   * @param {Function} fn The listener function.
	   * @param {*} [context=this] The context to invoke the listener with.
	   * @returns {EventEmitter} `this`.
	   * @public
	   */
	  EventEmitter.prototype.once = function once(event, fn, context) {
	    return addListener(this, event, fn, context, true);
	  };

	  /**
	   * Remove the listeners of a given event.
	   *
	   * @param {(String|Symbol)} event The event name.
	   * @param {Function} fn Only remove the listeners that match this function.
	   * @param {*} context Only remove the listeners that have this context.
	   * @param {Boolean} once Only remove one-time listeners.
	   * @returns {EventEmitter} `this`.
	   * @public
	   */
	  EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {
	    var evt = prefix ? prefix + event : event;
	    if (!this._events[evt]) return this;
	    if (!fn) {
	      clearEvent(this, evt);
	      return this;
	    }
	    var listeners = this._events[evt];
	    if (listeners.fn) {
	      if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
	        clearEvent(this, evt);
	      }
	    } else {
	      for (var i = 0, events = [], length = listeners.length; i < length; i++) {
	        if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
	          events.push(listeners[i]);
	        }
	      }

	      //
	      // Reset the array, or remove it completely if we have no more listeners.
	      //
	      if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;else clearEvent(this, evt);
	    }
	    return this;
	  };

	  /**
	   * Remove all listeners, or those of the specified event.
	   *
	   * @param {(String|Symbol)} [event] The event name.
	   * @returns {EventEmitter} `this`.
	   * @public
	   */
	  EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {
	    var evt;
	    if (event) {
	      evt = prefix ? prefix + event : event;
	      if (this._events[evt]) clearEvent(this, evt);
	    } else {
	      this._events = new Events();
	      this._eventsCount = 0;
	    }
	    return this;
	  };

	  //
	  // Alias methods names because people roll like that.
	  //
	  EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
	  EventEmitter.prototype.addListener = EventEmitter.prototype.on;

	  //
	  // Expose the prefix.
	  //
	  EventEmitter.prefixed = prefix;

	  //
	  // Allow `EventEmitter` to be imported as module namespace.
	  //
	  EventEmitter.EventEmitter = EventEmitter;

	  //
	  // Expose the module.
	  //
	  {
	    module.exports = EventEmitter;
	  }
	})(eventemitter3);
	var eventemitter3Exports = eventemitter3.exports;
	var index$1 = /*@__PURE__*/getDefaultExportFromCjs(eventemitter3Exports);

	const isType = (value, type) => Object.prototype.toString.call(value) === `[object ${type}]`;

	const isBoolean = (value, fuzzy = false) => {
	    if (fuzzy) {
	        return typeof value === 'boolean';
	    }
	    return value === true || value === false || isType(value, 'Boolean');
	};

	const isFunction = (value) => {
	    return typeof value === 'function';
	};

	const isNil = (value) => {
	    return value === null || value === undefined;
	};

	const isNull = (value) => {
	    return value === null;
	};

	const isValid = (value) => {
	    return value !== null && value !== undefined;
	};

	const isObject = (value) => {
	    const type = typeof value;
	    return (value !== null && type === 'object') || type === 'function';
	};

	const isObjectLike = (value) => {
	    return typeof value === 'object' && value !== null;
	};

	const isPlainObject = function (value) {
	    if (!isObjectLike(value) || !isType(value, 'Object')) {
	        return false;
	    }
	    if (Object.getPrototypeOf(value) === null) {
	        return true;
	    }
	    let proto = value;
	    while (Object.getPrototypeOf(proto) !== null) {
	        proto = Object.getPrototypeOf(proto);
	    }
	    return Object.getPrototypeOf(value) === proto;
	};

	const isUndefined = (value) => {
	    return value === undefined;
	};

	const isString = (value, fuzzy = false) => {
	    const type = typeof value;
	    if (fuzzy) {
	        return type === 'string';
	    }
	    return type === 'string' || isType(value, 'String');
	};

	const isArray = (value) => {
	    return Array.isArray ? Array.isArray(value) : isType(value, 'Array');
	};

	const isArrayLike = function (value) {
	    return value !== null && typeof value !== 'function' && Number.isFinite(value.length);
	};

	const isDate = (value) => {
	    return isType(value, 'Date');
	};

	const isNumber = (value, fuzzy = false) => {
	    const type = typeof value;
	    if (fuzzy) {
	        return type === 'number';
	    }
	    return type === 'number' || isType(value, 'Number');
	};

	const isNumeric = (value) => {
	    if (typeof value !== 'string') {
	        return false;
	    }
	    return !isNaN(Number(value)) && !isNaN(parseFloat(value));
	};

	const isValidNumber = (value) => {
	    return isNumber(value) && Number.isFinite(value);
	};

	const isValidUrl = (value) => {
	    const exp = /^(http(s)?:\/\/)\w+[^\s]+(\.[^\s]+){1,}$/;
	    const urlPattern = new RegExp(exp);
	    return urlPattern.test(value);
	};

	const isRegExp = (value) => {
	    return isType(value, 'RegExp');
	};

	const isBase64 = (value) => {
	    const exp = /^data:image\/(?:gif|png|jpeg|bmp|webp|svg\+xml)(?:;charset=utf-8)?;base64,(?:[A-Za-z0-9]|[+/])+={0,2}/g;
	    const urlPattern = new RegExp(exp);
	    return urlPattern.test(value);
	};

	const getType = (value) => {
	    return {}.toString
	        .call(value)
	        .replace(/^\[object /, '')
	        .replace(/]$/, '');
	};

	const objectProto = Object.prototype;
	const isPrototype = function (value) {
	    const Ctor = value && value.constructor;
	    const proto = (typeof Ctor === 'function' && Ctor.prototype) || objectProto;
	    return value === proto;
	};

	const hasOwnProperty$2 = Object.prototype.hasOwnProperty;
	function isEmpty(value) {
	    if (isNil(value)) {
	        return true;
	    }
	    if (isArrayLike(value)) {
	        return !value.length;
	    }
	    const type = getType(value);
	    if (type === 'Map' || type === 'Set') {
	        return !value.size;
	    }
	    if (isPrototype(value)) {
	        return !Object.keys(value).length;
	    }
	    for (const key in value) {
	        if (hasOwnProperty$2.call(value, key)) {
	            return false;
	        }
	    }
	    return true;
	}

	const get = (obj, path, defaultValue) => {
	    const paths = isString(path) ? path.split('.') : path;
	    for (let p = 0; p < paths.length; p++) {
	        obj = obj ? obj[paths[p]] : undefined;
	    }
	    return obj === undefined ? defaultValue : obj;
	};

	const hasOwnProperty$1 = Object.prototype.hasOwnProperty;
	const has = (object, key) => {
	    return object != null && hasOwnProperty$1.call(object, key);
	};

	function getRegExpFlags(re) {
	    let flags = '';
	    re.global && (flags += 'g');
	    re.ignoreCase && (flags += 'i');
	    re.multiline && (flags += 'm');
	    return flags;
	}
	function clone(parent, circular = false, depth = 0, prototype = undefined) {
	    const allParents = [];
	    const allChildren = [];
	    if (typeof circular === 'undefined') {
	        circular = true;
	    }
	    if (typeof depth === 'undefined') {
	        depth = Infinity;
	    }
	    function _clone(parent, depth) {
	        if (parent === null) {
	            return null;
	        }
	        if (depth === 0) {
	            return parent;
	        }
	        let child;
	        if (typeof parent !== 'object') {
	            return parent;
	        }
	        if (isArray(parent)) {
	            child = [];
	        }
	        else if (isRegExp(parent)) {
	            child = new RegExp(parent.source, getRegExpFlags(parent));
	            if (parent.lastIndex) {
	                child.lastIndex = parent.lastIndex;
	            }
	        }
	        else if (isDate(parent)) {
	            child = new Date(parent.getTime());
	        }
	        else {
	            if (typeof prototype === 'undefined') {
	                child = Object.create(Object.getPrototypeOf(parent));
	            }
	            else {
	                child = Object.create(prototype);
	            }
	        }
	        if (circular) {
	            const index = allParents.indexOf(parent);
	            if (index !== -1) {
	                return allChildren[index];
	            }
	            allParents.push(parent);
	            allChildren.push(child);
	        }
	        for (const i in parent) {
	            child[i] = _clone(parent[i], depth - 1);
	        }
	        return child;
	    }
	    return _clone(parent, depth);
	}

	function cloneDeep(value, ignoreWhen, excludeKeys) {
	    let result;
	    if (!isValid(value) || typeof value !== 'object' || (ignoreWhen && ignoreWhen(value))) {
	        return value;
	    }
	    const isArr = isArray(value);
	    const length = value.length;
	    if (isArr) {
	        result = new Array(length);
	    }
	    else if (typeof value === 'object') {
	        result = {};
	    }
	    else if (isBoolean(value) || isNumber(value) || isString(value)) {
	        result = value;
	    }
	    else if (isDate(value)) {
	        result = new Date(+value);
	    }
	    else {
	        result = undefined;
	    }
	    const props = isArr ? undefined : Object.keys(Object(value));
	    let index = -1;
	    if (result) {
	        while (++index < (props || value).length) {
	            const key = props ? props[index] : index;
	            const subValue = value[key];
	            if (excludeKeys && excludeKeys.includes(key.toString())) {
	                result[key] = subValue;
	            }
	            else {
	                result[key] = cloneDeep(subValue, ignoreWhen, excludeKeys);
	            }
	        }
	    }
	    return result;
	}

	function baseMerge(target, source, shallowArray = false, skipTargetArray = false) {
	    if (source) {
	        if (target === source) {
	            return;
	        }
	        if (isValid(source) && typeof source === 'object') {
	            const iterable = Object(source);
	            const props = [];
	            for (const key in iterable) {
	                props.push(key);
	            }
	            let { length } = props;
	            let propIndex = -1;
	            while (length--) {
	                const key = props[++propIndex];
	                if (isValid(iterable[key]) &&
	                    typeof iterable[key] === 'object' &&
	                    (!skipTargetArray || !isArray(target[key]))) {
	                    baseMergeDeep(target, source, key, shallowArray, skipTargetArray);
	                }
	                else {
	                    assignMergeValue(target, key, iterable[key]);
	                }
	            }
	        }
	    }
	}
	function baseMergeDeep(target, source, key, shallowArray = false, skipTargetArray = false) {
	    const objValue = target[key];
	    const srcValue = source[key];
	    let newValue = source[key];
	    let isCommon = true;
	    if (isArray(srcValue)) {
	        if (shallowArray) {
	            newValue = [];
	        }
	        else if (isArray(objValue)) {
	            newValue = objValue;
	        }
	        else if (isArrayLike(objValue)) {
	            newValue = new Array(objValue.length);
	            let index = -1;
	            const length = objValue.length;
	            while (++index < length) {
	                newValue[index] = objValue[index];
	            }
	        }
	    }
	    else if (isPlainObject(srcValue)) {
	        newValue = objValue !== null && objValue !== void 0 ? objValue : {};
	        if (typeof objValue === 'function' || typeof objValue !== 'object') {
	            newValue = {};
	        }
	    }
	    else {
	        isCommon = false;
	    }
	    if (isCommon) {
	        baseMerge(newValue, srcValue, shallowArray, skipTargetArray);
	    }
	    assignMergeValue(target, key, newValue);
	}
	function assignMergeValue(target, key, value) {
	    if ((value !== undefined && !eq(target[key], value)) || (value === undefined && !(key in target))) {
	        target[key] = value;
	    }
	}
	function eq(value, other) {
	    return value === other || (Number.isNaN(value) && Number.isNaN(other));
	}
	function merge(target, ...sources) {
	    let sourceIndex = -1;
	    const length = sources.length;
	    while (++sourceIndex < length) {
	        const source = sources[sourceIndex];
	        baseMerge(target, source, true);
	    }
	    return target;
	}

	const hasOwnProperty = Object.prototype.hasOwnProperty;
	function pick(obj, keys) {
	    if (!obj || !isPlainObject(obj)) {
	        return obj;
	    }
	    const result = {};
	    keys.forEach(k => {
	        if (hasOwnProperty.call(obj, k)) {
	            result[k] = obj[k];
	        }
	    });
	    return result;
	}

	function pickWithout(obj, keys) {
	    if (!obj || !isPlainObject(obj)) {
	        return obj;
	    }
	    const result = {};
	    Object.keys(obj).forEach((k) => {
	        const v = obj[k];
	        let match = false;
	        keys.forEach(itKey => {
	            if (isString(itKey) && itKey === k) {
	                match = true;
	            }
	            else if (itKey instanceof RegExp && k.match(itKey)) {
	                match = true;
	            }
	        });
	        if (!match) {
	            result[k] = v;
	        }
	    });
	    return result;
	}

	function objToString(obj) {
	    return Object.prototype.toString.call(obj);
	}
	function objectKeys(obj) {
	    return Object.keys(obj);
	}
	function isEqual(a, b, options) {
	    if (a === b) {
	        return true;
	    }
	    if (typeof a !== typeof b) {
	        return false;
	    }
	    if (a == null || b == null) {
	        return false;
	    }
	    if (Number.isNaN(a) && Number.isNaN(b)) {
	        return true;
	    }
	    if (objToString(a) !== objToString(b)) {
	        return false;
	    }
	    if (isFunction(a)) {
	        return !!(options === null || options === void 0 ? void 0 : options.skipFunction);
	    }
	    if (typeof a !== 'object') {
	        return false;
	    }
	    if (isArray(a)) {
	        if (a.length !== b.length) {
	            return false;
	        }
	        for (let i = a.length - 1; i >= 0; i--) {
	            if (!isEqual(a[i], b[i], options)) {
	                return false;
	            }
	        }
	        return true;
	    }
	    if (!isPlainObject(a)) {
	        return false;
	    }
	    const ka = objectKeys(a);
	    const kb = objectKeys(b);
	    if (ka.length !== kb.length) {
	        return false;
	    }
	    ka.sort();
	    kb.sort();
	    for (let i = ka.length - 1; i >= 0; i--) {
	        if (ka[i] != kb[i]) {
	            return false;
	        }
	    }
	    for (let i = ka.length - 1; i >= 0; i--) {
	        const key = ka[i];
	        if (!isEqual(a[key], b[key], options)) {
	            return false;
	        }
	    }
	    return true;
	}

	function is(x, y) {
	    if (x === y) {
	        return x !== 0 || y !== 0 || 1 / x === 1 / y;
	    }
	    return x !== x && y !== y;
	}
	function length(obj) {
	    if (isArray(obj)) {
	        return obj.length;
	    }
	    if (isObject(obj)) {
	        return Object.keys(obj).length;
	    }
	    return 0;
	}
	function isShallowEqual(objA, objB) {
	    if (is(objA, objB)) {
	        return true;
	    }
	    if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {
	        return false;
	    }
	    if (isArray(objA) !== isArray(objB)) {
	        return false;
	    }
	    if (length(objA) !== length(objB)) {
	        return false;
	    }
	    let ret = true;
	    Object.keys(objA).forEach((k) => {
	        const v = objA[k];
	        if (!is(v, objB[k])) {
	            ret = false;
	            return ret;
	        }
	        return true;
	    });
	    return ret;
	}

	function keys(obj) {
	    if (!obj) {
	        return [];
	    }
	    if (Object.keys) {
	        return Object.keys(obj);
	    }
	    const keyList = [];
	    for (const key in obj) {
	        if (obj.hasOwnProperty(key)) {
	            keyList.push(key);
	        }
	    }
	    return keyList;
	}
	function defaults(target, source, overlay) {
	    const keysArr = keys(source);
	    for (let i = 0; i < keysArr.length; i++) {
	        const key = keysArr[i];
	        if (overlay ? source[key] != null : target[key] == null) {
	            target[key] = source[key];
	        }
	    }
	    return target;
	}
	function mixin(target, source, override = true) {
	    target = 'prototype' in target ? target.prototype : target;
	    source = 'prototype' in source ? source.prototype : source;
	    if (Object.getOwnPropertyNames) {
	        const keyList = Object.getOwnPropertyNames(source);
	        for (let i = 0; i < keyList.length; i++) {
	            const key = keyList[i];
	            if (key !== 'constructor') {
	                if (override ? source[key] != null : target[key] == null) {
	                    target[key] = source[key];
	                }
	            }
	        }
	    }
	    else {
	        defaults(target, source, override);
	    }
	}

	function array(arr) {
	    if (isValid(arr)) {
	        return isArray(arr) ? arr : [arr];
	    }
	    return [];
	}
	function last(val) {
	    if (isArrayLike(val)) {
	        const arr = val;
	        return arr[arr.length - 1];
	    }
	    return undefined;
	}
	const span = (arr) => {
	    if (arr.length <= 1) {
	        return 0;
	    }
	    return last(arr) - arr[0];
	};
	const maxInArray = (arr, compareFn) => {
	    var _a;
	    if (arr.length === 0) {
	        return undefined;
	    }
	    let max = arr[0];
	    for (let i = 1; i < arr.length; i++) {
	        const value = arr[i];
	        if (((_a = compareFn === null || compareFn === void 0 ? void 0 : compareFn(value, max)) !== null && _a !== void 0 ? _a : value - max) > 0) {
	            max = value;
	        }
	    }
	    return max;
	};
	const minInArray = (arr, compareFn) => {
	    var _a;
	    if (arr.length === 0) {
	        return undefined;
	    }
	    let min = arr[0];
	    for (let i = 1; i < arr.length; i++) {
	        const value = arr[i];
	        if (((_a = compareFn === null || compareFn === void 0 ? void 0 : compareFn(value, min)) !== null && _a !== void 0 ? _a : value - min) < 0) {
	            min = value;
	        }
	    }
	    return min;
	};
	function arrayEqual(a, b) {
	    if (!isArray(a) || !isArray(b)) {
	        return false;
	    }
	    if (a.length !== b.length) {
	        return false;
	    }
	    for (let i = 0; i < a.length; i++) {
	        if (a[i] !== b[i]) {
	            return false;
	        }
	    }
	    return true;
	}
	function uniqArray(arr) {
	    if (!arr || !isArray(arr)) {
	        return arr;
	    }
	    return Array.from(new Set(array(arr)));
	}
	function shuffleArray(arr, random = Math.random) {
	    let j;
	    let x;
	    let i = arr.length;
	    while (i) {
	        j = Math.floor(random() * i);
	        x = arr[--i];
	        arr[i] = arr[j];
	        arr[j] = x;
	    }
	    return arr;
	}
	function flattenArray(arr) {
	    if (!isArray(arr)) {
	        return [arr];
	    }
	    const result = [];
	    for (const value of arr) {
	        result.push(...flattenArray(value));
	    }
	    return result;
	}

	function range(start, stop, step) {
	    if (!isValid(stop)) {
	        stop = start;
	        start = 0;
	    }
	    if (!isValid(step)) {
	        step = 1;
	    }
	    let i = -1;
	    const n = Math.max(0, Math.ceil((stop - start) / step)) | 0;
	    const range = new Array(n);
	    while (++i < n) {
	        range[i] = start + i * step;
	    }
	    return range;
	}

	function ascending(a, b) {
	    return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
	}

	function toNumber(a) {
	    return Number(a);
	}

	function quantileSorted(values, percent, valueof = toNumber) {
	    const n = values.length;
	    if (!n) {
	        return;
	    }
	    if (percent <= 0 || n < 2) {
	        return valueof(values[0], 0, values);
	    }
	    if (percent >= 1) {
	        return valueof(values[n - 1], n - 1, values);
	    }
	    const i = (n - 1) * percent;
	    const i0 = Math.floor(i);
	    const value0 = valueof(values[i0], i0, values);
	    const value1 = valueof(values[i0 + 1], i0 + 1, values);
	    return value0 + (value1 - value0) * (i - i0);
	}

	const hasConsole = typeof console !== 'undefined';
	function log(method, level, input) {
	    const args = [level].concat([].slice.call(input));
	    if (hasConsole) {
	        console[method].apply(console, args);
	    }
	}
	exports.LoggerLevel = void 0;
	(function (LoggerLevel) {
	    LoggerLevel[LoggerLevel["None"] = 0] = "None";
	    LoggerLevel[LoggerLevel["Error"] = 1] = "Error";
	    LoggerLevel[LoggerLevel["Warn"] = 2] = "Warn";
	    LoggerLevel[LoggerLevel["Info"] = 3] = "Info";
	    LoggerLevel[LoggerLevel["Debug"] = 4] = "Debug";
	})(exports.LoggerLevel || (exports.LoggerLevel = {}));
	class Logger {
	    static getInstance(level, method) {
	        if (Logger._instance && isNumber(level)) {
	            Logger._instance.level(level);
	        }
	        else if (!Logger._instance) {
	            Logger._instance = new Logger(level, method);
	        }
	        return Logger._instance;
	    }
	    static setInstance(logger) {
	        return (Logger._instance = logger);
	    }
	    static setInstanceLevel(level) {
	        if (Logger._instance) {
	            Logger._instance.level(level);
	        }
	        else {
	            Logger._instance = new Logger(level);
	        }
	    }
	    static clearInstance() {
	        Logger._instance = null;
	    }
	    constructor(level = exports.LoggerLevel.None, method) {
	        this._onErrorHandler = [];
	        this._level = level;
	        this._method = method;
	    }
	    addErrorHandler(handler) {
	        if (this._onErrorHandler.find(h => h === handler)) {
	            return;
	        }
	        this._onErrorHandler.push(handler);
	    }
	    removeErrorHandler(handler) {
	        const index = this._onErrorHandler.findIndex(h => h === handler);
	        if (index < 0) {
	            return;
	        }
	        this._onErrorHandler.splice(index, 1);
	    }
	    callErrorHandler(...args) {
	        this._onErrorHandler.forEach(h => h(...args));
	    }
	    canLogInfo() {
	        return this._level >= exports.LoggerLevel.Info;
	    }
	    canLogDebug() {
	        return this._level >= exports.LoggerLevel.Debug;
	    }
	    canLogError() {
	        return this._level >= exports.LoggerLevel.Error;
	    }
	    canLogWarn() {
	        return this._level >= exports.LoggerLevel.Warn;
	    }
	    level(levelValue) {
	        if (arguments.length) {
	            this._level = +levelValue;
	            return this;
	        }
	        return this._level;
	    }
	    error(...args) {
	        var _a;
	        if (this._level >= exports.LoggerLevel.Error) {
	            if (this._onErrorHandler.length) {
	                this.callErrorHandler(...args);
	            }
	            else {
	                log((_a = this._method) !== null && _a !== void 0 ? _a : 'error', 'ERROR', args);
	            }
	        }
	        return this;
	    }
	    warn(...args) {
	        if (this._level >= exports.LoggerLevel.Warn) {
	            log(this._method || 'warn', 'WARN', args);
	        }
	        return this;
	    }
	    info(...args) {
	        if (this._level >= exports.LoggerLevel.Info) {
	            log(this._method || 'log', 'INFO', args);
	        }
	        return this;
	    }
	    debug(...args) {
	        if (this._level >= exports.LoggerLevel.Debug) {
	            log(this._method || 'log', 'DEBUG', args);
	        }
	        return this;
	    }
	}
	Logger._instance = null;

	function bisect(a, x, lo = 0, hi) {
	    if (isNil(hi)) {
	        hi = a.length;
	    }
	    while (lo < hi) {
	        const mid = (lo + hi) >>> 1;
	        if (ascending(a[mid], x) > 0) {
	            hi = mid;
	        }
	        else {
	            lo = mid + 1;
	        }
	    }
	    return lo;
	}
	function findZeroOfFunction(f, a, b, parameters) {
	    var _a, _b;
	    const maxIterations = (_a = parameters === null || parameters === void 0 ? void 0 : parameters.maxIterations) !== null && _a !== void 0 ? _a : 100;
	    const tolerance = (_b = parameters === null || parameters === void 0 ? void 0 : parameters.tolerance) !== null && _b !== void 0 ? _b : 1e-10;
	    const fA = f(a);
	    const fB = f(b);
	    let delta = b - a;
	    if (fA * fB > 0) {
	        const logger = Logger.getInstance();
	        logger.error('Initial bisect points must have opposite signs');
	        return NaN;
	    }
	    if (fA === 0) {
	        return a;
	    }
	    if (fB === 0) {
	        return b;
	    }
	    for (let i = 0; i < maxIterations; ++i) {
	        delta /= 2;
	        const mid = a + delta;
	        const fMid = f(mid);
	        if (fMid * fA >= 0) {
	            a = mid;
	        }
	        if (Math.abs(delta) < tolerance || fMid === 0) {
	            return mid;
	        }
	    }
	    return a + delta;
	}
	const binaryFuzzySearch = (arr, compareFn) => {
	    return binaryFuzzySearchInNumberRange(0, arr.length, value => compareFn(arr[value]));
	};
	const binaryFuzzySearchInNumberRange = (x1, x2, compareFn) => {
	    let left = x1;
	    let right = x2;
	    while (left < right) {
	        const mid = Math.floor((left + right) / 2);
	        if (compareFn(mid) >= 0) {
	            right = mid;
	        }
	        else {
	            left = mid + 1;
	        }
	    }
	    return left;
	};

	function variance(values, valueof) {
	    let count = 0;
	    let delta;
	    let mean = 0;
	    let sum = 0;
	    if (valueof === undefined) {
	        for (let value of values) {
	            if (value != null && (value = +value) >= value) {
	                delta = value - mean;
	                mean += delta / ++count;
	                sum += delta * (value - mean);
	            }
	        }
	    }
	    else {
	        let index = -1;
	        for (let value of values) {
	            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {
	                delta = value - mean;
	                mean += delta / ++count;
	                sum += delta * (value - mean);
	            }
	        }
	    }
	    if (count > 1) {
	        return sum / (count - 1);
	    }
	    return 0;
	}

	function deviation(values, valueof) {
	    const v = variance(values, valueof);
	    return v ? Math.sqrt(v) : v;
	}

	const median = (values, isSorted) => {
	    let sorted = values;
	    if (isSorted !== true) {
	        sorted = values.sort(ascending);
	    }
	    return quantileSorted(sorted, 0.5);
	};

	const e10 = Math.sqrt(50);
	const e5 = Math.sqrt(10);
	const e2 = Math.sqrt(2);
	function tickStep(start, stop, count) {
	    const step0 = Math.abs(stop - start) / Math.max(0, count);
	    let step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10));
	    const error = step0 / step1;
	    if (error >= e10) {
	        step1 *= 10;
	    }
	    else if (error >= e5) {
	        step1 *= 5;
	    }
	    else if (error >= e2) {
	        step1 *= 2;
	    }
	    return stop < start ? -step1 : step1;
	}

	const DEFAULT_ABSOLUTE_TOLERATE = 1e-10;
	const DEFAULT_RELATIVE_TOLERATE = 1e-10;
	function isNumberClose(a, b, relTol = DEFAULT_RELATIVE_TOLERATE, absTol = DEFAULT_ABSOLUTE_TOLERATE) {
	    const abs = absTol;
	    const rel = relTol * Math.max(a, b);
	    return Math.abs(a - b) <= Math.max(abs, rel);
	}
	function isGreater(a, b, relTol, absTol) {
	    return a > b && !isNumberClose(a, b, relTol, absTol);
	}
	function isLess(a, b, relTol, absTol) {
	    return a < b && !isNumberClose(a, b, relTol, absTol);
	}

	const constant = (value) => {
	    return isFunction(value)
	        ? value
	        : () => {
	            return value;
	        };
	};

	const memoize = (func) => {
	    let lastArgs = null;
	    let lastResult = null;
	    return ((...args) => {
	        if (lastArgs && args.every((val, i) => val === lastArgs[i])) {
	            return lastResult;
	        }
	        lastArgs = args;
	        lastResult = func(...args);
	        return lastResult;
	    });
	};

	const repeat = (str, repeatCount = 0) => {
	    let s = '';
	    let i = repeatCount - 1;
	    while (i >= 0) {
	        s = `${s}${str}`;
	        i -= 1;
	    }
	    return s;
	};
	const pad = (str, length, padChar = ' ', align = 'right') => {
	    const c = padChar;
	    const s = str + '';
	    const n = length - s.length;
	    if (n <= 0) {
	        return s;
	    }
	    if (align === 'left') {
	        return repeat(c, n) + s;
	    }
	    return align === 'center' ? repeat(c, Math.floor(n / 2)) + s + repeat(c, Math.ceil(n / 2)) : s + repeat(c, n);
	};

	const truncate = (str, length, align = 'right', ellipsis) => {
	    const e = !isNil(ellipsis) ? ellipsis : '\u2026';
	    const s = str + '';
	    const n = s.length;
	    const l = Math.max(0, length - e.length);
	    if (n <= length) {
	        return s;
	    }
	    if (align === 'left') {
	        return e + s.slice(n - l);
	    }
	    return align === 'center' ? s.slice(0, Math.ceil(l / 2)) + e + s.slice(n - Math.floor(l / 2)) : s.slice(0, l) + e;
	};

	const uuid = (len, radix) => {
	    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
	    const uuid = [];
	    let i;
	    radix = radix || chars.length;
	    if (len) {
	        for (i = 0; i < len; i++) {
	            uuid[i] = chars[0 | (Math.random() * radix)];
	        }
	    }
	    else {
	        let r;
	        uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
	        uuid[14] = '4';
	        for (i = 0; i < 36; i++) {
	            if (!uuid[i]) {
	                r = 0 | (Math.random() * 16);
	                uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
	            }
	        }
	    }
	    return uuid.join('');
	};

	const clamp = function (input, min, max) {
	    if (input < min) {
	        return min;
	    }
	    else if (input > max) {
	        return max;
	    }
	    return input;
	};

	const clampRange = (range, min, max) => {
	    let [lowValue, highValue] = range;
	    if (highValue < lowValue) {
	        lowValue = range[1];
	        highValue = range[0];
	    }
	    const span = highValue - lowValue;
	    if (span >= max - min) {
	        return [min, max];
	    }
	    lowValue = Math.min(Math.max(lowValue, min), max - span);
	    return [lowValue, lowValue + span];
	};

	function clamper(a, b) {
	    let t;
	    if (a > b) {
	        t = a;
	        a = b;
	        b = t;
	    }
	    return (x) => {
	        return Math.max(a, Math.min(b, x));
	    };
	}

	let hasRaf = false;
	try {
	    hasRaf = typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function';
	}
	catch (err) {
	    hasRaf = false;
	}
	hasRaf = false;
	function debounce(func, wait, options) {
	    let lastArgs;
	    let lastThis;
	    let maxWait;
	    let result;
	    let timerId;
	    let lastCallTime;
	    let lastInvokeTime = 0;
	    let leading = false;
	    let maxing = false;
	    let trailing = true;
	    const useRAF = !wait && wait !== 0 && hasRaf;
	    if (typeof func !== 'function') {
	        throw new TypeError('Expected a function');
	    }
	    wait = +wait || 0;
	    if (isObject(options)) {
	        leading = !!options.leading;
	        maxing = 'maxWait' in options;
	        if (maxing) {
	            maxWait = Math.max(isValidNumber(options.maxWait) ? options.maxWait : 0, wait);
	        }
	        trailing = 'trailing' in options ? !!options.trailing : trailing;
	    }
	    function invokeFunc(time) {
	        const args = lastArgs;
	        const thisArg = lastThis;
	        lastArgs = lastThis = undefined;
	        lastInvokeTime = time;
	        result = func.apply(thisArg, args);
	        return result;
	    }
	    function startTimer(pendingFunc, wait) {
	        if (useRAF) {
	            cancelAnimationFrame(timerId);
	            return requestAnimationFrame(pendingFunc);
	        }
	        return setTimeout(pendingFunc, wait);
	    }
	    function cancelTimer(id) {
	        if (useRAF) {
	            return cancelAnimationFrame(id);
	        }
	        clearTimeout(id);
	    }
	    function leadingEdge(time) {
	        lastInvokeTime = time;
	        timerId = startTimer(timerExpired, wait);
	        return leading ? invokeFunc(time) : result;
	    }
	    function remainingWait(time) {
	        const timeSinceLastCall = time - lastCallTime;
	        const timeSinceLastInvoke = time - lastInvokeTime;
	        const timeWaiting = wait - timeSinceLastCall;
	        return maxing ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
	    }
	    function shouldInvoke(time) {
	        const timeSinceLastCall = time - lastCallTime;
	        const timeSinceLastInvoke = time - lastInvokeTime;
	        return (lastCallTime === undefined ||
	            timeSinceLastCall >= wait ||
	            timeSinceLastCall < 0 ||
	            (maxing && timeSinceLastInvoke >= maxWait));
	    }
	    function timerExpired() {
	        const time = Date.now();
	        if (shouldInvoke(time)) {
	            return trailingEdge(time);
	        }
	        timerId = startTimer(timerExpired, remainingWait(time));
	    }
	    function trailingEdge(time) {
	        timerId = undefined;
	        if (trailing && lastArgs) {
	            return invokeFunc(time);
	        }
	        lastArgs = lastThis = undefined;
	        return result;
	    }
	    function cancel() {
	        if (timerId !== undefined) {
	            cancelTimer(timerId);
	        }
	        lastInvokeTime = 0;
	        lastArgs = lastCallTime = lastThis = timerId = undefined;
	    }
	    function flush() {
	        return timerId === undefined ? result : trailingEdge(Date.now());
	    }
	    function pending() {
	        return timerId !== undefined;
	    }
	    function debounced(...args) {
	        const time = Date.now();
	        const isInvoking = shouldInvoke(time);
	        lastArgs = args;
	        lastThis = this;
	        lastCallTime = time;
	        if (isInvoking) {
	            if (timerId === undefined) {
	                return leadingEdge(lastCallTime);
	            }
	            if (maxing) {
	                timerId = startTimer(timerExpired, wait);
	                return invokeFunc(lastCallTime);
	            }
	        }
	        if (timerId === undefined) {
	            timerId = startTimer(timerExpired, wait);
	        }
	        return result;
	    }
	    debounced.cancel = cancel;
	    debounced.flush = flush;
	    debounced.pending = pending;
	    return debounced;
	}

	function throttle(func, wait, options) {
	    let leading = true;
	    let trailing = true;
	    if (typeof func !== 'function') {
	        throw new TypeError('Expected a function');
	    }
	    if (isObject(options)) {
	        leading = 'leading' in options ? !!options.leading : leading;
	        trailing = 'trailing' in options ? !!options.trailing : trailing;
	    }
	    return debounce(func, wait, {
	        leading,
	        trailing,
	        maxWait: wait
	    });
	}

	function interpolateNumber(a, b) {
	    return (t) => {
	        return a * (1 - t) + b * t;
	    };
	}
	function interpolateNumberRound(a, b) {
	    return function (t) {
	        return Math.round(a * (1 - t) + b * t);
	    };
	}
	function interpolateDate(a, b) {
	    const aVal = a.valueOf();
	    const bVal = b.valueOf();
	    const d = new Date();
	    return (t) => {
	        d.setTime(aVal * (1 - t) + bVal * t);
	        return d;
	    };
	}
	const reA = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g;
	const reB = new RegExp(reA.source, 'g');
	function zero$1(b) {
	    return function () {
	        return b;
	    };
	}
	function one(b) {
	    return function (t) {
	        return b(t) + '';
	    };
	}
	function interpolateString(a, b) {
	    let bi = (reA.lastIndex = reB.lastIndex = 0);
	    let am;
	    let bm;
	    let bs;
	    let i = -1;
	    const s = [];
	    const q = [];
	    (a = a + ''), (b = b + '');
	    while ((am = reA.exec(a)) && (bm = reB.exec(b))) {
	        if ((bs = bm.index) > bi) {
	            bs = b.slice(bi, bs);
	            if (s[i]) {
	                s[i] += bs;
	            }
	            else {
	                s[++i] = bs;
	            }
	        }
	        if ((am = am[0]) === (bm = bm[0])) {
	            if (s[i]) {
	                s[i] += bm;
	            }
	            else {
	                s[++i] = bm;
	            }
	        }
	        else {
	            s[++i] = null;
	            q.push({ i: i, x: interpolateNumber(am, bm) });
	        }
	        bi = reB.lastIndex;
	    }
	    if (bi < b.length) {
	        bs = b.slice(bi);
	        if (s[i]) {
	            s[i] += bs;
	        }
	        else {
	            s[++i] = bs;
	        }
	    }
	    return s.length < 2
	        ? q[0]
	            ? one(q[0].x)
	            : zero$1(b)
	        : ((b = q.length),
	            function (t) {
	                for (let i = 0, o; i < b; ++i) {
	                    s[(o = q[i]).i] = o.x(t);
	                }
	                return s.join('');
	            });
	}

	const TIME_REG = /^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;
	function toDate(val) {
	    if (val instanceof Date) {
	        return val;
	    }
	    else if (isString(val)) {
	        const match = TIME_REG.exec(val);
	        if (!match) {
	            return new Date(NaN);
	        }
	        if (!match[8]) {
	            return new Date(+match[1], +(match[2] || 1) - 1, +match[3] || 1, +match[4] || 0, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0);
	        }
	        let hour = +match[4] || 0;
	        if (match[8].toUpperCase() !== 'Z') {
	            hour -= +match[8].slice(0, 3);
	        }
	        return new Date(Date.UTC(+match[1], +(match[2] || 1) - 1, +match[3] || 1, hour, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0));
	    }
	    else if (isNil(val)) {
	        return new Date(NaN);
	    }
	    return new Date(Math.round(val));
	}

	function toValidNumber(v) {
	    if (isValidNumber(v)) {
	        return v;
	    }
	    const value = +v;
	    return isValidNumber(value) ? value : 0;
	}

	const lowerFirst = function (str) {
	    return str.charAt(0).toLowerCase() + str.substring(1);
	};

	const upperFirst = function (str) {
	    return str.charAt(0).toUpperCase() + str.substring(1);
	};

	function substitute(str, o) {
	    if (!str || !o) {
	        return str;
	    }
	    return str.replace(/\\?\{([^{}]+)\}/g, (match, name) => {
	        if (match.charAt(0) === '\\') {
	            return match.slice(1);
	        }
	        return o[name] === undefined ? '' : o[name];
	    });
	}

	function seedRandom(seed) {
	    return parseFloat('0.' + Math.sin(seed).toString().substring(6));
	}
	const a = 1664525;
	const c = 1013904223;
	const m = 4294967296;
	function randomLCG(initS = 1) {
	    let s = initS;
	    return () => (s = (a * s + c) % m) / m;
	}
	const fakeRandom = () => {
	    let i = -1;
	    const arr = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9];
	    return () => {
	        i = (i + 1) % arr.length;
	        return arr[i];
	    };
	};

	const getter = (path) => {
	    return (obj) => get(obj, path);
	};
	const fieldSingle = (fieldStr, opt = {}) => {
	    if (isFunction(fieldStr)) {
	        return fieldStr;
	    }
	    const path = [fieldStr];
	    return ((opt && opt.get) || getter)(path);
	};
	const field = (fieldStr, opt = {}) => {
	    if (isArray(fieldStr)) {
	        const funcs = fieldStr.map(entry => fieldSingle(entry, opt));
	        return (datum) => {
	            return funcs.map(func => func(datum));
	        };
	    }
	    return fieldSingle(fieldStr, opt);
	};
	const simpleField = (option) => {
	    if (!option) {
	        return null;
	    }
	    if (typeof option === 'string' || typeof option === 'number') {
	        return () => option;
	    }
	    else if (isFunction(option)) {
	        return option;
	    }
	    return (datum) => datum[option.field];
	};

	const toPercent = (percent, total) => {
	    if (isNil(percent)) {
	        return total;
	    }
	    return isString(percent) ? (total * parseFloat(percent)) / 100 : percent;
	};

	const zero = (_) => 0;

	const extent = (array, func) => {
	    const valueGetter = isFunction(func) ? func : (val) => val;
	    let min;
	    let max;
	    if (array && array.length) {
	        const n = array.length;
	        for (let i = 0; i < n; i += 1) {
	            let value = valueGetter(array[i]);
	            if (!isNil(value) && isNumber((value = +value)) && !Number.isNaN(value)) {
	                if (isNil(min)) {
	                    min = value;
	                    max = value;
	                }
	                else {
	                    min = Math.min(min, value);
	                    max = Math.max(max, value);
	                }
	            }
	        }
	        return [min, max];
	    }
	    return [min, max];
	};

	function ordinaryLeastSquares(uX, uY, uXY, uX2) {
	    const delta = uX2 - uX * uX;
	    const slope = Math.abs(delta) < 1e-24 ? 0 : (uXY - uX * uY) / delta;
	    const intercept = uY - slope * uX;
	    return [intercept, slope];
	}
	function visitPoints(data, x, y, callback) {
	    let i = -1;
	    let u;
	    let v;
	    data.forEach(d => {
	        u = x(d);
	        v = y(d);
	        if (!isNil(u) && (u = +u) >= u && !isNil(v) && (v = +v) >= v) {
	            callback(u, v, ++i);
	        }
	    });
	}
	function rSquared(data, x, y, uY, predict) {
	    let SSE = 0;
	    let SST = 0;
	    visitPoints(data, x, y, (dx, dy) => {
	        const sse = dy - predict(dx);
	        const sst = dy - uY;
	        SSE += sse * sse;
	        SST += sst * sst;
	    });
	    return 1 - SSE / SST;
	}
	function regressionLinear(data, x = datum => datum.x, y = datum => datum.y) {
	    let X = 0;
	    let Y = 0;
	    let XY = 0;
	    let X2 = 0;
	    let n = 0;
	    visitPoints(data, x, y, (dx, dy) => {
	        ++n;
	        X += (dx - X) / n;
	        Y += (dy - Y) / n;
	        XY += (dx * dy - XY) / n;
	        X2 += (dx * dx - X2) / n;
	    });
	    const coef = ordinaryLeastSquares(X, Y, XY, X2);
	    const predict = (x) => coef[0] + coef[1] * x;
	    return {
	        coef: coef,
	        predict: predict,
	        rSquared: rSquared(data, x, y, Y, predict)
	    };
	}

	class HashValue {
	}
	class HashTable {
	    constructor() {
	        this.items = {};
	        this.itemList = [];
	    }
	    get type() {
	        return 'xhHashTable';
	    }
	    set(key, value) {
	        const vl = new HashValue();
	        vl.key = key;
	        vl.value = value;
	        let index = this.itemList.length;
	        if (this.has(key)) {
	            index = this.items[key].index;
	        }
	        vl.index = index;
	        this.itemList[index] = vl;
	        this.items[key] = vl;
	        return vl;
	    }
	    clear() {
	        this.items = {};
	        this.itemList = [];
	    }
	    del(key) {
	        if (this.has(key)) {
	            const index = this.items[key].index;
	            if (index > -1) {
	                this.itemList.splice(index, 1);
	            }
	            delete this.items[key];
	            this.resetIndex();
	        }
	    }
	    delFrom(index) {
	        for (let i = index + 1; i < this.count(); i++) {
	            const key = this.itemList[i].key;
	            delete this.items[key];
	        }
	        this.itemList.splice(index + 1, this.count() - index);
	        this.resetIndex();
	    }
	    resetIndex() {
	        this.foreachHashv((k, v) => {
	            const index = this.itemList.indexOf(v);
	            this.items[k].index = index;
	        });
	    }
	    has(key) {
	        return key in this.items;
	    }
	    get(key) {
	        if (this.has(key)) {
	            return this.items[key].value;
	        }
	        return null;
	    }
	    count() {
	        return this.itemList.length;
	    }
	    all() {
	        return this.itemList.map(vl => {
	            return vl.value;
	        });
	    }
	    first() {
	        return this.itemList[0].value;
	    }
	    last() {
	        return this.itemList[this.itemList.length - 1].value;
	    }
	    getByIndex(index) {
	        return this.itemList[index].value;
	    }
	    getKeyByIndex(index) {
	        return this.itemList[index].key;
	    }
	    foreach(callback) {
	        for (const key in this.items) {
	            const returnVal = callback(key, this.items[key].value);
	            if (returnVal === false) {
	                return false;
	            }
	        }
	        return true;
	    }
	    foreachHashv(callback) {
	        for (const key in this.items) {
	            const returnVal = callback(key, this.items[key]);
	            if (returnVal === false) {
	                return false;
	            }
	        }
	        return true;
	    }
	    hasValue(value) {
	        for (const key in this.items) {
	            if (this.items[key].value === value) {
	                return true;
	            }
	        }
	        return false;
	    }
	    indexOf(key) {
	        if (this.has(key)) {
	            return this.items[key].index;
	        }
	        return -1;
	    }
	    insertAt(index, value, key) {
	        const hashV = new HashValue();
	        hashV.index = index;
	        hashV.key = key;
	        hashV.value = value;
	        this.itemList.splice(index, 0, hashV);
	        this.items[key] = hashV;
	        this.resetIndex();
	    }
	    sort(callback) {
	        return this.itemList.sort((a, b) => {
	            return callback(a.value, b.value);
	        });
	    }
	    toArray() {
	        return this.itemList.slice(0, this.itemList.length).map(vl => {
	            return vl.value;
	        });
	    }
	    push(lists) {
	        lists.foreach((key, value) => {
	            this.set(key, value);
	        });
	    }
	    mapKey() {
	        const returnArr = [];
	        for (const key in this.items) {
	            returnArr.push(key);
	        }
	        return returnArr;
	    }
	    toImmutableMap() {
	    }
	}

	const epsilon = 1e-12;
	const pi = Math.PI;
	const halfPi = pi / 2;
	const tau = 2 * pi;
	const NEWTON_ITERATIONS = 4;
	const NEWTON_MIN_SLOPE = 0.001;
	const SUBDIVISION_PRECISION = 0.0000001;
	const SUBDIVISION_MAX_ITERATIONS = 10;
	const pi2 = Math.PI * 2;
	const abs = Math.abs;
	const atan2 = Math.atan2;
	const cos = Math.cos;
	const max = Math.max;
	const min = Math.min;
	const sin = Math.sin;
	const sqrt = Math.sqrt;
	const pow = Math.pow;
	function acos(x) {
	    return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);
	}
	function asin(x) {
	    return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);
	}
	function pointAt(x1, y1, x2, y2, t) {
	    let x = x2;
	    let y = y2;
	    if (typeof x1 === 'number' && typeof x2 === 'number') {
	        x = (1 - t) * x1 + t * x2;
	    }
	    if (typeof y1 === 'number' && typeof y2 === 'number') {
	        y = (1 - t) * y1 + t * y2;
	    }
	    return {
	        x,
	        y
	    };
	}
	function lengthFromPointToLine(point, point1, point2) {
	    const dir1X = point2.x - point1.x;
	    const dir1Y = point2.y - point1.y;
	    const dir2X = point.x - point1.x;
	    const dir2Y = point.y - point1.y;
	    const s = Math.abs(dir1X * dir2Y - dir2X * dir1Y);
	    const length = Math.sqrt(dir1X * dir1X + dir1Y * dir1Y);
	    return s / length;
	}
	function crossProduct(dir1, dir2) {
	    return dir1[0] * dir2[1] - dir1[1] * dir2[0];
	}
	function crossProductPoint(dir1, dir2) {
	    return dir1.x * dir2.y - dir1.y * dir2.x;
	}
	function dotProduct(a, b) {
	    let ret = 0;
	    for (let i = 0; i < a.length; ++i) {
	        ret += a[i] * b[i];
	    }
	    return ret;
	}
	function fuzzyEqualNumber(a, b) {
	    return abs(a - b) < epsilon;
	}
	function fuzzyEqualVec(a, b) {
	    return abs(a[0] - b[0]) + abs(a[1] - b[1]) < epsilon;
	}
	function fixPrecision(num, precision = 10) {
	    return Math.round(num * precision) / precision;
	}
	function getDecimalPlaces(n) {
	    const dStr = n.toString().split(/[eE]/);
	    const s = (dStr[0].split('.')[1] || '').length - (+dStr[1] || 0);
	    return s > 0 ? s : 0;
	}
	function precisionAdd(a, b) {
	    return fixPrecision(a + b, 10 ** Math.max(getDecimalPlaces(a), getDecimalPlaces(b)));
	}
	function precisionSub(a, b) {
	    return fixPrecision(a - b, 10 ** Math.max(getDecimalPlaces(a), getDecimalPlaces(b)));
	}

	class Point {
	    constructor(x = 0, y = 0, x1, y1) {
	        this.x = 0;
	        this.y = 0;
	        this.x = x;
	        this.y = y;
	        this.x1 = x1;
	        this.y1 = y1;
	    }
	    clone() {
	        return new Point(this.x, this.y);
	    }
	    copyFrom(p) {
	        this.x = p.x;
	        this.y = p.y;
	        this.x1 = p.x1;
	        this.y1 = p.y1;
	        this.defined = p.defined;
	        this.context = p.context;
	        return this;
	    }
	    set(x, y) {
	        this.x = x;
	        this.y = y;
	        return this;
	    }
	    add(point) {
	        if (isNumber(point)) {
	            this.x += point;
	            this.y += point;
	            return;
	        }
	        this.x += point.x;
	        this.y += point.y;
	        return this;
	    }
	    sub(point) {
	        if (isNumber(point)) {
	            this.x -= point;
	            this.y -= point;
	            return;
	        }
	        this.x -= point.x;
	        this.y -= point.y;
	        return this;
	    }
	    multi(point) {
	        throw new Error('暂不支持');
	    }
	    div(point) {
	        throw new Error('暂不支持');
	    }
	}
	class PointService {
	    static distancePP(p1, p2) {
	        return sqrt(pow(p1.x - p2.x, 2) + pow(p1.y - p2.y, 2));
	    }
	    static distanceNN(x, y, x1, y1) {
	        return sqrt(pow(x - x1, 2) + pow(y - y1, 2));
	    }
	    static distancePN(point, x, y) {
	        return sqrt(pow(x - point.x, 2) + pow(y - point.y, 2));
	    }
	    static pointAtPP(p1, p2, t) {
	        return new Point((p2.x - p1.x) * t + p1.x, (p2.y - p1.y) * t + p1.y);
	    }
	}
	class PolarPoint {
	    constructor(r = 0, theta = 0, r1, theta1) {
	        this.r = 0;
	        this.theta = 0;
	        this.r = r;
	        this.theta = theta;
	        this.r1 = r1;
	        this.theta1 = theta1;
	    }
	    clone() {
	        return new PolarPoint(this.r, this.theta);
	    }
	    copyFrom(p) {
	        this.r = p.r;
	        this.theta = p.theta;
	        this.r1 = p.r1;
	        this.theta1 = p.theta1;
	        this.defined = p.defined;
	        this.context = p.context;
	        return this;
	    }
	    set(r, theta) {
	        this.r = r;
	        this.theta = theta;
	        return this;
	    }
	}

	const parseUint8ToImageData = (buffer, width, height) => {
	    const clampBuffer = new Uint8ClampedArray(buffer);
	    const flipClampBuffer = new Uint8ClampedArray(buffer.length);
	    for (let i = height - 1; i >= 0; i--) {
	        for (let j = 0; j < width; j++) {
	            const sourceIdx = i * width * 4 + j * 4;
	            const targetIdx = (height - i) * width * 4 + j * 4;
	            flipClampBuffer[targetIdx] = clampBuffer[sourceIdx];
	            flipClampBuffer[targetIdx + 1] = clampBuffer[sourceIdx + 1];
	            flipClampBuffer[targetIdx + 2] = clampBuffer[sourceIdx + 2];
	            flipClampBuffer[targetIdx + 3] = clampBuffer[sourceIdx + 3];
	        }
	    }
	    return new ImageData(flipClampBuffer, width, height);
	};

	function degreeToRadian(degree) {
	    return degree * (Math.PI / 180);
	}
	function radianToDegree(radian) {
	    return (radian * 180) / Math.PI;
	}
	const clampRadian = (angle = 0) => {
	    if (angle < 0) {
	        while (angle < -tau) {
	            angle += tau;
	        }
	    }
	    else if (angle > 0) {
	        while (angle > tau) {
	            angle -= tau;
	        }
	    }
	    return angle;
	};
	const clampAngleByRadian = clampRadian;
	const clampDegree = (a = 0) => {
	    if (a > 360 || a < -360) {
	        return a % 360;
	    }
	    return a;
	};
	const clampAngleByDegree = clampDegree;
	function polarToCartesian(center, radius, angleInRadian) {
	    if (!radius) {
	        return { x: center.x, y: center.y };
	    }
	    return {
	        x: center.x + radius * Math.cos(angleInRadian),
	        y: center.y + radius * Math.sin(angleInRadian)
	    };
	}
	function cartesianToPolar(point, center = { x: 0, y: 0 }, startAngle = 0, endAngle = 2 * Math.PI) {
	    const { x, y } = point;
	    const { x: centerX, y: centerY } = center;
	    let dx = x - centerX;
	    let dy = y - centerY;
	    const radius = Math.sqrt(dx * dx + dy * dy);
	    if (radius === 0) {
	        return {
	            radius: 0,
	            angle: 0
	        };
	    }
	    dx /= radius;
	    dy /= radius;
	    let radian = Math.atan2(dy, dx);
	    if (radian < startAngle) {
	        while (radian <= startAngle) {
	            radian += Math.PI * 2;
	        }
	    }
	    if (radian > endAngle) {
	        while (radian >= endAngle) {
	            radian -= Math.PI * 2;
	        }
	    }
	    return {
	        radius,
	        angle: radian
	    };
	}
	function getAngleByPoint(center, point) {
	    return Math.atan2(point.y - center.y, point.x - center.x);
	}
	function normalizeAngle(angle) {
	    while (angle < 0) {
	        angle += Math.PI * 2;
	    }
	    while (angle >= Math.PI * 2) {
	        angle -= Math.PI * 2;
	    }
	    return angle;
	}
	function findBoundaryAngles(startAngle, endAngle) {
	    const deltaAngle = Math.abs(endAngle - startAngle);
	    if (deltaAngle >= 2 * Math.PI || 2 * Math.PI - deltaAngle < 1e-6) {
	        return [0, Math.PI / 2, Math.PI, 1.5 * Math.PI];
	    }
	    const min = Math.min(startAngle, endAngle);
	    const normalMin = normalizeAngle(min);
	    const normalMax = normalMin + deltaAngle;
	    const steps = [normalMin, normalMax];
	    let directionAngle = (Math.floor(normalMin / Math.PI) * Math.PI) / 2;
	    while (directionAngle < normalMax) {
	        if (directionAngle > normalMin) {
	            steps.push(directionAngle);
	        }
	        directionAngle += Math.PI / 2;
	    }
	    return steps;
	}
	function calculateMaxRadius(rect, center, startAngle, endAngle) {
	    const { x, y } = center;
	    const steps = findBoundaryAngles(startAngle, endAngle);
	    const { width, height } = rect;
	    const radiusList = [];
	    steps.forEach(step => {
	        const sin = Math.sin(step);
	        const cos = Math.cos(step);
	        if (sin === 1) {
	            radiusList.push(height - y);
	        }
	        else if (sin === -1) {
	            radiusList.push(y);
	        }
	        else if (cos === 1) {
	            radiusList.push(width - x);
	        }
	        else if (cos === -1) {
	            radiusList.push(x);
	        }
	        else {
	            if (sin > 0) {
	                radiusList.push(Math.abs((height - y) / sin));
	            }
	            else {
	                radiusList.push(Math.abs(y / sin));
	            }
	            if (cos > 0) {
	                radiusList.push(Math.abs((width - x) / cos));
	            }
	            else {
	                radiusList.push(Math.abs(x / cos));
	            }
	        }
	    });
	    return Math.min.apply(null, radiusList);
	}
	function computeQuadrant(angle) {
	    angle = normalizeAngle(angle);
	    if (angle > 0 && angle <= Math.PI / 2) {
	        return 2;
	    }
	    else if (angle > Math.PI / 2 && angle <= Math.PI) {
	        return 3;
	    }
	    else if (angle > Math.PI && angle <= (3 * Math.PI) / 2) {
	        return 4;
	    }
	    return 1;
	}

	function sub(out, v1, v2) {
	    out[0] = v1[0] - v2[0];
	    out[1] = v1[1] - v2[1];
	}
	function isIntersect(left1, right1, left2, right2) {
	    let min1 = left1[0];
	    let max1 = right1[0];
	    let min2 = left2[0];
	    let max2 = right2[0];
	    if (max1 < min1) {
	        [min1, max1] = [max1, min1];
	    }
	    if (max2 < min2) {
	        [max2, min2] = [min2, max2];
	    }
	    if (max1 < min2 || max2 < min1) {
	        return false;
	    }
	    (min1 = left1[1]), (max1 = right1[1]), (min2 = left2[1]), (max2 = right2[1]);
	    if (max1 < min1) {
	        [min1, max1] = [max1, min1];
	    }
	    if (max2 < min2) {
	        [max2, min2] = [min2, max2];
	    }
	    if (max1 < min2 || max2 < min1) {
	        return false;
	    }
	    return true;
	}
	function getIntersectPoint(left1, right1, left2, right2) {
	    if (!isIntersect(left1, right1, left2, right2)) {
	        return false;
	    }
	    const dir1 = [0, 0];
	    const dir2 = [0, 0];
	    const tempVec = [0, 0];
	    sub(dir1, right1, left1);
	    sub(dir2, right2, left2);
	    if (fuzzyEqualVec(dir1, dir2)) {
	        return true;
	    }
	    sub(tempVec, left2, left1);
	    const t = crossProduct(tempVec, dir2) / crossProduct(dir1, dir2);
	    if (t >= 0 && t <= 1) {
	        return [left1[0] + dir1[0] * t, left1[1] + dir1[1] * t];
	    }
	    return false;
	}
	function getRectIntersect(bbox1, bbox2, format) {
	    if (bbox1 === null) {
	        return bbox2;
	    }
	    if (bbox2 === null) {
	        return bbox1;
	    }
	    const { x11, x12, y11, y12, x21, x22, y21, y22 } = formatTwoBBox(bbox1, bbox2, format);
	    if (x11 >= x22 || x12 <= x21 || y11 >= y22 || y12 <= y21) {
	        return { x1: 0, y1: 0, x2: 0, y2: 0 };
	    }
	    return { x1: Math.max(x11, x21), y1: Math.max(y11, y21), x2: Math.min(x12, x22), y2: Math.min(y12, y22) };
	}
	exports.InnerBBox = void 0;
	(function (InnerBBox) {
	    InnerBBox[InnerBBox["NONE"] = 0] = "NONE";
	    InnerBBox[InnerBBox["BBOX1"] = 1] = "BBOX1";
	    InnerBBox[InnerBBox["BBOX2"] = 2] = "BBOX2";
	})(exports.InnerBBox || (exports.InnerBBox = {}));
	const formatTwoBBox = (bbox1, bbox2, format) => {
	    let x11 = bbox1.x1;
	    let x12 = bbox1.x2;
	    let y11 = bbox1.y1;
	    let y12 = bbox1.y2;
	    let x21 = bbox2.x1;
	    let x22 = bbox2.x2;
	    let y21 = bbox2.y1;
	    let y22 = bbox2.y2;
	    if (format) {
	        if (x11 > x12) {
	            [x11, x12] = [x12, x11];
	        }
	        if (y11 > y12) {
	            [y11, y12] = [y12, y11];
	        }
	        if (x21 > x22) {
	            [x21, x22] = [x22, x21];
	        }
	        if (y21 > y22) {
	            [y21, y22] = [y22, y21];
	        }
	    }
	    return { x11, x12, y11, y12, x21, x22, y21, y22 };
	};
	function rectInsideAnotherRect(bbox1, bbox2, format) {
	    if (!bbox1 || !bbox2) {
	        return exports.InnerBBox.NONE;
	    }
	    const { x11, x12, y11, y12, x21, x22, y21, y22 } = formatTwoBBox(bbox1, bbox2, format);
	    if (x11 > x21 && x12 < x22 && y11 > y21 && y12 < y22) {
	        return exports.InnerBBox.BBOX1;
	    }
	    if (x21 > x11 && x22 < x12 && y21 > y11 && y22 < y12) {
	        return exports.InnerBBox.BBOX2;
	    }
	    return exports.InnerBBox.NONE;
	}
	function isRectIntersect(bbox1, bbox2, format) {
	    if (bbox1 && bbox2) {
	        if (!format) {
	            if (bbox1.x1 > bbox2.x2 || bbox1.x2 < bbox2.x1 || bbox1.y1 > bbox2.y2 || bbox1.y2 < bbox2.y1) {
	                return false;
	            }
	            return true;
	        }
	        const { x11, x12, y11, y12, x21, x22, y21, y22 } = formatTwoBBox(bbox1, bbox2, true);
	        if (x11 > x22 || x12 < x21 || y11 > y22 || y12 < y21) {
	            return false;
	        }
	        return true;
	    }
	    return true;
	}
	function pointInRect(point, bbox, format) {
	    if (!bbox) {
	        return true;
	    }
	    if (!format) {
	        return point.x >= bbox.x1 && point.x <= bbox.x2 && point.y >= bbox.y1 && point.y <= bbox.y2;
	    }
	    let x11 = bbox.x1;
	    let x12 = bbox.x2;
	    let y11 = bbox.y1;
	    let y12 = bbox.y2;
	    if (x11 > x12) {
	        [x11, x12] = [x12, x11];
	    }
	    if (y11 > y12) {
	        [y11, y12] = [y12, y11];
	    }
	    return point.x >= x11 && point.x <= x12 && point.y >= y11 && point.y <= y12;
	}
	function getProjectionRadius(checkAxis, axis) {
	    return Math.abs(axis[0] * checkAxis[0] + axis[1] * checkAxis[1]);
	}
	function rotatePoint({ x, y }, rad, origin = { x: 0, y: 0 }) {
	    return {
	        x: (x - origin.x) * Math.cos(rad) - (y - origin.y) * Math.sin(rad) + origin.x,
	        y: (x - origin.x) * Math.sin(rad) + (y - origin.y) * Math.cos(rad) + origin.y
	    };
	}
	function getCenterPoint(box) {
	    return {
	        x: (box.x1 + box.x2) / 2,
	        y: (box.y1 + box.y2) / 2
	    };
	}
	function toRect(box, isDeg) {
	    const deg = isDeg ? degreeToRadian(box.angle) : box.angle;
	    const cp = getCenterPoint(box);
	    return [
	        rotatePoint({
	            x: box.x1,
	            y: box.y1
	        }, deg, cp),
	        rotatePoint({
	            x: box.x2,
	            y: box.y1
	        }, deg, cp),
	        rotatePoint({
	            x: box.x2,
	            y: box.y2
	        }, deg, cp),
	        rotatePoint({
	            x: box.x1,
	            y: box.y2
	        }, deg, cp)
	    ];
	}
	function isRotateAABBIntersect(box1, box2, isDeg = false) {
	    const rect1 = toRect(box1, isDeg);
	    const rect2 = toRect(box2, isDeg);
	    const vector = (start, end) => {
	        return [end.x - start.x, end.y - start.y];
	    };
	    const p1 = getCenterPoint(box1);
	    const p2 = getCenterPoint(box2);
	    const vp1p2 = vector(p1, p2);
	    const AB = vector(rect1[0], rect1[1]);
	    const BC = vector(rect1[1], rect1[2]);
	    const A1B1 = vector(rect2[0], rect2[1]);
	    const B1C1 = vector(rect2[1], rect2[2]);
	    const deg11 = isDeg ? degreeToRadian(box1.angle) : box1.angle;
	    let deg12 = isDeg ? degreeToRadian(90 - box1.angle) : box1.angle + halfPi;
	    const deg21 = isDeg ? degreeToRadian(box2.angle) : box2.angle;
	    let deg22 = isDeg ? degreeToRadian(90 - box2.angle) : box2.angle + halfPi;
	    if (deg12 > pi2) {
	        deg12 -= pi2;
	    }
	    if (deg22 > pi2) {
	        deg22 -= pi2;
	    }
	    const isCover = (checkAxisRadius, deg, targetAxis1, targetAxis2) => {
	        const checkAxis = [Math.cos(deg), Math.sin(deg)];
	        const targetAxisRadius = (getProjectionRadius(checkAxis, targetAxis1) + getProjectionRadius(checkAxis, targetAxis2)) / 2;
	        const centerPointRadius = getProjectionRadius(checkAxis, vp1p2);
	        return checkAxisRadius + targetAxisRadius > centerPointRadius;
	    };
	    return (isCover((box1.x2 - box1.x1) / 2, deg11, A1B1, B1C1) &&
	        isCover((box1.y2 - box1.y1) / 2, deg12, A1B1, B1C1) &&
	        isCover((box2.x2 - box2.x1) / 2, deg21, AB, BC) &&
	        isCover((box2.y2 - box2.y1) / 2, deg22, AB, BC));
	}

	let x1;
	let y1;
	let x2;
	let y2;
	function getAABBFromPoints(points) {
	    (x1 = Infinity), (y1 = Infinity), (x2 = -Infinity), (y2 = -Infinity);
	    points.forEach(point => {
	        if (x1 > point.x) {
	            x1 = point.x;
	        }
	        if (x2 < point.x) {
	            x2 = point.x;
	        }
	        if (y1 > point.y) {
	            y1 = point.y;
	        }
	        if (y2 < point.y) {
	            y2 = point.y;
	        }
	    });
	    return { x1, y1, x2, y2 };
	}
	function pointInAABB(point, aabb) {
	    return pointInRect(point, aabb, false);
	}
	function unionAABB(bounds1, bounds2, buffer = 3, format = false) {
	    let x11 = bounds1.x1;
	    let x12 = bounds1.x2;
	    let y11 = bounds1.y1;
	    let y12 = bounds1.y2;
	    let x21 = bounds2.x1;
	    let x22 = bounds2.x2;
	    let y21 = bounds2.y1;
	    let y22 = bounds2.y2;
	    if (format) {
	        let temp;
	        if (x11 > x12) {
	            temp = x11;
	            x11 = x12;
	            x12 = temp;
	        }
	        if (y11 > y12) {
	            temp = y11;
	            y11 = y12;
	            y12 = temp;
	        }
	        if (x21 > x22) {
	            temp = x21;
	            x21 = x22;
	            x22 = temp;
	        }
	        if (y21 > y22) {
	            temp = y21;
	            y21 = y22;
	            y22 = temp;
	        }
	    }
	    if (x11 >= x22 || x12 <= x21 || y11 >= y22 || y12 <= y21) {
	        return [bounds1, bounds2];
	    }
	    const area1 = (x12 - x11 + buffer * 2) * (y12 - y11 + buffer * 2);
	    const area2 = (x22 - x21 + buffer * 2) * (y22 - y21 + buffer * 2);
	    const x1 = Math.min(x11, x21);
	    const y1 = Math.min(y11, y21);
	    const x2 = Math.max(x12, x22);
	    const y2 = Math.max(y12, y22);
	    const unionArea = (x2 - x1) * (y2 - y1);
	    if (area1 + area2 > unionArea) {
	        return [{ x1, x2, y1, y2 }];
	    }
	    return [bounds1, bounds2];
	}
	function mergeAABB(boundsList) {
	    const nextList = [];
	    function _merge(baseBound, list) {
	        const l = [];
	        list.forEach(b => {
	            let arr;
	            if ((arr = unionAABB(baseBound, b)).length > 1) {
	                l.push(b);
	                return;
	            }
	            baseBound = arr[0];
	        });
	        nextList.push(baseBound);
	        l.length && _merge(l[0], l.slice(1));
	    }
	    _merge(boundsList[0], boundsList.slice(1));
	    return nextList;
	}

	let dirX;
	let dirY;
	let normalX;
	let normalY;
	let len;
	let lineWidthDiv2;
	let width;
	let height;
	function getOBBFromLine(point1, point2, lineWidth) {
	    dirX = point2.x - point1.x;
	    dirY = point2.y - point1.y;
	    (normalX = dirY), (normalY = -dirX);
	    width = len = Math.sqrt(normalX * normalX + normalY * normalY);
	    height = lineWidth;
	    normalX /= len;
	    normalY /= len;
	    lineWidthDiv2 = lineWidth / 2;
	    dirX = lineWidthDiv2 * normalX;
	    dirY = lineWidthDiv2 * normalY;
	    const point11 = { x: point1.x + dirX, y: point1.y + dirY };
	    const point12 = { x: point1.x - dirX, y: point1.y - dirY };
	    const point13 = { x: point2.x + dirX, y: point2.y + dirY };
	    const point14 = { x: point2.x - dirX, y: point2.y - dirY };
	    return {
	        point1: point11,
	        point2: point12,
	        point3: point13,
	        point4: point14,
	        width,
	        height,
	        left: Math.min(point1.x, point2.x) - Math.abs(dirX),
	        top: Math.min(point1.y, point2.y) - Math.abs(dirY)
	    };
	}
	const point1 = { x: 0, y: 0 };
	const point2 = { x: 0, y: 0 };
	function pointInOBB(point, obb) {
	    point1.x = (obb.point1.x + obb.point2.x) / 2;
	    point1.y = (obb.point1.y + obb.point2.y) / 2;
	    point2.x = (obb.point3.x + obb.point4.x) / 2;
	    point2.y = (obb.point3.y + obb.point4.y) / 2;
	    return pointInLine(point, point1, point2, obb.height);
	}
	function pointInLine(point, point1, point2, lineWidth) {
	    return lengthFromPointToLine(point, point1, point2) <= lineWidth / 2 && pointBetweenLine(point, point1, point2);
	}
	const dir1 = { x: 0, y: 0 };
	const dir2 = { x: 0, y: 0 };
	const normal = { x: 0, y: 0 };
	function pointBetweenLine(point, point1, point2) {
	    dir1.x = point1.x - point.x;
	    dir1.y = point1.y - point.y;
	    dir2.x = point2.x - point.x;
	    dir2.y = point2.y - point.y;
	    normal.x = point1.y - point2.y;
	    normal.y = point2.x - point1.x;
	    return crossProductPoint(dir1, normal) * crossProductPoint(dir2, normal) < 0;
	}

	function getContextFont$1({ fontStyle, fontVariant, fontWeight, fontSize, fontFamily }) {
	    return ('' +
	        (fontStyle ? fontStyle + ' ' : '') +
	        (fontVariant ? fontVariant + ' ' : '') +
	        (fontWeight ? fontWeight + ' ' : '') +
	        (fontSize || 12) +
	        'px ' +
	        (fontFamily ? fontFamily : 'sans-serif'));
	}
	class GraphicUtil {
	    constructor(canvas) {
	        this.canvas = canvas;
	        if (canvas) {
	            this.ctx = canvas.getContext('2d');
	        }
	    }
	    setCanvas(canvas) {
	        this.canvas = canvas;
	        if (canvas) {
	            this.ctx = canvas.getContext('2d');
	        }
	    }
	    measureText(tc) {
	        if (!this.canvas) {
	            console.warn('[warn] no canvas, measureText might be not accurate');
	            return this.estimate(tc);
	        }
	        return this.measureTextByCanvas(tc);
	    }
	    measureTextByCanvas(tc) {
	        if (!this.ctx) {
	            console.error('[error!!!]measureTextByCanvas can not be called without canvas');
	            return { width: -1, height: tc.fontSize };
	        }
	        this.ctx.font = getContextFont$1(tc);
	        return {
	            width: this.ctx.measureText(tc.text).width,
	            height: tc.fontSize
	        };
	    }
	    estimate({ text, fontSize }) {
	        let eCharLen = 0;
	        let cCharLen = 0;
	        for (let i = 0; i < text.length; i++) {
	            text.charCodeAt(i) < 128 ? eCharLen++ : cCharLen++;
	        }
	        return {
	            width: ~~(0.8 * eCharLen * fontSize + cCharLen * fontSize),
	            height: fontSize
	        };
	    }
	    static getDefaultUtils(canvas) {
	        if (!GraphicUtil.instance) {
	            GraphicUtil.instance = new GraphicUtil(canvas);
	        }
	        return GraphicUtil.instance;
	    }
	}

	const EPSILON = 1e-8;
	function lineIntersectPolygon(a1x, a1y, a2x, a2y, points) {
	    for (let i = 0, p2 = points[points.length - 1]; i < points.length; i++) {
	        const p = points[i];
	        if (isIntersect([a1x, a1y], [a2x, a2y], [p.x, p.y], [p2.x, p2.y])) {
	            return true;
	        }
	        p2 = p;
	    }
	    return false;
	}
	function polygonContainPoint(points, x, y) {
	    let w = 0;
	    let p = points[0];
	    if (!p) {
	        return false;
	    }
	    for (let i = 1; i < points.length; i++) {
	        const p2 = points[i];
	        w += isPointInLine(p.x, p.y, p2.x, p2.y, x, y);
	        p = p2;
	    }
	    const p0 = points[0];
	    if (!isAroundEqual(p.x, p0.x) || !isAroundEqual(p.y, p0.y)) {
	        w += isPointInLine(p.x, p.y, p0.x, p0.y, x, y);
	    }
	    return w !== 0;
	}
	function isPointInLine(x0, y0, x1, y1, x, y) {
	    if ((y > y0 && y > y1) || (y < y0 && y < y1)) {
	        return 0;
	    }
	    if (y1 === y0) {
	        return 0;
	    }
	    const t = (y - y0) / (y1 - y0);
	    let dir = y1 < y0 ? 1 : -1;
	    if (t === 1 || t === 0) {
	        dir = y1 < y0 ? 0.5 : -0.5;
	    }
	    const x_ = t * (x1 - x0) + x0;
	    return x_ === x ? Infinity : x_ > x ? dir : 0;
	}
	function isAroundEqual(a, b) {
	    return Math.abs(a - b) < EPSILON;
	}
	function polygonIntersectPolygon(pointsA, pointsB) {
	    for (let i = 0; i < pointsB.length; i++) {
	        if (polygonContainPoint(pointsA, pointsB[i].x, pointsB[i].y)) {
	            return true;
	        }
	        if (i > 0 && lineIntersectPolygon(pointsB[i - 1].x, pointsB[i - 1].y, pointsB[i].x, pointsB[i].y, pointsA)) {
	            return true;
	        }
	    }
	    return false;
	}

	const calculateAnchorOfArc = (arcAttr, anchorType) => {
	    const { startAngle, endAngle, innerRadius, outerRadius } = arcAttr;
	    let angle = (startAngle + endAngle) / 2;
	    let radius = (innerRadius + outerRadius) / 2;
	    switch (anchorType) {
	        case 'inner-start':
	            angle = startAngle;
	            radius = innerRadius;
	            break;
	        case 'outer-start':
	            angle = startAngle;
	            radius = outerRadius;
	            break;
	        case 'inner-end':
	            angle = endAngle;
	            radius = innerRadius;
	            break;
	        case 'outer-end':
	            angle = endAngle;
	            radius = outerRadius;
	            break;
	        case 'inner-middle':
	            radius = innerRadius;
	            break;
	        case 'outer-middle':
	            radius = outerRadius;
	            break;
	    }
	    return { angle, radius };
	};

	function stringWidth(string, ambiguousCharacterIsNarrow = true) {
	    if (typeof string !== 'string' || string.length === 0) {
	        return 0;
	    }
	    string = stripAnsi(string);
	    if (string.length === 0) {
	        return 0;
	    }
	    string = string.replace(emojiRegex(), '  ');
	    const ambiguousCharacterWidth = ambiguousCharacterIsNarrow ? 1 : 2;
	    let width = 0;
	    for (const character of string) {
	        const codePoint = character.codePointAt(0);
	        if (codePoint <= 0x1f || (codePoint >= 0x7f && codePoint <= 0x9f)) {
	            continue;
	        }
	        if (codePoint >= 0x300 && codePoint <= 0x36f) {
	            continue;
	        }
	        const code = eastAsianCharacterInfo(character);
	        switch (code) {
	            case 'F':
	            case 'W':
	                width += 2;
	                break;
	            case 'A':
	                width += ambiguousCharacterWidth;
	                break;
	            default:
	                width += 1;
	        }
	    }
	    return width;
	}
	const stripAnsi = (string) => {
	    if (typeof string !== 'string') {
	        throw new TypeError(`Expected a \`string\`, got \`${typeof string}\``);
	    }
	    return string.replace(ansiRegex(), '');
	};
	const ansiRegex = ({ onlyFirst = false } = {}) => {
	    const pattern = [
	        '[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)',
	        '(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))'
	    ].join('|');
	    return new RegExp(pattern, onlyFirst ? undefined : 'g');
	};
	const eastAsianCharacterInfo = (character) => {
	    let x = character.charCodeAt(0);
	    let y = character.length === 2 ? character.charCodeAt(1) : 0;
	    let codePoint = x;
	    if (0xd800 <= x && x <= 0xdbff && 0xdc00 <= y && y <= 0xdfff) {
	        x &= 0x3ff;
	        y &= 0x3ff;
	        codePoint = (x << 10) | y;
	        codePoint += 0x10000;
	    }
	    if (0x3000 === codePoint ||
	        (0xff01 <= codePoint && codePoint <= 0xff60) ||
	        (0xffe0 <= codePoint && codePoint <= 0xffe6)) {
	        return 'F';
	    }
	    if (0x20a9 === codePoint ||
	        (0xff61 <= codePoint && codePoint <= 0xffbe) ||
	        (0xffc2 <= codePoint && codePoint <= 0xffc7) ||
	        (0xffca <= codePoint && codePoint <= 0xffcf) ||
	        (0xffd2 <= codePoint && codePoint <= 0xffd7) ||
	        (0xffda <= codePoint && codePoint <= 0xffdc) ||
	        (0xffe8 <= codePoint && codePoint <= 0xffee)) {
	        return 'H';
	    }
	    if ((0x1100 <= codePoint && codePoint <= 0x115f) ||
	        (0x11a3 <= codePoint && codePoint <= 0x11a7) ||
	        (0x11fa <= codePoint && codePoint <= 0x11ff) ||
	        (0x2329 <= codePoint && codePoint <= 0x232a) ||
	        (0x2e80 <= codePoint && codePoint <= 0x2e99) ||
	        (0x2e9b <= codePoint && codePoint <= 0x2ef3) ||
	        (0x2f00 <= codePoint && codePoint <= 0x2fd5) ||
	        (0x2ff0 <= codePoint && codePoint <= 0x2ffb) ||
	        (0x3001 <= codePoint && codePoint <= 0x303e) ||
	        (0x3041 <= codePoint && codePoint <= 0x3096) ||
	        (0x3099 <= codePoint && codePoint <= 0x30ff) ||
	        (0x3105 <= codePoint && codePoint <= 0x312d) ||
	        (0x3131 <= codePoint && codePoint <= 0x318e) ||
	        (0x3190 <= codePoint && codePoint <= 0x31ba) ||
	        (0x31c0 <= codePoint && codePoint <= 0x31e3) ||
	        (0x31f0 <= codePoint && codePoint <= 0x321e) ||
	        (0x3220 <= codePoint && codePoint <= 0x3247) ||
	        (0x3250 <= codePoint && codePoint <= 0x32fe) ||
	        (0x3300 <= codePoint && codePoint <= 0x4dbf) ||
	        (0x4e00 <= codePoint && codePoint <= 0xa48c) ||
	        (0xa490 <= codePoint && codePoint <= 0xa4c6) ||
	        (0xa960 <= codePoint && codePoint <= 0xa97c) ||
	        (0xac00 <= codePoint && codePoint <= 0xd7a3) ||
	        (0xd7b0 <= codePoint && codePoint <= 0xd7c6) ||
	        (0xd7cb <= codePoint && codePoint <= 0xd7fb) ||
	        (0xf900 <= codePoint && codePoint <= 0xfaff) ||
	        (0xfe10 <= codePoint && codePoint <= 0xfe19) ||
	        (0xfe30 <= codePoint && codePoint <= 0xfe52) ||
	        (0xfe54 <= codePoint && codePoint <= 0xfe66) ||
	        (0xfe68 <= codePoint && codePoint <= 0xfe6b) ||
	        (0x1b000 <= codePoint && codePoint <= 0x1b001) ||
	        (0x1f200 <= codePoint && codePoint <= 0x1f202) ||
	        (0x1f210 <= codePoint && codePoint <= 0x1f23a) ||
	        (0x1f240 <= codePoint && codePoint <= 0x1f248) ||
	        (0x1f250 <= codePoint && codePoint <= 0x1f251) ||
	        (0x20000 <= codePoint && codePoint <= 0x2f73f) ||
	        (0x2b740 <= codePoint && codePoint <= 0x2fffd) ||
	        (0x30000 <= codePoint && codePoint <= 0x3fffd)) {
	        return 'W';
	    }
	    if ((0x0020 <= codePoint && codePoint <= 0x007e) ||
	        (0x00a2 <= codePoint && codePoint <= 0x00a3) ||
	        (0x00a5 <= codePoint && codePoint <= 0x00a6) ||
	        0x00ac === codePoint ||
	        0x00af === codePoint ||
	        (0x27e6 <= codePoint && codePoint <= 0x27ed) ||
	        (0x2985 <= codePoint && codePoint <= 0x2986)) {
	        return 'Na';
	    }
	    if (0x00a1 === codePoint ||
	        0x00a4 === codePoint ||
	        (0x00a7 <= codePoint && codePoint <= 0x00a8) ||
	        0x00aa === codePoint ||
	        (0x00ad <= codePoint && codePoint <= 0x00ae) ||
	        (0x00b0 <= codePoint && codePoint <= 0x00b4) ||
	        (0x00b6 <= codePoint && codePoint <= 0x00ba) ||
	        (0x00bc <= codePoint && codePoint <= 0x00bf) ||
	        0x00c6 === codePoint ||
	        0x00d0 === codePoint ||
	        (0x00d7 <= codePoint && codePoint <= 0x00d8) ||
	        (0x00de <= codePoint && codePoint <= 0x00e1) ||
	        0x00e6 === codePoint ||
	        (0x00e8 <= codePoint && codePoint <= 0x00ea) ||
	        (0x00ec <= codePoint && codePoint <= 0x00ed) ||
	        0x00f0 === codePoint ||
	        (0x00f2 <= codePoint && codePoint <= 0x00f3) ||
	        (0x00f7 <= codePoint && codePoint <= 0x00fa) ||
	        0x00fc === codePoint ||
	        0x00fe === codePoint ||
	        0x0101 === codePoint ||
	        0x0111 === codePoint ||
	        0x0113 === codePoint ||
	        0x011b === codePoint ||
	        (0x0126 <= codePoint && codePoint <= 0x0127) ||
	        0x012b === codePoint ||
	        (0x0131 <= codePoint && codePoint <= 0x0133) ||
	        0x0138 === codePoint ||
	        (0x013f <= codePoint && codePoint <= 0x0142) ||
	        0x0144 === codePoint ||
	        (0x0148 <= codePoint && codePoint <= 0x014b) ||
	        0x014d === codePoint ||
	        (0x0152 <= codePoint && codePoint <= 0x0153) ||
	        (0x0166 <= codePoint && codePoint <= 0x0167) ||
	        0x016b === codePoint ||
	        0x01ce === codePoint ||
	        0x01d0 === codePoint ||
	        0x01d2 === codePoint ||
	        0x01d4 === codePoint ||
	        0x01d6 === codePoint ||
	        0x01d8 === codePoint ||
	        0x01da === codePoint ||
	        0x01dc === codePoint ||
	        0x0251 === codePoint ||
	        0x0261 === codePoint ||
	        0x02c4 === codePoint ||
	        0x02c7 === codePoint ||
	        (0x02c9 <= codePoint && codePoint <= 0x02cb) ||
	        0x02cd === codePoint ||
	        0x02d0 === codePoint ||
	        (0x02d8 <= codePoint && codePoint <= 0x02db) ||
	        0x02dd === codePoint ||
	        0x02df === codePoint ||
	        (0x0300 <= codePoint && codePoint <= 0x036f) ||
	        (0x0391 <= codePoint && codePoint <= 0x03a1) ||
	        (0x03a3 <= codePoint && codePoint <= 0x03a9) ||
	        (0x03b1 <= codePoint && codePoint <= 0x03c1) ||
	        (0x03c3 <= codePoint && codePoint <= 0x03c9) ||
	        0x0401 === codePoint ||
	        (0x0410 <= codePoint && codePoint <= 0x044f) ||
	        0x0451 === codePoint ||
	        0x2010 === codePoint ||
	        (0x2013 <= codePoint && codePoint <= 0x2016) ||
	        (0x2018 <= codePoint && codePoint <= 0x2019) ||
	        (0x201c <= codePoint && codePoint <= 0x201d) ||
	        (0x2020 <= codePoint && codePoint <= 0x2022) ||
	        (0x2024 <= codePoint && codePoint <= 0x2027) ||
	        0x2030 === codePoint ||
	        (0x2032 <= codePoint && codePoint <= 0x2033) ||
	        0x2035 === codePoint ||
	        0x203b === codePoint ||
	        0x203e === codePoint ||
	        0x2074 === codePoint ||
	        0x207f === codePoint ||
	        (0x2081 <= codePoint && codePoint <= 0x2084) ||
	        0x20ac === codePoint ||
	        0x2103 === codePoint ||
	        0x2105 === codePoint ||
	        0x2109 === codePoint ||
	        0x2113 === codePoint ||
	        0x2116 === codePoint ||
	        (0x2121 <= codePoint && codePoint <= 0x2122) ||
	        0x2126 === codePoint ||
	        0x212b === codePoint ||
	        (0x2153 <= codePoint && codePoint <= 0x2154) ||
	        (0x215b <= codePoint && codePoint <= 0x215e) ||
	        (0x2160 <= codePoint && codePoint <= 0x216b) ||
	        (0x2170 <= codePoint && codePoint <= 0x2179) ||
	        0x2189 === codePoint ||
	        (0x2190 <= codePoint && codePoint <= 0x2199) ||
	        (0x21b8 <= codePoint && codePoint <= 0x21b9) ||
	        0x21d2 === codePoint ||
	        0x21d4 === codePoint ||
	        0x21e7 === codePoint ||
	        0x2200 === codePoint ||
	        (0x2202 <= codePoint && codePoint <= 0x2203) ||
	        (0x2207 <= codePoint && codePoint <= 0x2208) ||
	        0x220b === codePoint ||
	        0x220f === codePoint ||
	        0x2211 === codePoint ||
	        0x2215 === codePoint ||
	        0x221a === codePoint ||
	        (0x221d <= codePoint && codePoint <= 0x2220) ||
	        0x2223 === codePoint ||
	        0x2225 === codePoint ||
	        (0x2227 <= codePoint && codePoint <= 0x222c) ||
	        0x222e === codePoint ||
	        (0x2234 <= codePoint && codePoint <= 0x2237) ||
	        (0x223c <= codePoint && codePoint <= 0x223d) ||
	        0x2248 === codePoint ||
	        0x224c === codePoint ||
	        0x2252 === codePoint ||
	        (0x2260 <= codePoint && codePoint <= 0x2261) ||
	        (0x2264 <= codePoint && codePoint <= 0x2267) ||
	        (0x226a <= codePoint && codePoint <= 0x226b) ||
	        (0x226e <= codePoint && codePoint <= 0x226f) ||
	        (0x2282 <= codePoint && codePoint <= 0x2283) ||
	        (0x2286 <= codePoint && codePoint <= 0x2287) ||
	        0x2295 === codePoint ||
	        0x2299 === codePoint ||
	        0x22a5 === codePoint ||
	        0x22bf === codePoint ||
	        0x2312 === codePoint ||
	        (0x2460 <= codePoint && codePoint <= 0x24e9) ||
	        (0x24eb <= codePoint && codePoint <= 0x254b) ||
	        (0x2550 <= codePoint && codePoint <= 0x2573) ||
	        (0x2580 <= codePoint && codePoint <= 0x258f) ||
	        (0x2592 <= codePoint && codePoint <= 0x2595) ||
	        (0x25a0 <= codePoint && codePoint <= 0x25a1) ||
	        (0x25a3 <= codePoint && codePoint <= 0x25a9) ||
	        (0x25b2 <= codePoint && codePoint <= 0x25b3) ||
	        (0x25b6 <= codePoint && codePoint <= 0x25b7) ||
	        (0x25bc <= codePoint && codePoint <= 0x25bd) ||
	        (0x25c0 <= codePoint && codePoint <= 0x25c1) ||
	        (0x25c6 <= codePoint && codePoint <= 0x25c8) ||
	        0x25cb === codePoint ||
	        (0x25ce <= codePoint && codePoint <= 0x25d1) ||
	        (0x25e2 <= codePoint && codePoint <= 0x25e5) ||
	        0x25ef === codePoint ||
	        (0x2605 <= codePoint && codePoint <= 0x2606) ||
	        0x2609 === codePoint ||
	        (0x260e <= codePoint && codePoint <= 0x260f) ||
	        (0x2614 <= codePoint && codePoint <= 0x2615) ||
	        0x261c === codePoint ||
	        0x261e === codePoint ||
	        0x2640 === codePoint ||
	        0x2642 === codePoint ||
	        (0x2660 <= codePoint && codePoint <= 0x2661) ||
	        (0x2663 <= codePoint && codePoint <= 0x2665) ||
	        (0x2667 <= codePoint && codePoint <= 0x266a) ||
	        (0x266c <= codePoint && codePoint <= 0x266d) ||
	        0x266f === codePoint ||
	        (0x269e <= codePoint && codePoint <= 0x269f) ||
	        (0x26be <= codePoint && codePoint <= 0x26bf) ||
	        (0x26c4 <= codePoint && codePoint <= 0x26cd) ||
	        (0x26cf <= codePoint && codePoint <= 0x26e1) ||
	        0x26e3 === codePoint ||
	        (0x26e8 <= codePoint && codePoint <= 0x26ff) ||
	        0x273d === codePoint ||
	        0x2757 === codePoint ||
	        (0x2776 <= codePoint && codePoint <= 0x277f) ||
	        (0x2b55 <= codePoint && codePoint <= 0x2b59) ||
	        (0x3248 <= codePoint && codePoint <= 0x324f) ||
	        (0xe000 <= codePoint && codePoint <= 0xf8ff) ||
	        (0xfe00 <= codePoint && codePoint <= 0xfe0f) ||
	        0xfffd === codePoint ||
	        (0x1f100 <= codePoint && codePoint <= 0x1f10a) ||
	        (0x1f110 <= codePoint && codePoint <= 0x1f12d) ||
	        (0x1f130 <= codePoint && codePoint <= 0x1f169) ||
	        (0x1f170 <= codePoint && codePoint <= 0x1f19a) ||
	        (0xe0100 <= codePoint && codePoint <= 0xe01ef) ||
	        (0xf0000 <= codePoint && codePoint <= 0xffffd) ||
	        (0x100000 <= codePoint && codePoint <= 0x10fffd)) {
	        return 'A';
	    }
	    return 'N';
	};
	const emojiRegex = () => {
	    return /[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;
	};

	function getContextFont(text, defaultAttr = {}, fontSizeScale) {
	    if (!fontSizeScale) {
	        fontSizeScale = 1;
	    }
	    const { fontStyle = defaultAttr.fontStyle, fontVariant = defaultAttr.fontVariant, fontWeight = defaultAttr.fontWeight, fontSize = defaultAttr.fontSize, fontFamily = defaultAttr.fontFamily } = text;
	    return ('' +
	        (fontStyle ? fontStyle + ' ' : '') +
	        (fontVariant ? fontVariant + ' ' : '') +
	        (fontWeight ? fontWeight + ' ' : '') +
	        fontSize * fontSizeScale +
	        'px ' +
	        (fontFamily ? fontFamily : 'sans-serif'));
	}

	class TextMeasure {
	    constructor(option, textSpec) {
	        this._numberCharSize = null;
	        this._fullCharSize = null;
	        this._letterCharSize = null;
	        this._specialCharSizeMap = {};
	        this._canvas = null;
	        this._context = null;
	        this._contextSaved = false;
	        this._notSupportCanvas = false;
	        this._notSupportVRender = false;
	        this._userSpec = {};
	        this.specialCharSet = '-/: .,@%\'"~';
	        this._option = option;
	        this._userSpec = textSpec !== null && textSpec !== void 0 ? textSpec : {};
	        this.textSpec = this._initSpec();
	        if (isValid(option.specialCharSet)) {
	            this.specialCharSet = option.specialCharSet;
	        }
	        this._standardMethod = isValid(option.getTextBounds)
	            ? this.fullMeasure.bind(this)
	            : this.measureWithNaiveCanvas.bind(this);
	    }
	    initContext() {
	        if (this._notSupportCanvas) {
	            return false;
	        }
	        if (isNil(this._canvas)) {
	            if (isValid(this._option.getCanvasForMeasure)) {
	                this._canvas = this._option.getCanvasForMeasure();
	            }
	            if (isNil(this._canvas) &&
	                typeof window !== 'undefined' &&
	                typeof window.document !== 'undefined' &&
	                globalThis &&
	                isValid(globalThis.document)) {
	                this._canvas = globalThis.document.createElement('canvas');
	            }
	        }
	        if (isNil(this._context) && isValid(this._canvas)) {
	            const context = this._canvas.getContext('2d');
	            if (isValid(context)) {
	                context.save();
	                context.font = getContextFont(this.textSpec);
	                this._contextSaved = true;
	                this._context = context;
	            }
	        }
	        if (isNil(this._context)) {
	            this._notSupportCanvas = true;
	            return false;
	        }
	        return true;
	    }
	    _initSpec() {
	        var _a, _b, _c;
	        const { defaultFontParams = {} } = this._option;
	        const { fontStyle = defaultFontParams.fontStyle, fontVariant = defaultFontParams.fontVariant, fontWeight = (_a = defaultFontParams.fontWeight) !== null && _a !== void 0 ? _a : 'normal', fontSize = (_b = defaultFontParams.fontSize) !== null && _b !== void 0 ? _b : 12, fontFamily = (_c = defaultFontParams.fontFamily) !== null && _c !== void 0 ? _c : 'sans-serif', align, textAlign = align !== null && align !== void 0 ? align : 'center', baseline, textBaseline = baseline !== null && baseline !== void 0 ? baseline : 'middle', ellipsis, limit } = this._userSpec;
	        let { lineHeight = fontSize } = this._userSpec;
	        if (isString(lineHeight) && lineHeight[lineHeight.length - 1] === '%') {
	            const scale = Number.parseFloat(lineHeight.substring(0, lineHeight.length - 1)) / 100;
	            lineHeight = fontSize * scale;
	        }
	        return {
	            fontStyle,
	            fontVariant,
	            fontFamily,
	            fontSize,
	            fontWeight,
	            textAlign,
	            textBaseline,
	            ellipsis,
	            limit,
	            lineHeight
	        };
	    }
	    measure(text, method) {
	        switch (method) {
	            case 'vrender':
	            case 'canopus':
	                return this.fullMeasure(text);
	            case 'canvas':
	                return this.measureWithNaiveCanvas(text);
	            case 'simple':
	                return this.quickMeasureWithoutCanvas(text);
	            case 'quick':
	            default:
	                return this.quickMeasure(text);
	        }
	    }
	    fullMeasure(text) {
	        if (isNil(text)) {
	            return { width: 0, height: 0 };
	        }
	        if (isNil(this._option.getTextBounds) || !this._notSupportVRender) {
	            return this.measureWithNaiveCanvas(text);
	        }
	        const { fontFamily, fontSize, fontWeight, textAlign, textBaseline, ellipsis, limit, lineHeight } = this.textSpec;
	        let size;
	        try {
	            const bounds = this._option.getTextBounds({
	                text,
	                fontFamily,
	                fontSize,
	                fontWeight,
	                textAlign,
	                textBaseline,
	                ellipsis: !!ellipsis,
	                maxLineWidth: limit || Infinity,
	                lineHeight
	            });
	            size = { width: bounds.width(), height: bounds.height() };
	        }
	        catch (e) {
	            this._notSupportVRender = true;
	            size = this.measureWithNaiveCanvas(text);
	        }
	        return size;
	    }
	    measureWithNaiveCanvas(text) {
	        return this._measureReduce(text, this._measureWithNaiveCanvas.bind(this));
	    }
	    _measureWithNaiveCanvas(text) {
	        var _a;
	        if (!this.initContext()) {
	            return this._quickMeasureWithoutCanvas(text);
	        }
	        const metrics = this._context.measureText(text);
	        const { fontSize, lineHeight } = this.textSpec;
	        return {
	            width: metrics.width,
	            height: (_a = lineHeight) !== null && _a !== void 0 ? _a : fontSize,
	            fontBoundingBoxAscent: metrics.fontBoundingBoxAscent,
	            fontBoundingBoxDescent: metrics.fontBoundingBoxDescent
	        };
	    }
	    quickMeasure(text) {
	        return this._measureReduce(text, this._quickMeasure.bind(this));
	    }
	    _quickMeasure(text) {
	        const totalSize = {
	            width: 0,
	            height: 0
	        };
	        for (let i = 0; i < text.length; i++) {
	            const char = text[i];
	            let size = this._measureSpecialChar(char);
	            if (isNil(size) && TextMeasure.NUMBERS_CHAR_SET.includes(char)) {
	                size = this._measureNumberChar();
	            }
	            if (isNil(size) && ['F', 'W'].includes(eastAsianCharacterInfo(char))) {
	                size = this._measureFullSizeChar();
	            }
	            if (isNil(size)) {
	                size = this._measureLetterChar();
	            }
	            totalSize.width += size.width;
	            totalSize.height = Math.max(totalSize.height, size.height);
	            !isNil(size.fontBoundingBoxAscent) && (totalSize.fontBoundingBoxAscent = size.fontBoundingBoxAscent);
	            !isNil(size.fontBoundingBoxDescent) && (totalSize.fontBoundingBoxDescent = size.fontBoundingBoxDescent);
	        }
	        return totalSize;
	    }
	    quickMeasureWithoutCanvas(text) {
	        return this._measureReduce(text, this._quickMeasureWithoutCanvas.bind(this));
	    }
	    _quickMeasureWithoutCanvas(text) {
	        var _a;
	        const totalSize = {
	            width: 0,
	            height: 0
	        };
	        const { fontSize, lineHeight } = this.textSpec;
	        for (let i = 0; i < text.length; i++) {
	            const char = text[i];
	            const size = ['F', 'W'].includes(eastAsianCharacterInfo(char)) ? 1 : 0.53;
	            totalSize.width += size * fontSize;
	        }
	        totalSize.height = (_a = lineHeight) !== null && _a !== void 0 ? _a : fontSize;
	        return totalSize;
	    }
	    _measureReduce(text, processor) {
	        var _a;
	        const { fontSize, lineHeight } = this.textSpec;
	        const defaultResult = { width: 0, height: 0 };
	        if (isNil(text)) {
	            return defaultResult;
	        }
	        else if (isArray(text)) {
	            const textArr = text.filter(isValid).map(s => s.toString());
	            if (textArr.length === 0) {
	                return defaultResult;
	            }
	            else if (textArr.length === 1) {
	                return processor(textArr[0]);
	            }
	            return {
	                width: textArr.reduce((maxWidth, cur) => Math.max(maxWidth, processor(cur).width), 0),
	                height: textArr.length * (((_a = lineHeight) !== null && _a !== void 0 ? _a : fontSize) + 1) + 1
	            };
	        }
	        return processor(text.toString());
	    }
	    _measureNumberChar() {
	        if (isNil(this._numberCharSize)) {
	            const numberBounds = this._standardMethod(TextMeasure.NUMBERS_CHAR_SET);
	            this._numberCharSize = {
	                width: numberBounds.width / TextMeasure.NUMBERS_CHAR_SET.length,
	                height: numberBounds.height,
	                fontBoundingBoxAscent: numberBounds.fontBoundingBoxAscent,
	                fontBoundingBoxDescent: numberBounds.fontBoundingBoxDescent
	            };
	        }
	        return this._numberCharSize;
	    }
	    _measureFullSizeChar() {
	        if (isNil(this._fullCharSize)) {
	            this._fullCharSize = this._standardMethod(TextMeasure.FULL_SIZE_CHAR);
	        }
	        return this._fullCharSize;
	    }
	    _measureLetterChar() {
	        if (isNil(this._letterCharSize)) {
	            const alphabetBounds = this._standardMethod(TextMeasure.ALPHABET_CHAR_SET);
	            this._letterCharSize = {
	                width: alphabetBounds.width / TextMeasure.ALPHABET_CHAR_SET.length,
	                height: alphabetBounds.height,
	                fontBoundingBoxAscent: alphabetBounds.fontBoundingBoxAscent,
	                fontBoundingBoxDescent: alphabetBounds.fontBoundingBoxDescent
	            };
	        }
	        return this._letterCharSize;
	    }
	    _measureSpecialChar(char) {
	        if (isValid(this._specialCharSizeMap[char])) {
	            return this._specialCharSizeMap[char];
	        }
	        if (this.specialCharSet.includes(char)) {
	            this._specialCharSizeMap[char] = this._standardMethod(char);
	            return this._specialCharSizeMap[char];
	        }
	        return null;
	    }
	    release() {
	        if (isValid(this._canvas)) {
	            this._canvas = null;
	        }
	        if (isValid(this._context)) {
	            if (this._contextSaved) {
	                this._context.restore();
	                this._contextSaved = false;
	            }
	            this._context = null;
	        }
	    }
	}
	TextMeasure.ALPHABET_CHAR_SET = 'abcdefghijklmnopqrstuvwxyz';
	TextMeasure.NUMBERS_CHAR_SET = '0123456789';
	TextMeasure.FULL_SIZE_CHAR = '字';

	const calculateAnchorOfBounds = (bounds, anchorType) => {
	    const { x1, x2, y1, y2 } = bounds;
	    const rectWidth = Math.abs(x2 - x1);
	    const rectHeight = Math.abs(y2 - y1);
	    let anchorX = (x1 + x2) / 2;
	    let anchorY = (y1 + y2) / 2;
	    let sx = 0;
	    let sy = 0;
	    switch (anchorType) {
	        case 'top':
	        case 'inside-top':
	            sy = -0.5;
	            break;
	        case 'bottom':
	        case 'inside-bottom':
	            sy = 0.5;
	            break;
	        case 'left':
	        case 'inside-left':
	            sx = -0.5;
	            break;
	        case 'right':
	        case 'inside-right':
	            sx = 0.5;
	            break;
	        case 'top-right':
	            sx = 0.5;
	            sy = -0.5;
	            break;
	        case 'top-left':
	            sx = -0.5;
	            sy = -0.5;
	            break;
	        case 'bottom-right':
	            sx = 0.5;
	            sy = 0.5;
	            break;
	        case 'bottom-left':
	            sx = -0.5;
	            sy = 0.5;
	    }
	    anchorX += sx * rectWidth;
	    anchorY += sy * rectHeight;
	    return { x: anchorX, y: anchorY };
	};
	const aabbSeparation = (a, b) => {
	    return Math.max(b.x1 - a.x2, a.x1 - b.x2, b.y1 - a.y2, a.y1 - b.y2);
	};
	const obbSeparation = (a, b) => {
	    const axes = [
	        { x: Math.cos(a.angle), y: Math.sin(a.angle) },
	        { x: -Math.sin(a.angle), y: Math.cos(a.angle) },
	        { x: Math.cos(b.angle), y: Math.sin(a.angle) },
	        { x: -Math.sin(b.angle), y: Math.cos(a.angle) }
	    ];
	    function getProjectionRange(obb, axisX, axisY) {
	        const corners = obb.getRotatedCorners();
	        const projections = corners.map(p => p.x * axisX + p.y * axisY);
	        return { min: Math.min(...projections), max: Math.max(...projections) };
	    }
	    let maxDistance = 0;
	    for (const axis of axes) {
	        const rangeA = getProjectionRange(a, axis.x, axis.y);
	        const rangeB = getProjectionRange(b, axis.x, axis.y);
	        let distance;
	        if (rangeA.max < rangeB.min) {
	            distance = rangeB.min - rangeA.max;
	        }
	        else if (rangeB.max < rangeA.min) {
	            distance = rangeA.min - rangeB.max;
	        }
	        else {
	            distance = 0;
	        }
	        maxDistance = Math.max(maxDistance, distance);
	    }
	    return maxDistance;
	};

	function transformBoundsWithMatrix(out, bounds, matrix) {
	    const { x1, y1, x2, y2 } = bounds;
	    if (matrix.onlyTranslate()) {
	        if (out !== bounds) {
	            out.setValue(bounds.x1, bounds.y1, bounds.x2, bounds.y2);
	        }
	        out.translate(matrix.e, matrix.f);
	        return bounds;
	    }
	    out.clear();
	    out.add(matrix.a * x1 + matrix.c * y1 + matrix.e, matrix.b * x1 + matrix.d * y1 + matrix.f);
	    out.add(matrix.a * x2 + matrix.c * y1 + matrix.e, matrix.b * x2 + matrix.d * y1 + matrix.f);
	    out.add(matrix.a * x2 + matrix.c * y2 + matrix.e, matrix.b * x2 + matrix.d * y2 + matrix.f);
	    out.add(matrix.a * x1 + matrix.c * y2 + matrix.e, matrix.b * x1 + matrix.d * y2 + matrix.f);
	    return bounds;
	}
	function transformBounds(bounds, x, y, scaleX, scaleY, angle, rotateCenter) {
	    if (abs(scaleX) <= epsilon || abs(scaleY) <= epsilon) {
	        return;
	    }
	    scaleX !== 1 && bounds.scaleX(scaleX);
	    scaleY !== 1 && bounds.scaleY(scaleY);
	    if (isFinite(angle) && Math.abs(angle) > epsilon) {
	        let rx = 0;
	        let ry = 0;
	        if (rotateCenter !== undefined) {
	            rx = rotateCenter[0];
	            ry = rotateCenter[1];
	        }
	        bounds.rotate(angle, rx, ry);
	    }
	    bounds.translate(x, y);
	}
	class Bounds {
	    constructor(bounds) {
	        if (bounds) {
	            this.setValue(bounds.x1, bounds.y1, bounds.x2, bounds.y2);
	        }
	        else {
	            this.clear();
	        }
	    }
	    clone() {
	        return new Bounds(this);
	    }
	    clear() {
	        this.x1 = +Number.MAX_VALUE;
	        this.y1 = +Number.MAX_VALUE;
	        this.x2 = -Number.MAX_VALUE;
	        this.y2 = -Number.MAX_VALUE;
	        return this;
	    }
	    empty() {
	        return (this.x1 === +Number.MAX_VALUE &&
	            this.y1 === +Number.MAX_VALUE &&
	            this.x2 === -Number.MAX_VALUE &&
	            this.y2 === -Number.MAX_VALUE);
	    }
	    equals(b) {
	        return this.x1 === b.x1 && this.y1 === b.y1 && this.x2 === b.x2 && this.y2 === b.y2;
	    }
	    setValue(x1 = 0, y1 = 0, x2 = 0, y2 = 0) {
	        this.x1 = x1;
	        this.y1 = y1;
	        this.x2 = x2;
	        this.y2 = y2;
	        return this;
	    }
	    set(x1 = 0, y1 = 0, x2 = 0, y2 = 0) {
	        if (x2 < x1) {
	            this.x2 = x1;
	            this.x1 = x2;
	        }
	        else {
	            this.x1 = x1;
	            this.x2 = x2;
	        }
	        if (y2 < y1) {
	            this.y2 = y1;
	            this.y1 = y2;
	        }
	        else {
	            this.y1 = y1;
	            this.y2 = y2;
	        }
	        return this;
	    }
	    add(x = 0, y = 0) {
	        if (x < this.x1) {
	            this.x1 = x;
	        }
	        if (y < this.y1) {
	            this.y1 = y;
	        }
	        if (x > this.x2) {
	            this.x2 = x;
	        }
	        if (y > this.y2) {
	            this.y2 = y;
	        }
	        return this;
	    }
	    expand(d = 0) {
	        if (isArray(d)) {
	            this.y1 -= d[0];
	            this.x2 += d[1];
	            this.y2 += d[2];
	            this.x1 -= d[3];
	        }
	        else {
	            this.x1 -= d;
	            this.y1 -= d;
	            this.x2 += d;
	            this.y2 += d;
	        }
	        return this;
	    }
	    round() {
	        this.x1 = Math.floor(this.x1);
	        this.y1 = Math.floor(this.y1);
	        this.x2 = Math.ceil(this.x2);
	        this.y2 = Math.ceil(this.y2);
	        return this;
	    }
	    translate(dx = 0, dy = 0) {
	        this.x1 += dx;
	        this.x2 += dx;
	        this.y1 += dy;
	        this.y2 += dy;
	        return this;
	    }
	    rotate(angle = 0, x = 0, y = 0) {
	        const p = this.rotatedPoints(angle, x, y);
	        return this.clear().add(p[0], p[1]).add(p[2], p[3]).add(p[4], p[5]).add(p[6], p[7]);
	    }
	    scale(sx = 0, sy = 0, x = 0, y = 0) {
	        const p = this.scalePoints(sx, sy, x, y);
	        return this.clear().add(p[0], p[1]).add(p[2], p[3]);
	    }
	    union(b) {
	        if (b.x1 < this.x1) {
	            this.x1 = b.x1;
	        }
	        if (b.y1 < this.y1) {
	            this.y1 = b.y1;
	        }
	        if (b.x2 > this.x2) {
	            this.x2 = b.x2;
	        }
	        if (b.y2 > this.y2) {
	            this.y2 = b.y2;
	        }
	        return this;
	    }
	    intersect(b) {
	        if (b.x1 > this.x1) {
	            this.x1 = b.x1;
	        }
	        if (b.y1 > this.y1) {
	            this.y1 = b.y1;
	        }
	        if (b.x2 < this.x2) {
	            this.x2 = b.x2;
	        }
	        if (b.y2 < this.y2) {
	            this.y2 = b.y2;
	        }
	        return this;
	    }
	    encloses(b) {
	        return b && this.x1 <= b.x1 && this.x2 >= b.x2 && this.y1 <= b.y1 && this.y2 >= b.y2;
	    }
	    alignsWith(b) {
	        return b && (this.x1 === b.x1 || this.x2 === b.x2 || this.y1 === b.y1 || this.y2 === b.y2);
	    }
	    intersects(b) {
	        return b && !(this.x2 < b.x1 || this.x1 > b.x2 || this.y2 < b.y1 || this.y1 > b.y2);
	    }
	    contains(x = 0, y = 0) {
	        return !(x < this.x1 || x > this.x2 || y < this.y1 || y > this.y2);
	    }
	    containsPoint(p) {
	        return !(p.x < this.x1 || p.x > this.x2 || p.y < this.y1 || p.y > this.y2);
	    }
	    width() {
	        if (this.empty()) {
	            return 0;
	        }
	        return this.x2 - this.x1;
	    }
	    height() {
	        if (this.empty()) {
	            return 0;
	        }
	        return this.y2 - this.y1;
	    }
	    scaleX(s = 0) {
	        this.x1 *= s;
	        this.x2 *= s;
	        return this;
	    }
	    scaleY(s = 0) {
	        this.y1 *= s;
	        this.y2 *= s;
	        return this;
	    }
	    transformWithMatrix(matrix) {
	        transformBoundsWithMatrix(this, this, matrix);
	        return this;
	    }
	    copy(b) {
	        this.x1 = b.x1;
	        this.y1 = b.y1;
	        this.x2 = b.x2;
	        this.y2 = b.y2;
	        return this;
	    }
	    rotatedPoints(angle, x, y) {
	        const { x1, y1, x2, y2 } = this;
	        const cos = Math.cos(angle);
	        const sin = Math.sin(angle);
	        const cx = x - x * cos + y * sin;
	        const cy = y - x * sin - y * cos;
	        return [
	            cos * x1 - sin * y1 + cx,
	            sin * x1 + cos * y1 + cy,
	            cos * x1 - sin * y2 + cx,
	            sin * x1 + cos * y2 + cy,
	            cos * x2 - sin * y1 + cx,
	            sin * x2 + cos * y1 + cy,
	            cos * x2 - sin * y2 + cx,
	            sin * x2 + cos * y2 + cy
	        ];
	    }
	    scalePoints(sx, sy, x, y) {
	        const { x1, y1, x2, y2 } = this;
	        return [sx * x1 + (1 - sx) * x, sy * y1 + (1 - sy) * y, sx * x2 + (1 - sx) * x, sy * y2 + (1 - sy) * y];
	    }
	}
	class AABBBounds extends Bounds {
	}
	class OBBBounds extends Bounds {
	    constructor(bounds, angle = 0) {
	        var _a;
	        super(bounds);
	        if (bounds) {
	            this.angle = (_a = bounds.angle) !== null && _a !== void 0 ? _a : angle;
	        }
	    }
	    intersects(b) {
	        return isRotateAABBIntersect(this, b);
	    }
	    setValue(x1 = 0, y1 = 0, x2 = 0, y2 = 0, angle = 0) {
	        super.setValue(x1, y1, x2, y2);
	        this.angle = angle;
	        return this;
	    }
	    clone() {
	        return new OBBBounds(this);
	    }
	    getRotatedCorners() {
	        const cx = (this.x1 + this.x2) / 2;
	        const cy = (this.y1 + this.y2) / 2;
	        const originPoint = { x: cx, y: cy };
	        return [
	            rotatePoint({ x: this.x1, y: this.y1 }, this.angle, originPoint),
	            rotatePoint({ x: this.x2, y: this.y1 }, this.angle, originPoint),
	            rotatePoint({ x: this.x1, y: this.y2 }, this.angle, originPoint),
	            rotatePoint({ x: this.x2, y: this.y2 }, this.angle, originPoint)
	        ];
	    }
	}

	class Matrix {
	    constructor(a = 1, b = 0, c = 0, d = 1, e = 0, f = 0) {
	        this.a = a;
	        this.b = b;
	        this.c = c;
	        this.d = d;
	        this.e = e;
	        this.f = f;
	    }
	    equalToMatrix(m2) {
	        return !(this.e !== m2.e ||
	            this.f !== m2.f ||
	            this.a !== m2.a ||
	            this.d !== m2.d ||
	            this.b !== m2.b ||
	            this.c !== m2.c);
	    }
	    equalTo(a, b, c, d, e, f) {
	        return !(this.e !== e || this.f !== f || this.a !== a || this.d !== d || this.b !== b || this.c !== c);
	    }
	    setValue(a, b, c, d, e, f) {
	        this.a = a;
	        this.b = b;
	        this.c = c;
	        this.d = d;
	        this.e = e;
	        this.f = f;
	        return this;
	    }
	    reset() {
	        this.a = 1;
	        this.b = 0;
	        this.c = 0;
	        this.d = 1;
	        this.e = 0;
	        this.f = 0;
	        return this;
	    }
	    getInverse() {
	        const a = this.a;
	        const b = this.b;
	        const c = this.c;
	        const d = this.d;
	        const e = this.e;
	        const f = this.f;
	        const m = new Matrix();
	        const dt = a * d - b * c;
	        m.a = d / dt;
	        m.b = -b / dt;
	        m.c = -c / dt;
	        m.d = a / dt;
	        m.e = (c * f - d * e) / dt;
	        m.f = -(a * f - b * e) / dt;
	        return m;
	    }
	    rotate(rad) {
	        const c = Math.cos(rad);
	        const s = Math.sin(rad);
	        const m11 = this.a * c + this.c * s;
	        const m12 = this.b * c + this.d * s;
	        const m21 = this.a * -s + this.c * c;
	        const m22 = this.b * -s + this.d * c;
	        this.a = m11;
	        this.b = m12;
	        this.c = m21;
	        this.d = m22;
	        return this;
	    }
	    rotateByCenter(rad, cx, cy) {
	        const cos = Math.cos(rad);
	        const sin = Math.sin(rad);
	        const rotateM13 = (1 - cos) * cx + sin * cy;
	        const rotateM23 = (1 - cos) * cy - sin * cx;
	        const m11 = cos * this.a - sin * this.b;
	        const m21 = sin * this.a + cos * this.b;
	        const m12 = cos * this.c - sin * this.d;
	        const m22 = sin * this.c + cos * this.d;
	        const m13 = cos * this.e - sin * this.f + rotateM13;
	        const m23 = sin * this.e + cos * this.f + rotateM23;
	        this.a = m11;
	        this.b = m21;
	        this.c = m12;
	        this.d = m22;
	        this.e = m13;
	        this.f = m23;
	        return this;
	    }
	    scale(sx, sy) {
	        this.a *= sx;
	        this.b *= sx;
	        this.c *= sy;
	        this.d *= sy;
	        return this;
	    }
	    setScale(sx, sy) {
	        this.b = (this.b / this.a) * sx;
	        this.c = (this.c / this.d) * sy;
	        this.a = sx;
	        this.d = sy;
	        return this;
	    }
	    transform(a, b, c, d, e, f) {
	        this.multiply(a, b, c, d, e, f);
	        return this;
	    }
	    translate(x, y) {
	        this.e += this.a * x + this.c * y;
	        this.f += this.b * x + this.d * y;
	        return this;
	    }
	    transpose() {
	        const { a, b, c, d, e, f } = this;
	        this.a = b;
	        this.b = a;
	        this.c = d;
	        this.d = c;
	        this.e = f;
	        this.f = e;
	        return this;
	    }
	    multiply(a2, b2, c2, d2, e2, f2) {
	        const a1 = this.a;
	        const b1 = this.b;
	        const c1 = this.c;
	        const d1 = this.d;
	        const e1 = this.e;
	        const f1 = this.f;
	        const m11 = a1 * a2 + c1 * b2;
	        const m12 = b1 * a2 + d1 * b2;
	        const m21 = a1 * c2 + c1 * d2;
	        const m22 = b1 * c2 + d1 * d2;
	        const dx = a1 * e2 + c1 * f2 + e1;
	        const dy = b1 * e2 + d1 * f2 + f1;
	        this.a = m11;
	        this.b = m12;
	        this.c = m21;
	        this.d = m22;
	        this.e = dx;
	        this.f = dy;
	        return this;
	    }
	    interpolate(m2, t) {
	        const m = new Matrix();
	        m.a = this.a + (m2.a - this.a) * t;
	        m.b = this.b + (m2.b - this.b) * t;
	        m.c = this.c + (m2.c - this.c) * t;
	        m.d = this.d + (m2.d - this.d) * t;
	        m.e = this.e + (m2.e - this.e) * t;
	        m.f = this.f + (m2.f - this.f) * t;
	        return m;
	    }
	    transformPoint(source, target) {
	        const { a, b, c, d, e, f } = this;
	        const dt = a * d - b * c;
	        const nextA = d / dt;
	        const nextB = -b / dt;
	        const nextC = -c / dt;
	        const nextD = a / dt;
	        const nextE = (c * f - d * e) / dt;
	        const nextF = -(a * f - b * e) / dt;
	        const { x, y } = source;
	        target.x = x * nextA + y * nextC + nextE;
	        target.y = x * nextB + y * nextD + nextF;
	    }
	    onlyTranslate(scale = 1) {
	        return this.a === scale && this.b === 0 && this.c === 0 && this.d === scale;
	    }
	    clone() {
	        return new Matrix(this.a, this.b, this.c, this.d, this.e, this.f);
	    }
	    toTransformAttrs() {
	        const a = this.a;
	        const b = this.b;
	        const c = this.c;
	        const d = this.d;
	        const e = this.e;
	        const f = this.f;
	        const delta = a * d - b * c;
	        const result = {
	            x: e,
	            y: f,
	            rotateDeg: 0,
	            scaleX: 0,
	            scaleY: 0,
	            skewX: 0,
	            skewY: 0
	        };
	        if (a !== 0 || b !== 0) {
	            const r = Math.sqrt(a * a + b * b);
	            result.rotateDeg = b > 0 ? Math.acos(a / r) : -Math.acos(a / r);
	            result.scaleX = r;
	            result.scaleY = delta / r;
	            result.skewX = (a * c + b * d) / delta;
	            result.skewY = 0;
	        }
	        else if (c !== 0 || d !== 0) {
	            const s = Math.sqrt(c * c + d * d);
	            result.rotateDeg = Math.PI / 2 - (d > 0 ? Math.acos(-c / s) : -Math.acos(c / s));
	            result.scaleX = delta / s;
	            result.scaleY = s;
	            result.skewX = 0;
	            result.skewY = (a * c + b * d) / delta;
	        }
	        else ;
	        result.rotateDeg = radianToDegree(result.rotateDeg);
	        return result;
	    }
	}
	function normalTransform(out, origin, x, y, scaleX, scaleY, angle, rotateCenter) {
	    const oa = origin.a;
	    const ob = origin.b;
	    const oc = origin.c;
	    const od = origin.d;
	    const oe = origin.e;
	    const of = origin.f;
	    const cosTheta = cos(angle);
	    const sinTheta = sin(angle);
	    let rotateCenterX;
	    let rotateCenterY;
	    if (rotateCenter) {
	        rotateCenterX = rotateCenter[0];
	        rotateCenterY = rotateCenter[1];
	    }
	    else {
	        rotateCenterX = x;
	        rotateCenterY = y;
	    }
	    const offsetX = rotateCenterX - x;
	    const offsetY = rotateCenterY - y;
	    const a1 = oa * cosTheta + oc * sinTheta;
	    const b1 = ob * cosTheta + od * sinTheta;
	    const c1 = oc * cosTheta - oa * sinTheta;
	    const d1 = od * cosTheta - ob * sinTheta;
	    out.a = scaleX * a1;
	    out.b = scaleX * b1;
	    out.c = scaleY * c1;
	    out.d = scaleY * d1;
	    out.e = oe + oa * rotateCenterX + oc * rotateCenterY - a1 * offsetX - c1 * offsetY;
	    out.f = of + ob * rotateCenterX + od * rotateCenterY - b1 * offsetX - d1 * offsetY;
	}

	class LRU {
	    constructor() {
	        this.CLEAN_THRESHOLD = 1e3;
	        this.L_TIME = 1000;
	        this.R_COUNT = 1;
	        this.R_TIMESTAMP_MAX_SIZE = 20;
	    }
	    clearCache(cache, params) {
	        const { CLEAN_THRESHOLD = this.CLEAN_THRESHOLD, L_TIME = this.L_TIME, R_COUNT = this.R_COUNT } = params;
	        if (cache.size < CLEAN_THRESHOLD) {
	            return 0;
	        }
	        let clearNum = 0;
	        const clear = (key) => {
	            clearNum++;
	            cache.delete(key);
	        };
	        const now = Date.now();
	        cache.forEach((item, key) => {
	            if (item.timestamp.length < R_COUNT) {
	                return clear(key);
	            }
	            let useCount = 0;
	            while (now - item.timestamp[item.timestamp.length - 1 - useCount] < L_TIME) {
	                useCount++;
	                if (useCount >= R_COUNT) {
	                    break;
	                }
	            }
	            if (useCount < R_COUNT) {
	                return clear(key);
	            }
	            while (now - item.timestamp[0] > L_TIME) {
	                item.timestamp.shift();
	            }
	            return;
	        });
	        return clearNum;
	    }
	    addLimitedTimestamp(cacheItem, t, params) {
	        const { R_TIMESTAMP_MAX_SIZE = this.R_TIMESTAMP_MAX_SIZE } = params;
	        if (cacheItem.timestamp.length > R_TIMESTAMP_MAX_SIZE) {
	            cacheItem.timestamp.shift();
	        }
	        cacheItem.timestamp.push(t);
	    }
	    clearTimeStamp(cache, params) {
	        const { L_TIME = this.L_TIME } = params;
	        const now = Date.now();
	        cache.forEach(item => {
	            while (now - item.timestamp[0] > L_TIME) {
	                item.timestamp.shift();
	            }
	        });
	    }
	    clearItemTimestamp(cacheItem, params) {
	        const { L_TIME = this.L_TIME } = params;
	        const now = Date.now();
	        while (now - cacheItem.timestamp[0] > L_TIME) {
	            cacheItem.timestamp.shift();
	        }
	    }
	}

	function hslToRgb(h, s, l) {
	    s /= 100;
	    l /= 100;
	    const c = (1 - Math.abs(2 * l - 1)) * s;
	    const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
	    const m = l - c / 2;
	    let r = 0;
	    let g = 0;
	    let b = 0;
	    if (0 <= h && h < 60) {
	        r = c;
	        g = x;
	        b = 0;
	    }
	    else if (60 <= h && h < 120) {
	        r = x;
	        g = c;
	        b = 0;
	    }
	    else if (120 <= h && h < 180) {
	        r = 0;
	        g = c;
	        b = x;
	    }
	    else if (180 <= h && h < 240) {
	        r = 0;
	        g = x;
	        b = c;
	    }
	    else if (240 <= h && h < 300) {
	        r = x;
	        g = 0;
	        b = c;
	    }
	    else if (300 <= h && h < 360) {
	        r = c;
	        g = 0;
	        b = x;
	    }
	    r = Math.round((r + m) * 255);
	    g = Math.round((g + m) * 255);
	    b = Math.round((b + m) * 255);
	    return { r, g, b };
	}

	function rgbToHsl(r, g, b) {
	    r /= 255;
	    g /= 255;
	    b /= 255;
	    const cMin = Math.min(r, g, b);
	    const cMax = Math.max(r, g, b);
	    const delta = cMax - cMin;
	    let h = 0;
	    let s = 0;
	    let l = 0;
	    if (delta === 0) {
	        h = 0;
	    }
	    else if (cMax === r) {
	        h = ((g - b) / delta) % 6;
	    }
	    else if (cMax === g) {
	        h = (b - r) / delta + 2;
	    }
	    else {
	        h = (r - g) / delta + 4;
	    }
	    h = Math.round(h * 60);
	    if (h < 0) {
	        h += 360;
	    }
	    l = (cMax + cMin) / 2;
	    s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));
	    s = +(s * 100).toFixed(1);
	    l = +(l * 100).toFixed(1);
	    return {
	        h,
	        s,
	        l
	    };
	}

	const REG_HEX = /^#([0-9a-f]{3,8})$/;
	const DEFAULT_COLORS_OPACITY = {
	    transparent: 0xffffff00
	};
	const DEFAULT_COLORS = {
	    aliceblue: 0xf0f8ff,
	    antiquewhite: 0xfaebd7,
	    aqua: 0x00ffff,
	    aquamarine: 0x7fffd4,
	    azure: 0xf0ffff,
	    beige: 0xf5f5dc,
	    bisque: 0xffe4c4,
	    black: 0x000000,
	    blanchedalmond: 0xffebcd,
	    blue: 0x0000ff,
	    blueviolet: 0x8a2be2,
	    brown: 0xa52a2a,
	    burlywood: 0xdeb887,
	    cadetblue: 0x5f9ea0,
	    chartreuse: 0x7fff00,
	    chocolate: 0xd2691e,
	    coral: 0xff7f50,
	    cornflowerblue: 0x6495ed,
	    cornsilk: 0xfff8dc,
	    crimson: 0xdc143c,
	    cyan: 0x00ffff,
	    darkblue: 0x00008b,
	    darkcyan: 0x008b8b,
	    darkgoldenrod: 0xb8860b,
	    darkgray: 0xa9a9a9,
	    darkgreen: 0x006400,
	    darkgrey: 0xa9a9a9,
	    darkkhaki: 0xbdb76b,
	    darkmagenta: 0x8b008b,
	    darkolivegreen: 0x556b2f,
	    darkorange: 0xff8c00,
	    darkorchid: 0x9932cc,
	    darkred: 0x8b0000,
	    darksalmon: 0xe9967a,
	    darkseagreen: 0x8fbc8f,
	    darkslateblue: 0x483d8b,
	    darkslategray: 0x2f4f4f,
	    darkslategrey: 0x2f4f4f,
	    darkturquoise: 0x00ced1,
	    darkviolet: 0x9400d3,
	    deeppink: 0xff1493,
	    deepskyblue: 0x00bfff,
	    dimgray: 0x696969,
	    dimgrey: 0x696969,
	    dodgerblue: 0x1e90ff,
	    firebrick: 0xb22222,
	    floralwhite: 0xfffaf0,
	    forestgreen: 0x228b22,
	    fuchsia: 0xff00ff,
	    gainsboro: 0xdcdcdc,
	    ghostwhite: 0xf8f8ff,
	    gold: 0xffd700,
	    goldenrod: 0xdaa520,
	    gray: 0x808080,
	    green: 0x008000,
	    greenyellow: 0xadff2f,
	    grey: 0x808080,
	    honeydew: 0xf0fff0,
	    hotpink: 0xff69b4,
	    indianred: 0xcd5c5c,
	    indigo: 0x4b0082,
	    ivory: 0xfffff0,
	    khaki: 0xf0e68c,
	    lavender: 0xe6e6fa,
	    lavenderblush: 0xfff0f5,
	    lawngreen: 0x7cfc00,
	    lemonchiffon: 0xfffacd,
	    lightblue: 0xadd8e6,
	    lightcoral: 0xf08080,
	    lightcyan: 0xe0ffff,
	    lightgoldenrodyellow: 0xfafad2,
	    lightgray: 0xd3d3d3,
	    lightgreen: 0x90ee90,
	    lightgrey: 0xd3d3d3,
	    lightpink: 0xffb6c1,
	    lightsalmon: 0xffa07a,
	    lightseagreen: 0x20b2aa,
	    lightskyblue: 0x87cefa,
	    lightslategray: 0x778899,
	    lightslategrey: 0x778899,
	    lightsteelblue: 0xb0c4de,
	    lightyellow: 0xffffe0,
	    lime: 0x00ff00,
	    limegreen: 0x32cd32,
	    linen: 0xfaf0e6,
	    magenta: 0xff00ff,
	    maroon: 0x800000,
	    mediumaquamarine: 0x66cdaa,
	    mediumblue: 0x0000cd,
	    mediumorchid: 0xba55d3,
	    mediumpurple: 0x9370db,
	    mediumseagreen: 0x3cb371,
	    mediumslateblue: 0x7b68ee,
	    mediumspringgreen: 0x00fa9a,
	    mediumturquoise: 0x48d1cc,
	    mediumvioletred: 0xc71585,
	    midnightblue: 0x191970,
	    mintcream: 0xf5fffa,
	    mistyrose: 0xffe4e1,
	    moccasin: 0xffe4b5,
	    navajowhite: 0xffdead,
	    navy: 0x000080,
	    oldlace: 0xfdf5e6,
	    olive: 0x808000,
	    olivedrab: 0x6b8e23,
	    orange: 0xffa500,
	    orangered: 0xff4500,
	    orchid: 0xda70d6,
	    palegoldenrod: 0xeee8aa,
	    palegreen: 0x98fb98,
	    paleturquoise: 0xafeeee,
	    palevioletred: 0xdb7093,
	    papayawhip: 0xffefd5,
	    peachpuff: 0xffdab9,
	    peru: 0xcd853f,
	    pink: 0xffc0cb,
	    plum: 0xdda0dd,
	    powderblue: 0xb0e0e6,
	    purple: 0x800080,
	    rebeccapurple: 0x663399,
	    red: 0xff0000,
	    rosybrown: 0xbc8f8f,
	    royalblue: 0x4169e1,
	    saddlebrown: 0x8b4513,
	    salmon: 0xfa8072,
	    sandybrown: 0xf4a460,
	    seagreen: 0x2e8b57,
	    seashell: 0xfff5ee,
	    sienna: 0xa0522d,
	    silver: 0xc0c0c0,
	    skyblue: 0x87ceeb,
	    slateblue: 0x6a5acd,
	    slategray: 0x708090,
	    slategrey: 0x708090,
	    snow: 0xfffafa,
	    springgreen: 0x00ff7f,
	    steelblue: 0x4682b4,
	    tan: 0xd2b48c,
	    teal: 0x008080,
	    thistle: 0xd8bfd8,
	    tomato: 0xff6347,
	    turquoise: 0x40e0d0,
	    violet: 0xee82ee,
	    wheat: 0xf5deb3,
	    white: 0xffffff,
	    whitesmoke: 0xf5f5f5,
	    yellow: 0xffff00,
	    yellowgreen: 0x9acd32
	};
	function hex(value) {
	    value = Math.max(0, Math.min(255, Math.round(value) || 0));
	    return (value < 16 ? '0' : '') + value.toString(16);
	}
	function rgb(value) {
	    if (isNumber(value)) {
	        return new RGB(value >> 16, (value >> 8) & 0xff, value & 0xff, 1);
	    }
	    else if (isArray(value)) {
	        return new RGB(value[0], value[1], value[2]);
	    }
	    return new RGB(255, 255, 255);
	}
	function rgba(value) {
	    if (isNumber(value)) {
	        return new RGB(value >>> 24, (value >>> 16) & 0xff, (value >>> 8) & 0xff, value & 0xff);
	    }
	    else if (isArray(value)) {
	        return new RGB(value[0], value[1], value[2], value[3]);
	    }
	    return new RGB(255, 255, 255, 1);
	}
	function SRGBToLinear(c) {
	    return c < 0.04045 ? c * 0.0773993808 : Math.pow(c * 0.9478672986 + 0.0521327014, 2.4);
	}
	function LinearToSRGB(c) {
	    return c < 0.0031308 ? c * 12.92 : 1.055 * Math.pow(c, 0.41666) - 0.055;
	}
	const setHex = (formatValue, forceHex) => {
	    const isHex = REG_HEX.exec(formatValue);
	    if (forceHex || isHex) {
	        const hex = parseInt(isHex[1], 16);
	        const hexLength = isHex[1].length;
	        if (hexLength === 3) {
	            return new RGB(((hex >> 8) & 0xf) + (((hex >> 8) & 0xf) << 4), ((hex >> 4) & 0xf) + (((hex >> 4) & 0xf) << 4), (hex & 0xf) + ((hex & 0xf) << 4), 1);
	        }
	        if (hexLength === 6) {
	            return rgb(hex);
	        }
	        else if (hexLength === 8) {
	            return new RGB((hex >> 24) & 0xff, (hex >> 16) & 0xff, (hex >> 8) & 0xff, (hex & 0xff) / 0xff);
	        }
	        return null;
	    }
	    return undefined;
	};
	class Color {
	    static Brighter(source, b = 1) {
	        if (b === 1) {
	            return source;
	        }
	        return new Color(source).brighter(b).toRGBA();
	    }
	    static SetOpacity(source, o = 1) {
	        if (o === 1) {
	            return source;
	        }
	        return new Color(source).setOpacity(o).toRGBA();
	    }
	    static getColorBrightness(source, model = 'hsl') {
	        const color = source instanceof Color ? source : new Color(source);
	        switch (model) {
	            case 'hsv':
	                return color.getHSVBrightness();
	            case 'hsl':
	                return color.getHSLBrightness();
	            case 'lum':
	                return color.getLuminance();
	            case 'lum2':
	                return color.getLuminance2();
	            case 'lum3':
	                return color.getLuminance3();
	            case 'wcag':
	                return color.getLuminanceWCAG();
	            default:
	                return color.getHSVBrightness();
	        }
	    }
	    static parseColorString(value) {
	        if (isValid(DEFAULT_COLORS_OPACITY[value])) {
	            return rgba(DEFAULT_COLORS_OPACITY[value]);
	        }
	        if (isValid(DEFAULT_COLORS[value])) {
	            return rgb(DEFAULT_COLORS[value]);
	        }
	        const formatValue = `${value}`.trim().toLowerCase();
	        const hexRes = setHex(formatValue);
	        if (hexRes !== undefined) {
	            return hexRes;
	        }
	        if (/^(rgb|RGB|rgba|RGBA)/.test(formatValue)) {
	            const aColor = formatValue.replace(/(?:\(|\)|rgba|RGBA|rgb|RGB)*/g, '').split(',');
	            return new RGB(parseInt(aColor[0], 10), parseInt(aColor[1], 10), parseInt(aColor[2], 10), parseFloat(aColor[3]));
	        }
	        if (/^(hsl|HSL|hsla|HSLA)/.test(formatValue)) {
	            const aColor = formatValue.replace(/(?:\(|\)|hsla|HSLA|hsl|HSL)*/g, '').split(',');
	            const rgb = hslToRgb(parseInt(aColor[0], 10), parseInt(aColor[1], 10), parseInt(aColor[2], 10));
	            return new RGB(rgb.r, rgb.g, rgb.b, parseFloat(aColor[3]));
	        }
	        return;
	    }
	    constructor(value) {
	        const color = Color.parseColorString(value);
	        if (color) {
	            this.color = color;
	        }
	        else {
	            console.warn(`Warn: 传入${value}无法解析为Color`);
	            this.color = new RGB(255, 255, 255);
	        }
	    }
	    toRGBA() {
	        return this.color.formatRgb();
	    }
	    toString() {
	        return this.color.formatRgb();
	    }
	    toHex() {
	        return this.color.formatHex();
	    }
	    toHsl() {
	        return this.color.formatHsl();
	    }
	    brighter(k) {
	        const { r, g, b } = this.color;
	        this.color.r = Math.max(0, Math.min(255, Math.floor(r * k)));
	        this.color.g = Math.max(0, Math.min(255, Math.floor(g * k)));
	        this.color.b = Math.max(0, Math.min(255, Math.floor(b * k)));
	        return this;
	    }
	    add(color) {
	        const { r, g, b } = this.color;
	        this.color.r += Math.min(255, r + color.color.r);
	        this.color.g += Math.min(255, g + color.color.g);
	        this.color.b += Math.min(255, b + color.color.b);
	        return this;
	    }
	    sub(color) {
	        this.color.r = Math.max(0, this.color.r - color.color.r);
	        this.color.g = Math.max(0, this.color.g - color.color.g);
	        this.color.b = Math.max(0, this.color.b - color.color.b);
	        return this;
	    }
	    multiply(color) {
	        const { r, g, b } = this.color;
	        this.color.r = Math.max(0, Math.min(255, Math.floor(r * color.color.r)));
	        this.color.g = Math.max(0, Math.min(255, Math.floor(g * color.color.g)));
	        this.color.b = Math.max(0, Math.min(255, Math.floor(b * color.color.b)));
	        return this;
	    }
	    getHSVBrightness() {
	        return Math.max(this.color.r, this.color.g, this.color.b) / 255;
	    }
	    getHSLBrightness() {
	        return ((Math.max(this.color.r, this.color.g, this.color.b) / 255 +
	            Math.min(this.color.r, this.color.g, this.color.b) / 255) *
	            0.5);
	    }
	    setHsl(h, s, l) {
	        const opacity = this.color.opacity;
	        const hsl = rgbToHsl(this.color.r, this.color.g, this.color.b);
	        const rgb = hslToRgb(isNil(h) ? hsl.h : clamp(h, 0, 360), isNil(s) ? hsl.s : s >= 0 && s <= 1 ? s * 100 : s, isNil(l) ? hsl.l : l <= 1 && l >= 0 ? l * 100 : l);
	        this.color = new RGB(rgb.r, rgb.g, rgb.b, opacity);
	        return this;
	    }
	    setRGB(r, g, b) {
	        !isNil(r) && (this.color.r = r);
	        !isNil(g) && (this.color.g = g);
	        !isNil(b) && (this.color.b = b);
	        return this;
	    }
	    setHex(value) {
	        const formatValue = `${value}`.trim().toLowerCase();
	        const res = setHex(formatValue, true);
	        return res !== null && res !== void 0 ? res : this;
	    }
	    setColorName(name) {
	        const hex = DEFAULT_COLORS[name.toLowerCase()];
	        if (typeof hex !== 'undefined') {
	            this.setHex(hex);
	        }
	        else {
	            console.warn('THREE.Color: Unknown color ' + name);
	        }
	        return this;
	    }
	    setScalar(scalar) {
	        this.color.r = scalar;
	        this.color.g = scalar;
	        this.color.b = scalar;
	        return this;
	    }
	    setOpacity(o = 1) {
	        this.color.opacity = o;
	        return this;
	    }
	    getLuminance() {
	        return (0.2126 * this.color.r + 0.7152 * this.color.g + 0.0722 * this.color.b) / 255;
	    }
	    getLuminance2() {
	        return (0.2627 * this.color.r + 0.678 * this.color.g + 0.0593 * this.color.b) / 255;
	    }
	    getLuminance3() {
	        return (0.299 * this.color.r + 0.587 * this.color.g + 0.114 * this.color.b) / 255;
	    }
	    getLuminanceWCAG() {
	        const RsRGB = this.color.r / 255;
	        const GsRGB = this.color.g / 255;
	        const BsRGB = this.color.b / 255;
	        let R;
	        let G;
	        let B;
	        if (RsRGB <= 0.03928) {
	            R = RsRGB / 12.92;
	        }
	        else {
	            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);
	        }
	        if (GsRGB <= 0.03928) {
	            G = GsRGB / 12.92;
	        }
	        else {
	            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);
	        }
	        if (BsRGB <= 0.03928) {
	            B = BsRGB / 12.92;
	        }
	        else {
	            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);
	        }
	        const L = 0.2126 * R + 0.7152 * G + 0.0722 * B;
	        return L;
	    }
	    clone() {
	        return new Color(this.color.toString());
	    }
	    copyGammaToLinear(color, gammaFactor = 2.0) {
	        this.color.r = Math.pow(color.color.r, gammaFactor);
	        this.color.g = Math.pow(color.color.g, gammaFactor);
	        this.color.b = Math.pow(color.color.b, gammaFactor);
	        return this;
	    }
	    copyLinearToGamma(color, gammaFactor = 2.0) {
	        const safeInverse = gammaFactor > 0 ? 1.0 / gammaFactor : 1.0;
	        this.color.r = Math.pow(color.color.r, safeInverse);
	        this.color.g = Math.pow(color.color.g, safeInverse);
	        this.color.b = Math.pow(color.color.b, safeInverse);
	        return this;
	    }
	    convertGammaToLinear(gammaFactor) {
	        this.copyGammaToLinear(this, gammaFactor);
	        return this;
	    }
	    convertLinearToGamma(gammaFactor) {
	        this.copyLinearToGamma(this, gammaFactor);
	        return this;
	    }
	    copySRGBToLinear(color) {
	        this.color.r = SRGBToLinear(color.color.r);
	        this.color.g = SRGBToLinear(color.color.g);
	        this.color.b = SRGBToLinear(color.color.b);
	        return this;
	    }
	    copyLinearToSRGB(color) {
	        this.color.r = LinearToSRGB(color.color.r);
	        this.color.g = LinearToSRGB(color.color.g);
	        this.color.b = LinearToSRGB(color.color.b);
	        return this;
	    }
	    convertSRGBToLinear() {
	        this.copySRGBToLinear(this);
	        return this;
	    }
	    convertLinearToSRGB() {
	        this.copyLinearToSRGB(this);
	        return this;
	    }
	}
	class RGB {
	    constructor(r, g, b, opacity) {
	        this.r = isNaN(+r) ? 255 : Math.max(0, Math.min(255, +r));
	        this.g = isNaN(+g) ? 255 : Math.max(0, Math.min(255, +g));
	        this.b = isNaN(+b) ? 255 : Math.max(0, Math.min(255, +b));
	        if (isValid(opacity)) {
	            this.opacity = isNaN(+opacity) ? 1 : Math.max(0, Math.min(1, +opacity));
	        }
	        else {
	            this.opacity = 1;
	        }
	    }
	    formatHex() {
	        return `#${hex(this.r) + hex(this.g) + hex(this.b) + (this.opacity === 1 ? '' : hex(this.opacity * 255))}`;
	    }
	    formatRgb() {
	        const opacity = this.opacity;
	        return `${opacity === 1 ? 'rgb(' : 'rgba('}${this.r},${this.g},${this.b}${opacity === 1 ? ')' : `,${opacity})`}`;
	    }
	    formatHsl() {
	        const opacity = this.opacity;
	        const { h, s, l } = rgbToHsl(this.r, this.g, this.b);
	        return `${opacity === 1 ? 'hsl(' : 'hsla('}${h},${s}%,${l}%${opacity === 1 ? ')' : `,${opacity})`}`;
	    }
	    toString() {
	        return this.formatHex();
	    }
	}

	function hexToRgb(str) {
	    let r = '';
	    let g = '';
	    let b = '';
	    const strtIndex = str[0] === '#' ? 1 : 0;
	    for (let i = strtIndex; i < str.length; i++) {
	        if (str[i] === '#') {
	            continue;
	        }
	        if (i < strtIndex + 2) {
	            r += str[i];
	        }
	        else if (i < strtIndex + 4) {
	            g += str[i];
	        }
	        else if (i < strtIndex + 6) {
	            b += str[i];
	        }
	    }
	    const ri = parseInt(r, 16);
	    const gi = parseInt(g, 16);
	    const bi = parseInt(b, 16);
	    return [ri, gi, bi];
	}

	function rgbToHex(r, g, b) {
	    return Number((1 << 24) + (r << 16) + (g << 8) + b)
	        .toString(16)
	        .slice(1);
	}

	function interpolateRgb(colorA, colorB) {
	    const redA = colorA.r;
	    const redB = colorB.r;
	    const greenA = colorA.g;
	    const greenB = colorB.g;
	    const blueA = colorA.b;
	    const blueB = colorB.b;
	    const opacityA = colorA.opacity;
	    const opacityB = colorB.opacity;
	    return (t) => {
	        const r = Math.round(redA * (1 - t) + redB * t);
	        const g = Math.round(greenA * (1 - t) + greenB * t);
	        const b = Math.round(blueA * (1 - t) + blueB * t);
	        const opacity = opacityA * (1 - t) + opacityB * t;
	        return new RGB(r, g, b, opacity);
	    };
	}

	var index = /*#__PURE__*/Object.freeze({
		__proto__: null,
		Color: Color,
		DEFAULT_COLORS: DEFAULT_COLORS,
		RGB: RGB,
		hexToRgb: hexToRgb,
		hslToRgb: hslToRgb,
		interpolateRgb: interpolateRgb,
		rgbToHex: rgbToHex,
		rgbToHsl: rgbToHsl
	});

	function normalizePadding(padding) {
	    if (isValidNumber(padding)) {
	        return [padding, padding, padding, padding];
	    }
	    if (isArray(padding)) {
	        const length = padding.length;
	        if (length === 1) {
	            const paddingValue = padding[0];
	            return [paddingValue, paddingValue, paddingValue, paddingValue];
	        }
	        if (length === 2) {
	            const [vertical, horizontal] = padding;
	            return [vertical, horizontal, vertical, horizontal];
	        }
	        if (length === 3) {
	            const [top, horizontal, bottom] = padding;
	            return [top, horizontal, bottom, horizontal];
	        }
	        if (length === 4) {
	            return padding;
	        }
	    }
	    if (isObject(padding)) {
	        const { top = 0, right = 0, bottom = 0, left = 0 } = padding;
	        return [top, right, bottom, left];
	    }
	    return [0, 0, 0, 0];
	}

	function fullYearGetterName(isUTC) {
	    return isUTC ? 'getUTCFullYear' : 'getFullYear';
	}
	function monthGetterName(isUTC) {
	    return isUTC ? 'getUTCMonth' : 'getMonth';
	}
	function dateGetterName(isUTC) {
	    return isUTC ? 'getUTCDate' : 'getDate';
	}
	function hoursGetterName(isUTC) {
	    return isUTC ? 'getUTCHours' : 'getHours';
	}
	function minutesGetterName(isUTC) {
	    return isUTC ? 'getUTCMinutes' : 'getMinutes';
	}
	function secondsGetterName(isUTC) {
	    return isUTC ? 'getUTCSeconds' : 'getSeconds';
	}
	function millisecondsGetterName(isUTC) {
	    return isUTC ? 'getUTCMilliseconds' : 'getMilliseconds';
	}
	function fullYearSetterName(isUTC) {
	    return isUTC ? 'setUTCFullYear' : 'setFullYear';
	}
	function monthSetterName(isUTC) {
	    return isUTC ? 'setUTCMonth' : 'setMonth';
	}
	function dateSetterName(isUTC) {
	    return isUTC ? 'setUTCDate' : 'setDate';
	}
	function hoursSetterName(isUTC) {
	    return isUTC ? 'setUTCHours' : 'setHours';
	}
	function minutesSetterName(isUTC) {
	    return isUTC ? 'setUTCMinutes' : 'setMinutes';
	}
	function secondsSetterName(isUTC) {
	    return isUTC ? 'setUTCSeconds' : 'setSeconds';
	}
	function millisecondsSetterName(isUTC) {
	    return isUTC ? 'setUTCMilliseconds' : 'setMilliseconds';
	}
	function getFormatFromValue(value, isUTC) {
	    const date = toDate(value);
	    const M = date[monthGetterName(isUTC)]() + 1;
	    const d = date[dateGetterName(isUTC)]();
	    const h = date[hoursGetterName(isUTC)]();
	    const m = date[minutesGetterName(isUTC)]();
	    const s = date[secondsGetterName(isUTC)]();
	    const S = date[millisecondsGetterName(isUTC)]();
	    const isSecond = S === 0;
	    const isMinute = isSecond && s === 0;
	    const isHour = isMinute && m === 0;
	    const isDay = isHour && h === 0;
	    const isMonth = isDay && d === 1;
	    const isYear = isMonth && M === 1;
	    if (isYear) {
	        return 'YYYY';
	    }
	    else if (isMonth) {
	        return 'YYYY-MM';
	    }
	    else if (isDay) {
	        return 'YYYY-MM-DD';
	    }
	    else if (isHour) {
	        return 'HH';
	    }
	    else if (isMinute) {
	        return 'HH:mm';
	    }
	    else if (isSecond) {
	        return 'HH:mm:ss';
	    }
	    return 'HH:mm:ss SSS';
	}
	function getTimeFormatter(template, isUTC) {
	    return (time) => {
	        const date = toDate(time);
	        const y = date[fullYearGetterName(isUTC)]();
	        const M = date[monthGetterName(isUTC)]() + 1;
	        const q = Math.floor((M - 1) / 3) + 1;
	        const d = date[dateGetterName(isUTC)]();
	        const e = date[('get' + (isUTC ? 'UTC' : '') + 'Day')]();
	        const H = date[hoursGetterName(isUTC)]();
	        const h = ((H - 1) % 12) + 1;
	        const m = date[minutesGetterName(isUTC)]();
	        const s = date[secondsGetterName(isUTC)]();
	        const S = date[millisecondsGetterName(isUTC)]();
	        return ((template || '')
	            .replace(/YYYY/g, pad(y + '', 4, '0', 'left'))
	            .replace(/yyyy/g, y + '')
	            .replace(/yy/g, (y % 100) + '')
	            .replace(/Q/g, q + '')
	            .replace(/MM/g, pad(M, 2, '0', 'left'))
	            .replace(/M/g, M + '')
	            .replace(/dd/g, pad(d, 2, '0', 'left'))
	            .replace(/d/g, d + '')
	            .replace(/e/g, e + '')
	            .replace(/HH/g, pad(H, 2, '0', 'left'))
	            .replace(/H/g, H + '')
	            .replace(/hh/g, pad(h + '', 2, '0', 'left'))
	            .replace(/h/g, h + '')
	            .replace(/mm/g, pad(m, 2, '0', 'left'))
	            .replace(/m/g, m + '')
	            .replace(/ss/g, pad(s, 2, '0', 'left'))
	            .replace(/s/g, s + '')
	            .replace(/SSS/g, pad(S, 3, '0', 'left'))
	            .replace(/S/g, S + ''));
	    };
	}

	const SECOND = 1000;
	const MINUTE = 60 * SECOND;
	const HOUR = 60 * MINUTE;
	const DAY = 24 * HOUR;
	const MONTH = DAY * 31;
	const YEAR = DAY * 365;
	const yearFloor = (date) => {
	    date.setMonth(0, 1);
	    date.setHours(0, 0, 0, 0);
	    return date;
	};
	const yearOffset = (date, step) => {
	    date.setFullYear(date.getFullYear() + step);
	    return date;
	};
	const yearCount = (start, end) => {
	    return end.getFullYear() - start.getFullYear();
	};
	const yearField = (date) => date.getFullYear();
	const utcYearFloor = (date) => {
	    date.setUTCMonth(0, 1);
	    date.setUTCHours(0, 0, 0, 0);
	    return date;
	};
	const utcYearOffset = (date, step) => {
	    date.setUTCFullYear(date.getUTCFullYear() + step);
	    return date;
	};
	const utcYearCount = (start, end) => {
	    return end.getUTCFullYear() - start.getUTCFullYear();
	};
	const utcYearField = (date) => date.getUTCFullYear();
	const monthFloor = (date) => {
	    date.setDate(1);
	    date.setHours(0, 0, 0, 0);
	    return date;
	};
	const monthOffset = (date, step) => {
	    date.setMonth(date.getMonth() + step);
	    return date;
	};
	const monthCount = (start, end) => {
	    return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;
	};
	const monthField = (date) => date.getMonth();
	const utcMonthFloor = (date) => {
	    date.setUTCDate(1);
	    date.setUTCHours(0, 0, 0, 0);
	    return date;
	};
	const utcMonthOffset = (date, step) => {
	    date.setUTCMonth(date.getUTCMonth() + step);
	    return date;
	};
	const utcMonthCount = (start, end) => {
	    return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;
	};
	const utcMonthField = (date) => date.getUTCMonth();
	const dayFloor = (date) => {
	    date.setHours(0, 0, 0, 0);
	    return date;
	};
	const dayOffset = (date, step) => {
	    date.setDate(date.getDate() + step);
	    return date;
	};
	const dayCount = (start, end) => {
	    return (+end - +start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * MINUTE) / DAY;
	};
	const dayField = (date) => date.getDate() - 1;
	const utcDayFloor = (date) => {
	    date.setUTCHours(0, 0, 0, 0);
	    return date;
	};
	const utcDayOffset = (date, step) => {
	    date.setUTCDate(date.getUTCDate() + step);
	    return date;
	};
	const utcDayCount = (start, end) => {
	    return (+end - +start) / DAY;
	};
	const utcDayField = (date) => date.getUTCDate() - 1;
	const hourFloor = (date) => {
	    date.setTime(+date - date.getMilliseconds() - date.getSeconds() * SECOND - date.getMinutes() * MINUTE);
	    return date;
	};
	const hourOffset = (date, step) => {
	    date.setHours(date.getHours() + step);
	    return date;
	};
	const hourCount = (start, end) => {
	    return (+end - +start) / HOUR;
	};
	const hourField = (date) => date.getHours();
	const utcHourFloor = (date) => {
	    date.setTime(+date - date.getUTCMilliseconds() - date.getUTCSeconds() * SECOND - date.getUTCMinutes() * MINUTE);
	    return date;
	};
	const utcHourOffset = (date, step) => {
	    date.setUTCHours(date.getUTCHours() + step);
	    return date;
	};
	const utcHourField = (date) => date.getUTCHours();
	const minuteFloor = (date) => {
	    date.setTime(+date - date.getMilliseconds() - date.getSeconds() * SECOND);
	    return date;
	};
	const minuteOffset = (date, step) => {
	    date.setMinutes(date.getMinutes() + step);
	    return date;
	};
	const minuteCount = (start, end) => {
	    return (+end - +start) / MINUTE;
	};
	const minuteField = (date) => {
	    return date.getMinutes();
	};
	const utcMinuteFloor = (date) => {
	    date.setTime(+date - date.getUTCMilliseconds() - date.getUTCSeconds() * SECOND);
	    return date;
	};
	const utcMinuteOffset = (date, step) => {
	    date.setUTCMinutes(date.getUTCMinutes() + step);
	    return date;
	};
	const utcMinuteField = (date) => {
	    return date.getUTCMinutes();
	};
	const secondFloor = (date) => {
	    date.setTime(+date - date.getMilliseconds());
	    return date;
	};
	const secondOffset = (date, step) => {
	    date.setSeconds(date.getSeconds() + step);
	    return date;
	};
	const secondCount = (start, end) => {
	    return (+end - +start) / SECOND;
	};
	const secondField = (date) => date.getSeconds();
	const utcSecondFloor = (date) => {
	    date.setTime(+date - date.getUTCMilliseconds());
	    return date;
	};
	const utcSecondOffset = (date, step) => {
	    date.setUTCSeconds(date.getUTCSeconds() + step);
	    return date;
	};
	const utcSecondField = (date) => date.getUTCSeconds();
	const millisecondsFloor = (date) => {
	    return date;
	};
	const millisecondsOffset = (date, step) => {
	    date.setTime(+date + step);
	    return date;
	};
	const millisecondsCount = (start, end) => +end - +start;
	const generateCeil = (floor, offset) => {
	    return (date) => {
	        const n = new Date(+date - 1);
	        offset(n, 1);
	        floor(n);
	        return n;
	    };
	};
	const generateCount = (floor, count) => {
	    return (start, end) => {
	        const a = new Date();
	        const b = new Date();
	        a.setTime(+start);
	        b.setTime(+end);
	        floor(a);
	        floor(b);
	        return Math.floor(count(a, b));
	    };
	};
	const generateStepInterval = (step, { floor, offset, field, count }) => {
	    const s = Math.floor(step);
	    if (!Number.isFinite(s) || s <= 0) {
	        return null;
	    }
	    if (s <= 1) {
	        return {
	            floor,
	            offset,
	            ceil: generateCeil(floor, offset)
	        };
	    }
	    const stepCount = generateCount(floor, count);
	    const testFunc = field
	        ? (d) => {
	            return field(d) % s === 0;
	        }
	        : (d) => {
	            return stepCount(0, d) % s === 0;
	        };
	    const stepFloor = (date) => {
	        if (!Number.isNaN(+date)) {
	            floor(date);
	            while (!testFunc(date)) {
	                date.setTime(+date - 1);
	                floor(date);
	            }
	        }
	        return date;
	    };
	    const stepOffset = (date, stepCount) => {
	        if (!Number.isNaN(+date)) {
	            if (s < 0) {
	                while (++stepCount <= 0) {
	                    offset(date, -1);
	                    while (!testFunc(date)) {
	                        offset(date, -1);
	                    }
	                }
	            }
	            else {
	                while (--stepCount >= 0) {
	                    offset(date, +1);
	                    while (!testFunc(date)) {
	                        offset(date, +1);
	                    }
	                }
	            }
	        }
	        return date;
	    };
	    return {
	        floor: stepFloor,
	        offset: stepOffset,
	        ceil: generateCeil(stepFloor, stepOffset)
	    };
	};
	const getIntervalOptions = (type, isUTC) => {
	    if (type === 'year' && isUTC) {
	        return { floor: utcYearFloor, offset: utcYearOffset, count: utcYearCount, field: utcYearField };
	    }
	    if (type === 'month' && isUTC) {
	        return { floor: utcMonthFloor, offset: utcMonthOffset, count: utcMonthCount, field: utcMonthField };
	    }
	    if (type === 'day' && isUTC) {
	        return { floor: utcDayFloor, offset: utcDayOffset, count: utcDayCount, field: utcDayField };
	    }
	    if (type === 'hour' && isUTC) {
	        return { floor: utcHourFloor, offset: utcHourOffset, count: hourCount, field: utcHourField };
	    }
	    if (type === 'minute' && isUTC) {
	        return { floor: utcMinuteFloor, offset: utcMinuteOffset, count: minuteCount, field: utcMinuteField };
	    }
	    if (type === 'second' && isUTC) {
	        return { floor: utcSecondFloor, offset: utcSecondOffset, count: secondCount, field: utcSecondField };
	    }
	    if (type === 'year') {
	        return { floor: yearFloor, offset: yearOffset, count: yearCount, field: yearField };
	    }
	    if (type === 'month') {
	        return { floor: monthFloor, offset: monthOffset, count: monthCount, field: monthField };
	    }
	    if (type === 'day') {
	        return { floor: dayFloor, offset: dayOffset, count: dayCount, field: dayField };
	    }
	    if (type === 'hour') {
	        return { floor: hourFloor, offset: hourOffset, count: hourCount, field: hourField };
	    }
	    if (type === 'minute') {
	        return { floor: minuteFloor, offset: minuteOffset, count: minuteCount, field: minuteField };
	    }
	    if (type === 'second') {
	        return { floor: secondFloor, offset: secondOffset, count: secondCount, field: secondField };
	    }
	    return { floor: millisecondsFloor, offset: millisecondsOffset, count: millisecondsCount };
	};

	function getContainerSize(el, defaultWidth = 0, defaultHeight = 0) {
	    if (!el) {
	        return { width: defaultWidth, height: defaultHeight };
	    }
	    let getComputedStyle;
	    try {
	        getComputedStyle = window === null || window === void 0 ? void 0 : window.getComputedStyle;
	    }
	    catch (e) {
	        getComputedStyle = () => {
	            return {};
	        };
	    }
	    const style = getComputedStyle(el);
	    if (/^(\d*\.?\d+)(px)$/.exec(style.width)) {
	        const computedWidth = parseFloat(style.width) - parseFloat(style.paddingLeft) - parseFloat(style.paddingRight) || el.clientWidth - 1;
	        const computedHeight = parseFloat(style.height) - parseFloat(style.paddingTop) - parseFloat(style.paddingBottom) || el.clientHeight - 1;
	        return {
	            width: computedWidth <= 0 ? defaultWidth : computedWidth,
	            height: computedHeight <= 0 ? defaultHeight : computedHeight
	        };
	    }
	    return { width: defaultWidth, height: defaultHeight };
	}
	function getElementAbsolutePosition(element) {
	    const { x, y } = element.getBoundingClientRect();
	    return { x, y };
	}
	function getElementRelativePosition(element, base) {
	    const posElement = getElementAbsolutePosition(element);
	    const posBase = getElementAbsolutePosition(base);
	    return { x: posElement.x - posBase.x, y: posElement.y - posBase.y };
	}
	const getScrollLeft = (element) => {
	    var _a, _b, _c;
	    if (element === ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) === null || _a === void 0 ? void 0 : _a.body)) {
	        return ((_c = (_b = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) === null || _b === void 0 ? void 0 : _b.documentElement) === null || _c === void 0 ? void 0 : _c.scrollLeft) || element.scrollLeft;
	    }
	    else if (element.tagName.toLowerCase() === 'html') {
	        return 0;
	    }
	    return element.scrollLeft;
	};
	const getScrollTop = (element) => {
	    var _a, _b, _c;
	    if (element === ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) === null || _a === void 0 ? void 0 : _a.body)) {
	        return ((_c = (_b = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) === null || _b === void 0 ? void 0 : _b.documentElement) === null || _c === void 0 ? void 0 : _c.scrollTop) || element.scrollTop;
	    }
	    else if (element.tagName.toLowerCase() === 'html') {
	        return 0;
	    }
	    return element.scrollTop;
	};
	const getScaleX = (element) => {
	    return element.getBoundingClientRect().width / element.offsetWidth;
	};
	const getScaleY = (element) => {
	    return element.getBoundingClientRect().height / element.offsetHeight;
	};
	const getScale = (element) => {
	    if (element.offsetWidth > 0) {
	        return getScaleX(element);
	    }
	    return getScaleY(element);
	};
	function hasParentElement(element, target) {
	    let parent = element.parentNode;
	    while (parent !== null) {
	        if (parent === target) {
	            return true;
	        }
	        parent = parent.parentNode;
	    }
	    return false;
	}
	const styleStringToObject = (styleStr = '') => {
	    const res = {};
	    styleStr.split(';').forEach(item => {
	        if (item) {
	            const arr = item.split(':');
	            if (arr.length === 2) {
	                const key = arr[0].trim();
	                const value = arr[1].trim();
	                if (key && value) {
	                    res[key] = value;
	                }
	            }
	        }
	    });
	    return res;
	};
	const lowerCamelCaseToMiddle = (str) => {
	    return str.replace(/([A-Z])/g, '-$1').toLowerCase();
	};
	function toCamelCase(str) {
	    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
	}
	function isHTMLElement(obj) {
	    try {
	        return obj instanceof Element;
	    }
	    catch (_a) {
	        const htmlElementKeys = [
	            'children',
	            'innerHTML',
	            'classList',
	            'setAttribute',
	            'tagName',
	            'getBoundingClientRect'
	        ];
	        const keys = Object.keys(obj);
	        return htmlElementKeys.every(key => keys.includes(key));
	    }
	}

	/**
	 * @module helpers
	 */
	/**
	 * Earth Radius used with the Harvesine formula and approximates using a spherical (non-ellipsoid) Earth.
	 *
	 * @memberof helpers
	 * @type {number}
	 */
	var earthRadius = 6371008.8;
	/**
	 * Unit of measurement factors using a spherical (non-ellipsoid) earth radius.
	 *
	 * @memberof helpers
	 * @type {Object}
	 */
	var factors = {
	  centimeters: earthRadius * 100,
	  centimetres: earthRadius * 100,
	  degrees: earthRadius / 111325,
	  feet: earthRadius * 3.28084,
	  inches: earthRadius * 39.37,
	  kilometers: earthRadius / 1000,
	  kilometres: earthRadius / 1000,
	  meters: earthRadius,
	  metres: earthRadius,
	  miles: earthRadius / 1609.344,
	  millimeters: earthRadius * 1000,
	  millimetres: earthRadius * 1000,
	  nauticalmiles: earthRadius / 1852,
	  radians: 1,
	  yards: earthRadius * 1.0936
	};
	/**
	 * Convert a distance measurement (assuming a spherical Earth) from a real-world unit into radians
	 * Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet
	 *
	 * @name lengthToRadians
	 * @param {number} distance in real units
	 * @param {string} [units="kilometers"] can be degrees, radians, miles, inches, yards, metres,
	 * meters, kilometres, kilometers.
	 * @returns {number} radians
	 */
	function lengthToRadians(distance, units) {
	  if (units === void 0) {
	    units = "kilometers";
	  }
	  var factor = factors[units];
	  if (!factor) {
	    throw new Error(units + " units is invalid");
	  }
	  return distance / factor;
	}

	function getGeom(geojson) {
	    if (geojson.type === 'Feature') {
	        return geojson.geometry;
	    }
	    return geojson;
	}
	function isPointInPolygon(point, polygon) {
	    if (!point) {
	        return false;
	    }
	    if (!polygon) {
	        return false;
	    }
	    const geom = getGeom(polygon);
	    const type = geom.type;
	    const bbox = polygon.bbox;
	    let polys = geom.coordinates;
	    if (bbox && pointInRect(point, { x1: bbox[0], x2: bbox[1], y1: bbox[1], y2: bbox[3] }, true) === true) {
	        return false;
	    }
	    if (type === 'Polygon') {
	        polys = [polys];
	    }
	    let result = false;
	    for (let i = 0; i < polys.length; ++i) {
	        for (let j = 0; j < polys[i].length; ++j) {
	            const polyResult = polygonContainPoint(polys[i][j].map((p) => ({ x: p[0], y: p[1] })), point.x, point.y);
	            if (polyResult) {
	                result = true;
	                return result;
	            }
	        }
	    }
	    return result;
	}
	function destination(point, distance, bearing, options = {}) {
	    const longitude1 = degreeToRadian(point[0]);
	    const latitude1 = degreeToRadian(point[1]);
	    const bearingRad = degreeToRadian(bearing);
	    const radians = lengthToRadians(distance, options.units);
	    const latitude2 = Math.asin(Math.sin(latitude1) * Math.cos(radians) + Math.cos(latitude1) * Math.sin(radians) * Math.cos(bearingRad));
	    const longitude2 = longitude1 +
	        Math.atan2(Math.sin(bearingRad) * Math.sin(radians) * Math.cos(latitude1), Math.cos(radians) - Math.sin(latitude1) * Math.sin(latitude2));
	    const lng = radianToDegree(longitude2);
	    const lat = radianToDegree(latitude2);
	    return { x: lng, y: lat };
	}

	const SMALL = 1e-10;

	function intersectionArea(circles, stats) {
	    const intersectionPoints = getIntersectionPoints(circles);
	    const innerPoints = intersectionPoints.filter(function (p) {
	        return containedInCircles(p, circles);
	    });
	    let arcArea = 0;
	    let polygonArea = 0;
	    const arcs = [];
	    if (innerPoints.length > 1) {
	        const center = getCenter(innerPoints);
	        for (let i = 0; i < innerPoints.length; ++i) {
	            const p = innerPoints[i];
	            p.angle = Math.atan2(p.x - center.x, p.y - center.y);
	        }
	        innerPoints.sort(function (a, b) {
	            return b.angle - a.angle;
	        });
	        let p2 = innerPoints[innerPoints.length - 1];
	        for (let i = 0; i < innerPoints.length; ++i) {
	            const p1 = innerPoints[i];
	            polygonArea += (p2.x + p1.x) * (p1.y - p2.y);
	            const midPoint = { x: (p1.x + p2.x) / 2, y: (p1.y + p2.y) / 2 };
	            let arc = null;
	            for (let j = 0; j < p1.parentIndex.length; ++j) {
	                if (p2.parentIndex.indexOf(p1.parentIndex[j]) > -1) {
	                    const circle = circles[p1.parentIndex[j]];
	                    const a1 = Math.atan2(p1.x - circle.x, p1.y - circle.y);
	                    const a2 = Math.atan2(p2.x - circle.x, p2.y - circle.y);
	                    let angleDiff = a2 - a1;
	                    if (angleDiff < 0) {
	                        angleDiff += 2 * Math.PI;
	                    }
	                    const a = a2 - angleDiff / 2;
	                    let width = PointService.distancePP(midPoint, {
	                        x: circle.x + circle.radius * Math.sin(a),
	                        y: circle.y + circle.radius * Math.cos(a)
	                    });
	                    if (width > circle.radius * 2) {
	                        width = circle.radius * 2;
	                    }
	                    if (arc === null || arc.width > width) {
	                        arc = { circle: circle, width: width, p1: p1, p2: p2 };
	                    }
	                }
	            }
	            if (arc !== null) {
	                arcs.push(arc);
	                arcArea += circleArea(arc.circle.radius, arc.width);
	                p2 = p1;
	            }
	        }
	    }
	    else {
	        let smallest = circles[0];
	        for (let i = 1; i < circles.length; ++i) {
	            if (circles[i].radius < smallest.radius) {
	                smallest = circles[i];
	            }
	        }
	        let disjoint = false;
	        for (let i = 0; i < circles.length; ++i) {
	            if (PointService.distancePP(circles[i], smallest) > Math.abs(smallest.radius - circles[i].radius)) {
	                disjoint = true;
	                break;
	            }
	        }
	        if (disjoint) {
	            arcArea = polygonArea = 0;
	        }
	        else {
	            arcArea = smallest.radius * smallest.radius * Math.PI;
	            arcs.push({
	                circle: smallest,
	                p1: { x: smallest.x, y: smallest.y + smallest.radius },
	                p2: { x: smallest.x - SMALL, y: smallest.y + smallest.radius },
	                width: smallest.radius * 2
	            });
	        }
	    }
	    polygonArea /= 2;
	    if (stats) {
	        stats.area = arcArea + polygonArea;
	        stats.arcArea = arcArea;
	        stats.polygonArea = polygonArea;
	        stats.arcs = arcs;
	        stats.innerPoints = innerPoints;
	        stats.intersectionPoints = intersectionPoints;
	    }
	    return arcArea + polygonArea;
	}
	function containedInCircles(point, circles) {
	    for (let i = 0; i < circles.length; ++i) {
	        if (PointService.distancePP(point, circles[i]) > circles[i].radius + SMALL) {
	            return false;
	        }
	    }
	    return true;
	}
	function getIntersectionPoints(circles) {
	    const ret = [];
	    for (let i = 0; i < circles.length; ++i) {
	        for (let j = i + 1; j < circles.length; ++j) {
	            const intersect = circleCircleIntersection(circles[i], circles[j]);
	            for (let k = 0; k < intersect.length; ++k) {
	                const p = intersect[k];
	                p.parentIndex = [i, j];
	                ret.push(p);
	            }
	        }
	    }
	    return ret;
	}
	function circleArea(r, width) {
	    return r * r * Math.acos(1 - width / r) - (r - width) * Math.sqrt(width * (2 * r - width));
	}
	function circleOverlap(r1, r2, d) {
	    if (d >= r1 + r2) {
	        return 0;
	    }
	    if (d <= Math.abs(r1 - r2)) {
	        return Math.PI * Math.min(r1, r2) * Math.min(r1, r2);
	    }
	    const w1 = r1 - (d * d - r2 * r2 + r1 * r1) / (2 * d);
	    const w2 = r2 - (d * d - r1 * r1 + r2 * r2) / (2 * d);
	    return circleArea(r1, w1) + circleArea(r2, w2);
	}
	function circleCircleIntersection(p1, p2) {
	    const d = PointService.distancePP(p1, p2);
	    const r1 = p1.radius;
	    const r2 = p2.radius;
	    if (d >= r1 + r2 || d <= Math.abs(r1 - r2)) {
	        return [];
	    }
	    const a = (r1 * r1 - r2 * r2 + d * d) / (2 * d);
	    const h = Math.sqrt(r1 * r1 - a * a);
	    const x0 = p1.x + (a * (p2.x - p1.x)) / d;
	    const y0 = p1.y + (a * (p2.y - p1.y)) / d;
	    const rx = -(p2.y - p1.y) * (h / d);
	    const ry = -(p2.x - p1.x) * (h / d);
	    return [
	        { x: x0 + rx, y: y0 - ry },
	        { x: x0 - rx, y: y0 + ry }
	    ];
	}
	function getCenter(points) {
	    const center = { x: 0, y: 0 };
	    for (let i = 0; i < points.length; ++i) {
	        center.x += points[i].x;
	        center.y += points[i].y;
	    }
	    center.x /= points.length;
	    center.y /= points.length;
	    return center;
	}

	class TimeUtil {
	    static getInstance() {
	        if (!TimeUtil.instance) {
	            TimeUtil.instance = new TimeUtil();
	        }
	        return TimeUtil.instance;
	    }
	    constructor() {
	        this.locale_shortWeekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
	        this.locale_periods = ['AM', 'PM'];
	        this.locale_weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
	        this.locale_shortMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
	        this.numberRe = /^\s*\d+/;
	        this.pads = { '-': '', _: ' ', '0': '0' };
	        this.requoteRe = /[\\^$*+?|[\]().{}]/g;
	        this.locale_months = [
	            'January',
	            'February',
	            'March',
	            'April',
	            'May',
	            'June',
	            'July',
	            'August',
	            'September',
	            'October',
	            'November',
	            'December'
	        ];
	        this.formatShortWeekday = (d) => {
	            return this.locale_shortWeekdays[d.getDay()];
	        };
	        this.formatWeekday = (d) => {
	            return this.locale_weekdays[d.getDay()];
	        };
	        this.formatShortMonth = (d) => {
	            return this.locale_shortMonths[d.getMonth()];
	        };
	        this.formatMonth = (d) => {
	            return this.locale_months[d.getMonth()];
	        };
	        this.formatDayOfMonth = (d, p) => {
	            return this.pad(d.getDate(), p, 2);
	        };
	        this.formatHour24 = (d, p) => {
	            return this.pad(d.getHours(), p, 2);
	        };
	        this.formatHour12 = (d, p) => {
	            return this.pad(d.getHours() % 12 || 12, p, 2);
	        };
	        this.formatMilliseconds = (d, p) => {
	            return this.pad(d.getMilliseconds(), p, 3);
	        };
	        this.formatMonthNumber = (d, p) => {
	            return this.pad(d.getMonth() + 1, p, 2);
	        };
	        this.formatMinutes = (d, p) => {
	            return this.pad(d.getMinutes(), p, 2);
	        };
	        this.formatPeriod = (d) => {
	            return this.locale_periods[+(d.getHours() >= 12)];
	        };
	        this.formatSeconds = (d, p) => {
	            return this.pad(d.getSeconds(), p, 2);
	        };
	        this.formatFullYear = (d, p) => {
	            return this.pad(d.getFullYear() % 10000, p, 4);
	        };
	        this.formatUTCShortWeekday = (d) => {
	            return this.locale_shortWeekdays[d.getUTCDay()];
	        };
	        this.formatUTCWeekday = (d) => {
	            return this.locale_weekdays[d.getUTCDay()];
	        };
	        this.formatUTCShortMonth = (d) => {
	            return this.locale_shortMonths[d.getUTCMonth()];
	        };
	        this.formatUTCMonth = (d) => {
	            return this.locale_months[d.getUTCMonth()];
	        };
	        this.formatUTCDayOfMonth = (d, p) => {
	            return this.pad(d.getUTCDate(), p, 2);
	        };
	        this.formatUTCHour24 = (d, p) => {
	            return this.pad(d.getUTCHours(), p, 2);
	        };
	        this.formatUTCHour12 = (d, p) => {
	            return this.pad(d.getUTCHours() % 12 || 12, p, 2);
	        };
	        this.formatUTCMilliseconds = (d, p) => {
	            return this.pad(d.getUTCMilliseconds(), p, 3);
	        };
	        this.formatUTCMonthNumber = (d, p) => {
	            return this.pad(d.getUTCMonth() + 1, p, 2);
	        };
	        this.formatUTCMinutes = (d, p) => {
	            return this.pad(d.getUTCMinutes(), p, 2);
	        };
	        this.formatUTCPeriod = (d) => {
	            return this.locale_periods[+(d.getUTCHours() >= 12)];
	        };
	        this.formatUTCSeconds = (d, p) => {
	            return this.pad(d.getUTCSeconds(), p, 2);
	        };
	        this.formatUTCFullYear = (d, p) => {
	            return this.pad(d.getUTCFullYear() % 10000, p, 4);
	        };
	        this.formats = {
	            a: this.formatShortWeekday,
	            A: this.formatWeekday,
	            b: this.formatShortMonth,
	            B: this.formatMonth,
	            d: this.formatDayOfMonth,
	            e: this.formatDayOfMonth,
	            H: this.formatHour24,
	            I: this.formatHour12,
	            L: this.formatMilliseconds,
	            m: this.formatMonthNumber,
	            M: this.formatMinutes,
	            p: this.formatPeriod,
	            S: this.formatSeconds,
	            Y: this.formatFullYear
	        };
	        this.utcFormats = {
	            a: this.formatUTCShortWeekday,
	            A: this.formatUTCWeekday,
	            b: this.formatUTCShortMonth,
	            B: this.formatUTCMonth,
	            d: this.formatUTCDayOfMonth,
	            e: this.formatUTCDayOfMonth,
	            H: this.formatUTCHour24,
	            I: this.formatUTCHour12,
	            L: this.formatUTCMilliseconds,
	            m: this.formatUTCMonthNumber,
	            M: this.formatUTCMinutes,
	            p: this.formatUTCPeriod,
	            S: this.formatUTCSeconds,
	            Y: this.formatUTCFullYear
	        };
	        this.parseShortWeekday = (d, string, i) => {
	            const n = this.shortWeekdayRe.exec(string.slice(i));
	            return n ? ((d.w = this.shortWeekdayLookup.get(n[0].toLowerCase())), i + n[0].length) : -1;
	        };
	        this.parseWeekday = (d, string, i) => {
	            const n = this.weekdayRe.exec(string.slice(i));
	            return n ? ((d.w = this.weekdayLookup.get(n[0].toLowerCase())), i + n[0].length) : -1;
	        };
	        this.parseShortMonth = (d, string, i) => {
	            const n = this.shortMonthRe.exec(string.slice(i));
	            return n ? ((d.m = this.shortMonthLookup.get(n[0].toLowerCase())), i + n[0].length) : -1;
	        };
	        this.parseMonth = (d, string, i) => {
	            const n = this.monthRe.exec(string.slice(i));
	            return n ? ((d.m = this.monthLookup.get(n[0].toLowerCase())), i + n[0].length) : -1;
	        };
	        this.parseDayOfMonth = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 2));
	            return n ? ((d.d = +n[0]), i + n[0].length) : -1;
	        };
	        this.parseHour24 = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 2));
	            return n ? ((d.H = +n[0]), i + n[0].length) : -1;
	        };
	        this.parseMilliseconds = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 3));
	            return n ? ((d.L = +n[0]), i + n[0].length) : -1;
	        };
	        this.parseMonthNumber = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 2));
	            return n ? ((d.m = n - 1), i + n[0].length) : -1;
	        };
	        this.parseMinutes = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 2));
	            return n ? ((d.M = +n[0]), i + n[0].length) : -1;
	        };
	        this.parsePeriod = (d, string, i) => {
	            const n = this.periodRe.exec(string.slice(i));
	            return n ? ((d.p = this.periodLookup.get(n[0].toLowerCase())), i + n[0].length) : -1;
	        };
	        this.parseSeconds = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 2));
	            return n ? ((d.S = +n[0]), i + n[0].length) : -1;
	        };
	        this.parseFullYear = (d, string, i) => {
	            const n = this.numberRe.exec(string.slice(i, i + 4));
	            return n ? ((d.y = +n[0]), i + n[0].length) : -1;
	        };
	        this.parses = {
	            a: this.parseShortWeekday,
	            A: this.parseWeekday,
	            b: this.parseShortMonth,
	            B: this.parseMonth,
	            d: this.parseDayOfMonth,
	            e: this.parseDayOfMonth,
	            H: this.parseHour24,
	            I: this.parseHour24,
	            L: this.parseMilliseconds,
	            m: this.parseMonthNumber,
	            M: this.parseMinutes,
	            p: this.parsePeriod,
	            S: this.parseSeconds,
	            Y: this.parseFullYear
	        };
	        this.timeFormat = (specifier, timeText) => {
	            return this.newFormat(specifier, this.formats)(new Date(this.getFullTimeStamp(timeText)));
	        };
	        this.timeUTCFormat = (specifier, timeText) => {
	            return this.newFormat(specifier, this.utcFormats)(new Date(this.getFullTimeStamp(timeText)));
	        };
	        this.timeParse = (specifier, timeText) => {
	            return this.newParse(specifier, false)(timeText + '');
	        };
	        this.requoteF = this.requote.bind(this);
	        this.periodRe = this.formatRe(this.locale_periods);
	        this.periodLookup = this.formatLookup(this.locale_periods);
	        this.weekdayRe = this.formatRe(this.locale_weekdays);
	        this.weekdayLookup = this.formatLookup(this.locale_weekdays);
	        this.shortWeekdayRe = this.formatRe(this.locale_shortWeekdays);
	        this.shortWeekdayLookup = this.formatLookup(this.locale_shortWeekdays);
	        this.monthRe = this.formatRe(this.locale_months);
	        this.monthLookup = this.formatLookup(this.locale_months);
	        this.shortMonthRe = this.formatRe(this.locale_shortMonths);
	        this.shortMonthLookup = this.formatLookup(this.locale_shortMonths);
	    }
	    requote(s) {
	        return s.replace(this.requoteRe, '\\$&');
	    }
	    localDate(d) {
	        if (0 <= d.y && d.y < 100) {
	            const date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);
	            date.setFullYear(d.y);
	            return date;
	        }
	        return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);
	    }
	    utcDate(d) {
	        if (0 <= d.y && d.y < 100) {
	            const date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));
	            date.setUTCFullYear(d.y);
	            return date;
	        }
	        return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));
	    }
	    newDate(y, m, d) {
	        return { y: y, m: m, d: d, H: 0, M: 0, S: 0, L: 0 };
	    }
	    formatRe(names) {
	        return new RegExp('^(?:' + names.map(this.requoteF).join('|') + ')', 'i');
	    }
	    formatLookup(names) {
	        return new Map(names.map((name, i) => [name.toLowerCase(), i]));
	    }
	    pad(value, fill, width) {
	        const sign = value < 0 ? '-' : '';
	        const string = (sign ? -value : value) + '';
	        const length = string.length;
	        return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);
	    }
	    parseSpecifier(d, specifier, string, j) {
	        let i = 0;
	        const n = specifier.length;
	        const m = string.length;
	        let c;
	        let parse;
	        while (i < n) {
	            if (j >= m) {
	                return -1;
	            }
	            c = specifier.charCodeAt(i++);
	            if (c === 37) {
	                c = specifier.charAt(i++);
	                parse = this.parses[c in this.pads ? specifier.charAt(i++) : c];
	                if (!parse || (j = parse(d, string, j)) < 0) {
	                    return -1;
	                }
	            }
	            else if (c !== string.charCodeAt(j++)) {
	                return -1;
	            }
	        }
	        return j;
	    }
	    newParse(specifier, Z) {
	        const that = this;
	        return function (string) {
	            const d = that.newDate(1900, undefined, 1);
	            const i = that.parseSpecifier(d, specifier, (string += ''), 0);
	            if (i !== string.length) {
	                return null;
	            }
	            if ('Q' in d) {
	                return new Date(d.Q);
	            }
	            if ('s' in d) {
	                return new Date(d.s * 1000 + ('L' in d ? d.L : 0));
	            }
	            if (Z && !('Z' in d)) {
	                d.Z = 0;
	            }
	            if ('p' in d) {
	                d.H = (d.H % 12) + d.p * 12;
	            }
	            if (d.m === undefined) {
	                d.m = 'q' in d ? d.q : 0;
	            }
	            if ('Z' in d) {
	                d.H += (d.Z / 100) | 0;
	                d.M += d.Z % 100;
	                return that.utcDate(d);
	            }
	            return that.localDate(d);
	        };
	    }
	    newFormat(specifier, formats) {
	        const that = this;
	        return function (date) {
	            const string = [];
	            let i = -1;
	            let j = 0;
	            const n = specifier.length;
	            let c;
	            let pad;
	            let format;
	            if (!(date instanceof Date)) {
	                date = new Date(+date);
	            }
	            while (++i < n) {
	                if (specifier.charCodeAt(i) === 37) {
	                    string.push(specifier.slice(j, i));
	                    if ((pad = that.pads[(c = specifier.charAt(++i))])) {
	                        c = specifier.charAt(++i);
	                    }
	                    else {
	                        pad = c === 'e' ? ' ' : '0';
	                    }
	                    format = formats[c];
	                    c = format(date, pad);
	                    string.push(c);
	                    j = i + 1;
	                }
	            }
	            string.push(specifier.slice(j, i));
	            return string.join('');
	        };
	    }
	    getFullTimeStamp(timeText) {
	        const timeOriStamp = parseInt(timeText + '', 10);
	        return String(timeOriStamp).length === 10 ? timeOriStamp * 1000 : timeOriStamp;
	    }
	}

	function formatDecimal(x) {
	    return Math.abs((x = Math.round(x))) >= 1e21 ? x.toLocaleString('en').replace(/,/g, '') : x.toString(10);
	}
	function formatDecimalParts(x, p) {
	    const _x = p ? x.toExponential(p - 1) : x.toExponential();
	    const i = _x.indexOf('e');
	    if (i < 0) {
	        return null;
	    }
	    const coefficient = _x.slice(0, i);
	    return [coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient, +_x.slice(i + 1)];
	}

	function formatGroup(grouping, thousands) {
	    return function (value, width) {
	        let i = value.length;
	        const t = [];
	        let j = 0;
	        let g = grouping[0];
	        let length = 0;
	        while (i > 0 && g > 0) {
	            if (length + g + 1 > width) {
	                g = Math.max(1, width - length);
	            }
	            t.push(value.substring((i -= g), i + g));
	            if ((length += g + 1) > width) {
	                break;
	            }
	            g = grouping[(j = (j + 1) % grouping.length)];
	        }
	        return t.reverse().join(thousands);
	    };
	}

	let prefixExponent;
	function formatPrefixAuto(x, p) {
	    const d = formatDecimalParts(x, p);
	    if (!d) {
	        return x + '';
	    }
	    const coefficient = d[0];
	    const exponent = d[1];
	    const i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1;
	    const n = coefficient.length;
	    return i === n
	        ? coefficient
	        : i > n
	            ? coefficient + new Array(i - n + 1).join('0')
	            : i > 0
	                ? coefficient.slice(0, i) + '.' + coefficient.slice(i)
	                : '0.' + new Array(1 - i).join('0') + formatDecimalParts(x, Math.max(0, p + i - 1))[0];
	}

	function formatRounded(x, p) {
	    const d = formatDecimalParts(x, p);
	    if (!d) {
	        return x + '';
	    }
	    const coefficient = d[0];
	    const exponent = d[1];
	    return exponent < 0
	        ? '0.' + new Array(-exponent).join('0') + coefficient
	        : coefficient.length > exponent + 1
	            ? coefficient.slice(0, exponent + 1) + '.' + coefficient.slice(exponent + 1)
	            : coefficient + new Array(exponent - coefficient.length + 2).join('0');
	}

	function formatTrim(s) {
	    const n = s.length;
	    let i0 = -1;
	    let i1;
	    out: for (let i = 1; i < n; ++i) {
	        switch (s[i]) {
	            case '.':
	                i0 = i1 = i;
	                break;
	            case '0':
	                if (i0 === 0) {
	                    i0 = i;
	                }
	                i1 = i;
	                break;
	            default:
	                if (!+s[i]) {
	                    break out;
	                }
	                if (i0 > 0) {
	                    i0 = 0;
	                }
	                break;
	        }
	    }
	    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;
	}

	class FormatSpecifier {
	    constructor(specifier = {}) {
	        this.fill = specifier.fill === undefined ? ' ' : specifier.fill + '';
	        this.align = specifier.align === undefined ? '>' : specifier.align + '';
	        this.sign = specifier.sign === undefined ? '-' : specifier.sign + '';
	        this.symbol = specifier.symbol === undefined ? '' : specifier.symbol + '';
	        this.zero = !!specifier.zero;
	        this.width = specifier.width === undefined ? undefined : +specifier.width;
	        this.comma = !!specifier.comma;
	        this.precision = specifier.precision === undefined ? undefined : +specifier.precision;
	        this.trim = !!specifier.trim;
	        this.type = specifier.type === undefined ? '' : specifier.type + '';
	    }
	    toString() {
	        return (this.fill +
	            this.align +
	            this.sign +
	            this.symbol +
	            (this.zero ? '0' : '') +
	            (this.width === undefined ? '' : Math.max(1, this.width | 0)) +
	            (this.comma ? ',' : '') +
	            (this.precision === undefined ? '' : '.' + Math.max(0, this.precision | 0)) +
	            (this.trim ? '~' : '') +
	            this.type);
	    }
	}
	const numberSpecifierReg = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
	function formatSpecifier(specifier) {
	    let match;
	    if (!(match = numberSpecifierReg.exec(specifier))) {
	        Logger.getInstance().error('invalid format: ' + specifier);
	        return;
	    }
	    return new FormatSpecifier({
	        fill: match[1],
	        align: match[2],
	        sign: match[3],
	        symbol: match[4],
	        zero: match[5],
	        width: match[6],
	        comma: match[7],
	        precision: match[8] && match[8].slice(1),
	        trim: match[9],
	        type: match[10]
	    });
	}

	const prefixes = ['y', 'z', 'a', 'f', 'p', 'n', 'µ', 'm', '', 'k', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
	class NumberUtil {
	    constructor() {
	        this.locale = {
	            thousands: ',',
	            grouping: [3],
	            currency: ['$', '']
	        };
	        this.group = this.locale.grouping === undefined || this.locale.thousands === undefined
	            ? (group) => group
	            : formatGroup([...this.locale.grouping].map(Number), `${this.locale.thousands}`);
	        this.currencyPrefix = this.locale.currency === undefined ? '' : this.locale.currency[0] + '';
	        this.currencySuffix = this.locale.currency === undefined ? '' : this.locale.currency[1] + '';
	        this.decimal = this.locale.decimal === undefined ? '.' : this.locale.decimal + '';
	        this.numerals = this.locale.numerals === undefined
	            ? (numerals) => numerals
	            : formatNumerals([...this.locale.numerals].map(String));
	        this.percent = this.locale.percent === undefined ? '%' : this.locale.percent + '';
	        this.minus = this.locale.minus === undefined ? '−' : this.locale.minus + '';
	        this.nan = this.locale.nan === undefined ? 'NaN' : this.locale.nan + '';
	        this.formatter = (specifier) => {
	            return this.newFormat(specifier);
	        };
	        this.format = (specifier, value) => {
	            return this.formatter(specifier)(value);
	        };
	        this.formatPrefix = (specifier, value) => {
	            return this._formatPrefix(specifier, value);
	        };
	    }
	    static getInstance() {
	        if (!NumberUtil.instance) {
	            NumberUtil.instance = new NumberUtil();
	        }
	        return NumberUtil.instance;
	    }
	    newFormat(specifier) {
	        const specifierIns = formatSpecifier(specifier);
	        let fill = specifierIns.fill;
	        let align = specifierIns.align;
	        const sign = specifierIns.sign;
	        const symbol = specifierIns.symbol;
	        let zero = specifierIns.zero;
	        const width = specifierIns.width;
	        let comma = specifierIns.comma;
	        let precision = specifierIns.precision;
	        let trim = specifierIns.trim;
	        let type = specifierIns.type;
	        if (type === 'n') {
	            (comma = true), (type = 'g');
	        }
	        else if (!formatTypes[type]) {
	            precision === undefined && (precision = 12), (trim = true), (type = 'g');
	        }
	        if (zero || (fill === '0' && align === '=')) {
	            (zero = true), (fill = '0'), (align = '=');
	        }
	        const prefix = symbol === '$' ? this.currencyPrefix : symbol === '#' && /[boxX]/.test(type) ? '0' + type.toLowerCase() : '';
	        const suffix = symbol === '$' ? this.currencySuffix : /[%p]/.test(type) ? this.percent : '';
	        const formatType = formatTypes[type];
	        const maybeSuffix = /[defgprstz%]/.test(type);
	        precision =
	            precision === undefined
	                ? 6
	                : /[gprs]/.test(type)
	                    ? Math.max(1, Math.min(21, precision))
	                    : Math.max(0, Math.min(20, precision));
	        const { nan, minus, decimal, group, numerals } = this;
	        function format(value) {
	            let valuePrefix = prefix;
	            let valueSuffix = suffix;
	            let i;
	            let n;
	            let c;
	            let _value = value;
	            if (type === 'c') {
	                valueSuffix = formatType(_value) + valueSuffix;
	                _value = '';
	            }
	            else {
	                _value = +_value;
	                let valueNegative = _value < 0 || 1 / _value < 0;
	                _value = isNaN(_value) ? nan : formatType(Math.abs(_value), precision);
	                if (trim) {
	                    _value = formatTrim(_value);
	                }
	                if (valueNegative && +_value === 0 && sign !== '+') {
	                    valueNegative = false;
	                }
	                valuePrefix =
	                    (valueNegative ? (sign === '(' ? sign : minus) : sign === '-' || sign === '(' ? '' : sign) + valuePrefix;
	                valueSuffix =
	                    (type === 's' ? prefixes[8 + prefixExponent / 3] : '') +
	                        valueSuffix +
	                        (valueNegative && sign === '(' ? ')' : '');
	                if (maybeSuffix) {
	                    (i = -1), (n = _value.length);
	                    while (++i < n) {
	                        if (((c = _value.charCodeAt(i)), 48 > c || c > 57)) {
	                            valueSuffix = (c === 46 ? decimal + _value.slice(i + 1) : _value.slice(i)) + valueSuffix;
	                            _value = _value.slice(0, i);
	                            break;
	                        }
	                    }
	                }
	            }
	            if (comma && !zero) {
	                _value = group(_value, Infinity);
	            }
	            let length = valuePrefix.length + _value.length + valueSuffix.length;
	            let padding = length < width ? new Array(width - length + 1).join(fill) : '';
	            if (comma && zero) {
	                _value = group(padding + _value, padding.length ? width - valueSuffix.length : Infinity);
	                padding = '';
	            }
	            switch (align) {
	                case '<':
	                    _value = valuePrefix + _value + valueSuffix + padding;
	                    break;
	                case '=':
	                    _value = valuePrefix + padding + _value + valueSuffix;
	                    break;
	                case '^':
	                    _value =
	                        padding.slice(0, (length = padding.length >> 1)) +
	                            valuePrefix +
	                            _value +
	                            valueSuffix +
	                            padding.slice(length);
	                    break;
	                default:
	                    _value = padding + valuePrefix + _value + valueSuffix;
	                    break;
	            }
	            return numerals(_value);
	        }
	        format.toString = function () {
	            return specifier + '';
	        };
	        return format;
	    }
	    _formatPrefix(specifier, value) {
	        const _specifier = formatSpecifier(specifier);
	        _specifier.type = 'f';
	        const f = this.newFormat(_specifier.toString());
	        const e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3;
	        const k = Math.pow(10, -e);
	        const prefix = prefixes[8 + e / 3];
	        return function (value) {
	            return f(k * value) + prefix;
	        };
	    }
	}
	const formatTypes = {
	    '%': (x, p) => (x * 100).toFixed(p),
	    b: (x) => Math.round(x).toString(2),
	    c: (x) => x + '',
	    d: formatDecimal,
	    f: (x, p) => x.toFixed(p),
	    e: (x, p) => x.toExponential(p),
	    g: (x, p) => x.toPrecision(p),
	    o: (x) => Math.round(x).toString(8),
	    p: (x, p) => formatRounded(x * 100, p),
	    r: formatRounded,
	    s: formatPrefixAuto,
	    X: (x) => Math.round(x).toString(16).toUpperCase(),
	    x: (x) => Math.round(x).toString(16),
	    t: (x, p) => {
	        if (Number.isInteger(x)) {
	            return x.toFixed(2);
	        }
	        return Math.floor(x * Math.pow(10, p)) / Math.pow(10, p) + '';
	    },
	    z: (x, p) => (x % 1 === 0 ? x + '' : x.toFixed(p))
	};
	function exponent(x) {
	    const _x = formatDecimalParts(Math.abs(x));
	    return _x ? _x[1] : NaN;
	}
	function formatNumerals(numerals) {
	    return function (value) {
	        return value.replace(/[0-9]/g, (i) => numerals[+i]);
	    };
	}

	function zeros(x) {
	    const r = new Array(x);
	    for (let i = 0; i < x; ++i) {
	        r[i] = 0;
	    }
	    return r;
	}
	function zerosM(x, y) {
	    return zeros(x).map(function () {
	        return zeros(y);
	    });
	}
	function norm2(a) {
	    return Math.sqrt(dotProduct(a, a));
	}
	function scale(ret, value, c) {
	    for (let i = 0; i < value.length; ++i) {
	        ret[i] = value[i] * c;
	    }
	}
	function weightedSum(ret, w1, v1, w2, v2) {
	    for (let j = 0; j < ret.length; ++j) {
	        ret[j] = w1 * v1[j] + w2 * v2[j];
	    }
	}
	function gemv(output, A, x) {
	    for (let i = 0; i < output.length; ++i) {
	        output[i] = dotProduct(A[i], x);
	    }
	}

	function nelderMead(f, x0, parameters) {
	    parameters = parameters || {};
	    const maxIterations = parameters.maxIterations || x0.length * 200;
	    const nonZeroDelta = parameters.nonZeroDelta || 1.05;
	    const zeroDelta = parameters.zeroDelta || 0.001;
	    const minErrorDelta = parameters.minErrorDelta || 1e-6;
	    const minTolerance = parameters.minErrorDelta || 1e-5;
	    const rho = parameters.rho !== undefined ? parameters.rho : 1;
	    const chi = parameters.chi !== undefined ? parameters.chi : 2;
	    const psi = parameters.psi !== undefined ? parameters.psi : -0.5;
	    const sigma = parameters.sigma !== undefined ? parameters.sigma : 0.5;
	    let maxDiff;
	    const N = x0.length;
	    const simplex = new Array(N + 1);
	    simplex[0] = x0;
	    simplex[0].fx = f(x0);
	    simplex[0].id = 0;
	    for (let i = 0; i < N; ++i) {
	        const point = x0.slice();
	        point[i] = point[i] ? point[i] * nonZeroDelta : zeroDelta;
	        simplex[i + 1] = point;
	        simplex[i + 1].fx = f(point);
	        simplex[i + 1].id = i + 1;
	    }
	    function updateSimplex(value) {
	        for (let i = 0; i < value.length; i++) {
	            simplex[N][i] = value[i];
	        }
	        simplex[N].fx = value.fx;
	    }
	    const sortOrder = function (a, b) {
	        return a.fx - b.fx;
	    };
	    const centroid = x0.slice();
	    const reflected = x0.slice();
	    const contracted = x0.slice();
	    const expanded = x0.slice();
	    for (let iteration = 0; iteration < maxIterations; ++iteration) {
	        simplex.sort(sortOrder);
	        if (parameters.history) {
	            const sortedSimplex = simplex.map(function (x) {
	                const state = x.slice();
	                state.fx = x.fx;
	                state.id = x.id;
	                return state;
	            });
	            sortedSimplex.sort(function (a, b) {
	                return a.id - b.id;
	            });
	            parameters.history.push({ x: simplex[0].slice(), fx: simplex[0].fx, simplex: sortedSimplex });
	        }
	        maxDiff = 0;
	        for (let i = 0; i < N; ++i) {
	            maxDiff = Math.max(maxDiff, Math.abs(simplex[0][i] - simplex[1][i]));
	        }
	        if (Math.abs(simplex[0].fx - simplex[N].fx) < minErrorDelta && maxDiff < minTolerance) {
	            break;
	        }
	        for (let i = 0; i < N; ++i) {
	            centroid[i] = 0;
	            for (let j = 0; j < N; ++j) {
	                centroid[i] += simplex[j][i];
	            }
	            centroid[i] /= N;
	        }
	        const worst = simplex[N];
	        weightedSum(reflected, 1 + rho, centroid, -rho, worst);
	        reflected.fx = f(reflected);
	        if (reflected.fx < simplex[0].fx) {
	            weightedSum(expanded, 1 + chi, centroid, -chi, worst);
	            expanded.fx = f(expanded);
	            if (expanded.fx < reflected.fx) {
	                updateSimplex(expanded);
	            }
	            else {
	                updateSimplex(reflected);
	            }
	        }
	        else if (reflected.fx >= simplex[N - 1].fx) {
	            let shouldReduce = false;
	            if (reflected.fx > worst.fx) {
	                weightedSum(contracted, 1 + psi, centroid, -psi, worst);
	                contracted.fx = f(contracted);
	                if (contracted.fx < worst.fx) {
	                    updateSimplex(contracted);
	                }
	                else {
	                    shouldReduce = true;
	                }
	            }
	            else {
	                weightedSum(contracted, 1 - psi * rho, centroid, psi * rho, worst);
	                contracted.fx = f(contracted);
	                if (contracted.fx < reflected.fx) {
	                    updateSimplex(contracted);
	                }
	                else {
	                    shouldReduce = true;
	                }
	            }
	            if (shouldReduce) {
	                if (sigma >= 1) {
	                    break;
	                }
	                for (let i = 1; i < simplex.length; ++i) {
	                    weightedSum(simplex[i], 1 - sigma, simplex[0], sigma, simplex[i]);
	                    simplex[i].fx = f(simplex[i]);
	                }
	            }
	        }
	        else {
	            updateSimplex(reflected);
	        }
	    }
	    simplex.sort(sortOrder);
	    return { fx: simplex[0].fx, x: simplex[0] };
	}

	function wolfeLineSearch(f, pk, current, next, a, c1, c2) {
	    const phi0 = current.fx;
	    const phiPrime0 = dotProduct(current.fxprime, pk);
	    let phi = phi0;
	    let phi_old = phi0;
	    let phiPrime = phiPrime0;
	    let a0 = 0;
	    a = a || 1;
	    c1 = c1 || 1e-6;
	    c2 = c2 || 0.1;
	    function zoom(a_lo, a_high, phi_lo) {
	        for (let iteration = 0; iteration < 16; ++iteration) {
	            a = (a_lo + a_high) / 2;
	            weightedSum(next.x, 1.0, current.x, a, pk);
	            phi = next.fx = f(next.x, next.fxprime);
	            phiPrime = dotProduct(next.fxprime, pk);
	            if (phi > phi0 + c1 * a * phiPrime0 || phi >= phi_lo) {
	                a_high = a;
	            }
	            else {
	                if (Math.abs(phiPrime) <= -c2 * phiPrime0) {
	                    return a;
	                }
	                if (phiPrime * (a_high - a_lo) >= 0) {
	                    a_high = a_lo;
	                }
	                a_lo = a;
	                phi_lo = phi;
	            }
	        }
	        return 0;
	    }
	    for (let iteration = 0; iteration < 10; ++iteration) {
	        weightedSum(next.x, 1.0, current.x, a, pk);
	        phi = next.fx = f(next.x, next.fxprime);
	        phiPrime = dotProduct(next.fxprime, pk);
	        if (phi > phi0 + c1 * a * phiPrime0 || (iteration && phi >= phi_old)) {
	            return zoom(a0, a, phi_old);
	        }
	        if (Math.abs(phiPrime) <= -c2 * phiPrime0) {
	            return a;
	        }
	        if (phiPrime >= 0) {
	            return zoom(a, a0, phi);
	        }
	        phi_old = phi;
	        a0 = a;
	        a *= 2;
	    }
	    return a;
	}

	function conjugateGradient(f, initial, params) {
	    let current = { x: initial.slice(), fx: 0, fxprime: initial.slice() };
	    let next = { x: initial.slice(), fx: 0, fxprime: initial.slice() };
	    const yk = initial.slice();
	    let temp;
	    let a = 1;
	    params = params || {};
	    const maxIterations = params.maxIterations || initial.length * 20;
	    current.fx = f(current.x, current.fxprime);
	    const pk = current.fxprime.slice();
	    scale(pk, current.fxprime, -1);
	    for (let i = 0; i < maxIterations; ++i) {
	        a = wolfeLineSearch(f, pk, current, next, a);
	        if (params.history) {
	            params.history.push({ x: current.x.slice(), fx: current.fx, fxprime: current.fxprime.slice(), alpha: a });
	        }
	        if (!a) {
	            scale(pk, current.fxprime, -1);
	        }
	        else {
	            weightedSum(yk, 1, next.fxprime, -1, current.fxprime);
	            const delta_k = dotProduct(current.fxprime, current.fxprime);
	            const beta_k = Math.max(0, dotProduct(yk, next.fxprime) / delta_k);
	            weightedSum(pk, beta_k, pk, -1, next.fxprime);
	            temp = current;
	            current = next;
	            next = temp;
	        }
	        if (norm2(current.fxprime) <= 1e-5) {
	            break;
	        }
	    }
	    if (params.history) {
	        params.history.push({ x: current.x.slice(), fx: current.fx, fxprime: current.fxprime.slice(), alpha: a });
	    }
	    return current;
	}

	exports.AABBBounds = AABBBounds;
	exports.Bounds = Bounds;
	exports.Color = Color;
	exports.ColorUtil = index;
	exports.DAY = DAY;
	exports.DEFAULT_COLORS = DEFAULT_COLORS;
	exports.EventEmitter = index$1;
	exports.FormatSpecifier = FormatSpecifier;
	exports.GraphicUtil = GraphicUtil;
	exports.HOUR = HOUR;
	exports.HashTable = HashTable;
	exports.HashValue = HashValue;
	exports.LRU = LRU;
	exports.Logger = Logger;
	exports.MINUTE = MINUTE;
	exports.MONTH = MONTH;
	exports.Matrix = Matrix;
	exports.NEWTON_ITERATIONS = NEWTON_ITERATIONS;
	exports.NEWTON_MIN_SLOPE = NEWTON_MIN_SLOPE;
	exports.NumberUtil = NumberUtil;
	exports.OBBBounds = OBBBounds;
	exports.Point = Point;
	exports.PointService = PointService;
	exports.PolarPoint = PolarPoint;
	exports.RGB = RGB;
	exports.SECOND = SECOND;
	exports.SMALL = SMALL;
	exports.SUBDIVISION_MAX_ITERATIONS = SUBDIVISION_MAX_ITERATIONS;
	exports.SUBDIVISION_PRECISION = SUBDIVISION_PRECISION;
	exports.TextMeasure = TextMeasure;
	exports.TimeUtil = TimeUtil;
	exports.YEAR = YEAR;
	exports.aabbSeparation = aabbSeparation;
	exports.abs = abs;
	exports.acos = acos;
	exports.array = array;
	exports.arrayEqual = arrayEqual;
	exports.ascending = ascending;
	exports.asin = asin;
	exports.atan2 = atan2;
	exports.baseMerge = baseMerge;
	exports.binaryFuzzySearch = binaryFuzzySearch;
	exports.binaryFuzzySearchInNumberRange = binaryFuzzySearchInNumberRange;
	exports.bisect = bisect;
	exports.calculateAnchorOfArc = calculateAnchorOfArc;
	exports.calculateAnchorOfBounds = calculateAnchorOfBounds;
	exports.calculateMaxRadius = calculateMaxRadius;
	exports.cartesianToPolar = cartesianToPolar;
	exports.circleArea = circleArea;
	exports.circleCircleIntersection = circleCircleIntersection;
	exports.circleOverlap = circleOverlap;
	exports.clamp = clamp;
	exports.clampAngleByDegree = clampAngleByDegree;
	exports.clampAngleByRadian = clampAngleByRadian;
	exports.clampDegree = clampDegree;
	exports.clampRadian = clampRadian;
	exports.clampRange = clampRange;
	exports.clamper = clamper;
	exports.clone = clone;
	exports.cloneDeep = cloneDeep;
	exports.computeQuadrant = computeQuadrant;
	exports.conjugateGradient = conjugateGradient;
	exports.constant = constant;
	exports.containedInCircles = containedInCircles;
	exports.cos = cos;
	exports.crossProduct = crossProduct;
	exports.crossProductPoint = crossProductPoint;
	exports.dateGetterName = dateGetterName;
	exports.dateSetterName = dateSetterName;
	exports.dayCount = dayCount;
	exports.dayField = dayField;
	exports.dayFloor = dayFloor;
	exports.dayOffset = dayOffset;
	exports.debounce = debounce;
	exports.defaults = defaults;
	exports.degreeToRadian = degreeToRadian;
	exports.destination = destination;
	exports.deviation = deviation;
	exports.dotProduct = dotProduct;
	exports.eastAsianCharacterInfo = eastAsianCharacterInfo;
	exports.epsilon = epsilon;
	exports.exponent = exponent;
	exports.extent = extent;
	exports.fakeRandom = fakeRandom;
	exports.field = field;
	exports.findBoundaryAngles = findBoundaryAngles;
	exports.findZeroOfFunction = findZeroOfFunction;
	exports.fixPrecision = fixPrecision;
	exports.flattenArray = flattenArray;
	exports.formatNumerals = formatNumerals;
	exports.formatSpecifier = formatSpecifier;
	exports.formatTypes = formatTypes;
	exports.fullYearGetterName = fullYearGetterName;
	exports.fullYearSetterName = fullYearSetterName;
	exports.fuzzyEqualNumber = fuzzyEqualNumber;
	exports.fuzzyEqualVec = fuzzyEqualVec;
	exports.gemv = gemv;
	exports.generateCeil = generateCeil;
	exports.generateCount = generateCount;
	exports.generateStepInterval = generateStepInterval;
	exports.get = get;
	exports.getAABBFromPoints = getAABBFromPoints;
	exports.getAngleByPoint = getAngleByPoint;
	exports.getCenter = getCenter;
	exports.getContainerSize = getContainerSize;
	exports.getContextFont = getContextFont;
	exports.getDecimalPlaces = getDecimalPlaces;
	exports.getElementAbsolutePosition = getElementAbsolutePosition;
	exports.getElementRelativePosition = getElementRelativePosition;
	exports.getFormatFromValue = getFormatFromValue;
	exports.getIntersectPoint = getIntersectPoint;
	exports.getIntervalOptions = getIntervalOptions;
	exports.getOBBFromLine = getOBBFromLine;
	exports.getRectIntersect = getRectIntersect;
	exports.getScale = getScale;
	exports.getScaleX = getScaleX;
	exports.getScaleY = getScaleY;
	exports.getScrollLeft = getScrollLeft;
	exports.getScrollTop = getScrollTop;
	exports.getTimeFormatter = getTimeFormatter;
	exports.getter = getter;
	exports.halfPi = halfPi;
	exports.has = has;
	exports.hasParentElement = hasParentElement;
	exports.hexToRgb = hexToRgb;
	exports.hourCount = hourCount;
	exports.hourField = hourField;
	exports.hourFloor = hourFloor;
	exports.hourOffset = hourOffset;
	exports.hoursGetterName = hoursGetterName;
	exports.hoursSetterName = hoursSetterName;
	exports.hslToRgb = hslToRgb;
	exports.interpolateDate = interpolateDate;
	exports.interpolateNumber = interpolateNumber;
	exports.interpolateNumberRound = interpolateNumberRound;
	exports.interpolateRgb = interpolateRgb;
	exports.interpolateString = interpolateString;
	exports.intersectionArea = intersectionArea;
	exports.isArray = isArray;
	exports.isArrayLike = isArrayLike;
	exports.isBase64 = isBase64;
	exports.isBoolean = isBoolean;
	exports.isDate = isDate;
	exports.isEmpty = isEmpty;
	exports.isEqual = isEqual;
	exports.isFunction = isFunction;
	exports.isGreater = isGreater;
	exports.isHTMLElement = isHTMLElement;
	exports.isIntersect = isIntersect;
	exports.isLess = isLess;
	exports.isNil = isNil;
	exports.isNull = isNull;
	exports.isNumber = isNumber;
	exports.isNumberClose = isNumberClose;
	exports.isNumeric = isNumeric;
	exports.isObject = isObject;
	exports.isObjectLike = isObjectLike;
	exports.isPlainObject = isPlainObject;
	exports.isPointInLine = isPointInLine;
	exports.isPointInPolygon = isPointInPolygon;
	exports.isRectIntersect = isRectIntersect;
	exports.isRegExp = isRegExp;
	exports.isRotateAABBIntersect = isRotateAABBIntersect;
	exports.isShallowEqual = isShallowEqual;
	exports.isString = isString;
	exports.isType = isType;
	exports.isUndefined = isUndefined;
	exports.isValid = isValid;
	exports.isValidNumber = isValidNumber;
	exports.isValidUrl = isValidUrl;
	exports.keys = keys;
	exports.last = last;
	exports.lengthFromPointToLine = lengthFromPointToLine;
	exports.lineIntersectPolygon = lineIntersectPolygon;
	exports.lowerCamelCaseToMiddle = lowerCamelCaseToMiddle;
	exports.lowerFirst = lowerFirst;
	exports.max = max;
	exports.maxInArray = maxInArray;
	exports.median = median;
	exports.memoize = memoize;
	exports.merge = merge;
	exports.mergeAABB = mergeAABB;
	exports.millisecondsCount = millisecondsCount;
	exports.millisecondsFloor = millisecondsFloor;
	exports.millisecondsGetterName = millisecondsGetterName;
	exports.millisecondsOffset = millisecondsOffset;
	exports.millisecondsSetterName = millisecondsSetterName;
	exports.min = min;
	exports.minInArray = minInArray;
	exports.minuteCount = minuteCount;
	exports.minuteField = minuteField;
	exports.minuteFloor = minuteFloor;
	exports.minuteOffset = minuteOffset;
	exports.minutesGetterName = minutesGetterName;
	exports.minutesSetterName = minutesSetterName;
	exports.mixin = mixin;
	exports.monthCount = monthCount;
	exports.monthField = monthField;
	exports.monthFloor = monthFloor;
	exports.monthGetterName = monthGetterName;
	exports.monthOffset = monthOffset;
	exports.monthSetterName = monthSetterName;
	exports.nelderMead = nelderMead;
	exports.norm2 = norm2;
	exports.normalTransform = normalTransform;
	exports.normalizeAngle = normalizeAngle;
	exports.normalizePadding = normalizePadding;
	exports.numberSpecifierReg = numberSpecifierReg;
	exports.obbSeparation = obbSeparation;
	exports.ordinaryLeastSquares = ordinaryLeastSquares;
	exports.pad = pad;
	exports.parseUint8ToImageData = parseUint8ToImageData;
	exports.pi = pi;
	exports.pi2 = pi2;
	exports.pick = pick;
	exports.pickWithout = pickWithout;
	exports.pointAt = pointAt;
	exports.pointBetweenLine = pointBetweenLine;
	exports.pointInAABB = pointInAABB;
	exports.pointInLine = pointInLine;
	exports.pointInOBB = pointInOBB;
	exports.pointInRect = pointInRect;
	exports.polarToCartesian = polarToCartesian;
	exports.polygonContainPoint = polygonContainPoint;
	exports.polygonIntersectPolygon = polygonIntersectPolygon;
	exports.pow = pow;
	exports.precisionAdd = precisionAdd;
	exports.precisionSub = precisionSub;
	exports.quantileSorted = quantileSorted;
	exports.rSquared = rSquared;
	exports.radianToDegree = radianToDegree;
	exports.randomLCG = randomLCG;
	exports.range = range;
	exports.rectInsideAnotherRect = rectInsideAnotherRect;
	exports.regressionLinear = regressionLinear;
	exports.rgbToHex = rgbToHex;
	exports.rgbToHsl = rgbToHsl;
	exports.rotatePoint = rotatePoint;
	exports.scale = scale;
	exports.secondCount = secondCount;
	exports.secondField = secondField;
	exports.secondFloor = secondFloor;
	exports.secondOffset = secondOffset;
	exports.secondsGetterName = secondsGetterName;
	exports.secondsSetterName = secondsSetterName;
	exports.seedRandom = seedRandom;
	exports.shuffleArray = shuffleArray;
	exports.simpleField = simpleField;
	exports.sin = sin;
	exports.span = span;
	exports.sqrt = sqrt;
	exports.stringWidth = stringWidth;
	exports.styleStringToObject = styleStringToObject;
	exports.substitute = substitute;
	exports.tau = tau;
	exports.throttle = throttle;
	exports.tickStep = tickStep;
	exports.toCamelCase = toCamelCase;
	exports.toDate = toDate;
	exports.toNumber = toNumber;
	exports.toPercent = toPercent;
	exports.toValidNumber = toValidNumber;
	exports.transformBounds = transformBounds;
	exports.transformBoundsWithMatrix = transformBoundsWithMatrix;
	exports.truncate = truncate;
	exports.unionAABB = unionAABB;
	exports.uniqArray = uniqArray;
	exports.upperFirst = upperFirst;
	exports.utcDayCount = utcDayCount;
	exports.utcDayField = utcDayField;
	exports.utcDayFloor = utcDayFloor;
	exports.utcDayOffset = utcDayOffset;
	exports.utcHourField = utcHourField;
	exports.utcHourFloor = utcHourFloor;
	exports.utcHourOffset = utcHourOffset;
	exports.utcMinuteField = utcMinuteField;
	exports.utcMinuteFloor = utcMinuteFloor;
	exports.utcMinuteOffset = utcMinuteOffset;
	exports.utcMonthCount = utcMonthCount;
	exports.utcMonthField = utcMonthField;
	exports.utcMonthFloor = utcMonthFloor;
	exports.utcMonthOffset = utcMonthOffset;
	exports.utcSecondField = utcSecondField;
	exports.utcSecondFloor = utcSecondFloor;
	exports.utcSecondOffset = utcSecondOffset;
	exports.utcYearCount = utcYearCount;
	exports.utcYearField = utcYearField;
	exports.utcYearFloor = utcYearFloor;
	exports.utcYearOffset = utcYearOffset;
	exports.uuid = uuid;
	exports.variance = variance;
	exports.visitPoints = visitPoints;
	exports.weightedSum = weightedSum;
	exports.yearCount = yearCount;
	exports.yearField = yearField;
	exports.yearFloor = yearFloor;
	exports.yearOffset = yearOffset;
	exports.zero = zero;
	exports.zeros = zeros;
	exports.zerosM = zerosM;

}));
