!function(E,e,P,T){"use strict";void 0!==(e=void 0!==e&&e.Math==Math?e:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")())&&e.Math==Math||"undefined"!=typeof self&&self.Math==Math||Function("return this")();E.fn.progress=function(m){var b,e=E(this),h=e.selector||"",x=(new Date).getTime(),w=[],y=m,V="string"==typeof y,C=[].slice.call(arguments,1);return e.each(function(){var r=E.isPlainObject(m)?E.extend(!0,{},E.fn.progress.settings,m):E.extend({},E.fn.progress.settings),t=r.className,n=r.metadata,e=r.namespace,a=r.selector,s=r.error,o="."+e,i="module-"+e,l=E(this),c=E(this).find(a.bar),u=E(this).find(a.progress),d=E(this).find(a.label),g=this,v=l.data(i),p=!1,f={initialize:function(){f.debug("Initializing progress bar",r),f.set.duration(),f.set.transitionEvent(),f.read.metadata(),f.read.settings(),f.instantiate()},instantiate:function(){f.verbose("Storing instance of progress",f),v=f,l.data(i,f)},destroy:function(){f.verbose("Destroying previous progress for",l),clearInterval(v.interval),f.remove.state(),l.removeData(i),v=T},reset:function(){f.remove.nextValue(),f.update.progress(0)},complete:function(){(f.percent===T||f.percent<100)&&(f.remove.progressPoll(),f.set.percent(100))},read:{metadata:function(){var e={percent:l.data(n.percent),total:l.data(n.total),value:l.data(n.value)};e.percent&&(f.debug("Current percent value set from metadata",e.percent),f.set.percent(e.percent)),e.total&&(f.debug("Total value set from metadata",e.total),f.set.total(e.total)),e.value&&(f.debug("Current value set from metadata",e.value),f.set.value(e.value),f.set.progress(e.value))},settings:function(){!1!==r.total&&(f.debug("Current total set in settings",r.total),f.set.total(r.total)),!1!==r.value&&(f.debug("Current value set in settings",r.value),f.set.value(r.value),f.set.progress(f.value)),!1!==r.percent&&(f.debug("Current percent set in settings",r.percent),f.set.percent(r.percent))}},bind:{transitionEnd:function(t){var e=f.get.transitionEnd();c.one(e+o,function(e){clearTimeout(f.failSafeTimer),t.call(this,e)}),f.failSafeTimer=setTimeout(function(){c.triggerHandler(e)},r.duration+r.failSafeDelay),f.verbose("Adding fail safe timer",f.timer)}},increment:function(e){var t,n;f.has.total()?n=(t=f.get.value())+(e=e||1):(n=(t=f.get.percent())+(e=e||f.get.randomValue()),f.debug("Incrementing percentage by",t,n)),n=f.get.normalizedValue(n),f.set.progress(n)},decrement:function(e){var t,n;f.get.total()?(n=(t=f.get.value())-(e=e||1),f.debug("Decrementing value by",e,t)):(n=(t=f.get.percent())-(e=e||f.get.randomValue()),f.debug("Decrementing percentage by",e,t)),n=f.get.normalizedValue(n),f.set.progress(n)},has:{progressPoll:function(){return f.progressPoll},total:function(){return!1!==f.get.total()}},get:{text:function(e){var t=f.value||0,n=f.total||0,r=p?f.get.displayPercent():f.percent||0,a=0<f.total?n-t:100-r;return e=(e=e||"").replace("{value}",t).replace("{total}",n).replace("{left}",a).replace("{percent}",r),f.verbose("Adding variables to progress bar text",e),e},normalizedValue:function(e){if(e<0)return f.debug("Value cannot decrement below 0"),0;if(f.has.total()){if(e>f.total)return f.debug("Value cannot increment above total",f.total),f.total}else if(100<e)return f.debug("Value cannot increment above 100 percent"),100;return e},updateInterval:function(){return"auto"==r.updateInterval?r.duration:r.updateInterval},randomValue:function(){return f.debug("Generating random increment percentage"),Math.floor(Math.random()*r.random.max+r.random.min)},numericValue:function(e){return"string"==typeof e?""!==e.replace(/[^\d.]/g,"")&&+e.replace(/[^\d.]/g,""):e},transitionEnd:function(){var e,t=P.createElement("element"),n={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in n)if(t.style[e]!==T)return n[e]},displayPercent:function(){var e=c.width(),t=l.width(),n=parseInt(c.css("min-width"),10)<e?e/t*100:f.percent;return 0<r.precision?Math.round(n*(10*r.precision))/(10*r.precision):Math.round(n)},percent:function(){return f.percent||0},value:function(){return f.nextValue||f.value||0},total:function(){return f.total||!1}},create:{progressPoll:function(){f.progressPoll=setTimeout(function(){f.update.toNextValue(),f.remove.progressPoll()},f.get.updateInterval())}},is:{complete:function(){return f.is.success()||f.is.warning()||f.is.error()},success:function(){return l.hasClass(t.success)},warning:function(){return l.hasClass(t.warning)},error:function(){return l.hasClass(t.error)},active:function(){return l.hasClass(t.active)},visible:function(){return l.is(":visible")}},remove:{progressPoll:function(){f.verbose("Removing progress poll timer"),f.progressPoll&&(clearTimeout(f.progressPoll),delete f.progressPoll)},nextValue:function(){f.verbose("Removing progress value stored for next update"),delete f.nextValue},state:function(){f.verbose("Removing stored state"),delete f.total,delete f.percent,delete f.value},active:function(){f.verbose("Removing active state"),l.removeClass(t.active)},success:function(){f.verbose("Removing success state"),l.removeClass(t.success)},warning:function(){f.verbose("Removing warning state"),l.removeClass(t.warning)},error:function(){f.verbose("Removing error state"),l.removeClass(t.error)}},set:{barWidth:function(e){100<e?f.error(s.tooHigh,e):e<0?f.error(s.tooLow,e):(c.css("width",e+"%"),l.attr("data-percent",parseInt(e,10)))},duration:function(e){e="number"==typeof(e=e||r.duration)?e+"ms":e,f.verbose("Setting progress bar transition duration",e),c.css({"transition-duration":e})},percent:function(e){e="string"==typeof e?+e.replace("%",""):e,e=0<r.precision?Math.round(e*(10*r.precision))/(10*r.precision):Math.round(e),f.percent=e,f.has.total()||(f.value=0<r.precision?Math.round(e/100*f.total*(10*r.precision))/(10*r.precision):Math.round(e/100*f.total*10)/10,r.limitValues&&(f.value=100<f.value?100:f.value<0?0:f.value)),f.set.barWidth(e),f.set.labelInterval(),f.set.labels(),r.onChange.call(g,e,f.value,f.total)},labelInterval:function(){clearInterval(f.interval),f.bind.transitionEnd(function(){f.verbose("Bar finished animating, removing continuous label updates"),clearInterval(f.interval),p=!1,f.set.labels()}),p=!0,f.interval=setInterval(function(){E.contains(P.documentElement,g)||(clearInterval(f.interval),p=!1),f.set.labels()},r.framerate)},labels:function(){f.verbose("Setting both bar progress and outer label text"),f.set.barLabel(),f.set.state()},label:function(e){(e=e||"")&&(e=f.get.text(e),f.verbose("Setting label to text",e),d.text(e))},state:function(e){100===(e=e!==T?e:f.percent)?r.autoSuccess&&!(f.is.warning()||f.is.error()||f.is.success())?(f.set.success(),f.debug("Automatically triggering success at 100%")):(f.verbose("Reached 100% removing active state"),f.remove.active(),f.remove.progressPoll()):0<e?(f.verbose("Adjusting active progress bar label",e),f.set.active()):(f.remove.active(),f.set.label(r.text.active))},barLabel:function(e){e!==T?u.text(f.get.text(e)):"ratio"==r.label&&f.total?(f.verbose("Adding ratio to bar label"),u.text(f.get.text(r.text.ratio))):"percent"==r.label&&(f.verbose("Adding percentage to bar label"),u.text(f.get.text(r.text.percent)))},active:function(e){e=e||r.text.active,f.debug("Setting active state"),r.showActivity&&!f.is.active()&&l.addClass(t.active),f.remove.warning(),f.remove.error(),f.remove.success(),(e=r.onLabelUpdate("active",e,f.value,f.total))&&f.set.label(e),f.bind.transitionEnd(function(){r.onActive.call(g,f.value,f.total)})},success:function(e){e=e||r.text.success||r.text.active,f.debug("Setting success state"),l.addClass(t.success),f.remove.active(),f.remove.warning(),f.remove.error(),f.complete(),e=r.text.success?r.onLabelUpdate("success",e,f.value,f.total):r.onLabelUpdate("active",e,f.value,f.total),f.set.label(e),f.bind.transitionEnd(function(){r.onSuccess.call(g,f.total)})},warning:function(e){e=e||r.text.warning,f.debug("Setting warning state"),l.addClass(t.warning),f.remove.active(),f.remove.success(),f.remove.error(),f.complete(),(e=r.onLabelUpdate("warning",e,f.value,f.total))&&f.set.label(e),f.bind.transitionEnd(function(){r.onWarning.call(g,f.value,f.total)})},error:function(e){e=e||r.text.error,f.debug("Setting error state"),l.addClass(t.error),f.remove.active(),f.remove.success(),f.remove.warning(),f.complete(),(e=r.onLabelUpdate("error",e,f.value,f.total))&&f.set.label(e),f.bind.transitionEnd(function(){r.onError.call(g,f.value,f.total)})},transitionEvent:function(){f.get.transitionEnd()},total:function(e){f.total=e},value:function(e){f.value=e},progress:function(e){f.has.progressPoll()?(f.debug("Updated within interval, setting next update to use new value",e),f.set.nextValue(e)):(f.debug("First update in progress update interval, immediately updating",e),f.update.progress(e),f.create.progressPoll())},nextValue:function(e){f.nextValue=e}},update:{toNextValue:function(){var e=f.nextValue;e&&(f.debug("Update interval complete using last updated value",e),f.update.progress(e),f.remove.nextValue())},progress:function(e){var t;!1===(e=f.get.numericValue(e))&&f.error(s.nonNumeric,e),e=f.get.normalizedValue(e),f.has.total()?(f.set.value(e),t=e/f.total*100,f.debug("Calculating percent complete from total",t)):(t=e,f.debug("Setting value to exact percentage value",t)),f.set.percent(t)}},setting:function(e,t){if(f.debug("Changing setting",e,t),E.isPlainObject(e))E.extend(!0,r,e);else{if(t===T)return r[e];E.isPlainObject(r[e])?E.extend(!0,r[e],t):r[e]=t}},internal:function(e,t){if(E.isPlainObject(e))E.extend(!0,f,e);else{if(t===T)return f[e];f[e]=t}},debug:function(){!r.silent&&r.debug&&(r.performance?f.performance.log(arguments):(f.debug=Function.prototype.bind.call(console.info,console,r.name+":"),f.debug.apply(console,arguments)))},verbose:function(){!r.silent&&r.verbose&&r.debug&&(r.performance?f.performance.log(arguments):(f.verbose=Function.prototype.bind.call(console.info,console,r.name+":"),f.verbose.apply(console,arguments)))},error:function(){r.silent||(f.error=Function.prototype.bind.call(console.error,console,r.name+":"),f.error.apply(console,arguments))},performance:{log:function(e){var t,n;r.performance&&(n=(t=(new Date).getTime())-(x||t),x=t,w.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:g,"Execution Time":n})),clearTimeout(f.performance.timer),f.performance.timer=setTimeout(f.performance.display,500)},display:function(){var e=r.name+":",n=0;x=!1,clearTimeout(f.performance.timer),E.each(w,function(e,t){n+=t["Execution Time"]}),e+=" "+n+"ms",h&&(e+=" '"+h+"'"),(console.group!==T||console.table!==T)&&0<w.length&&(console.groupCollapsed(e),console.table?console.table(w):E.each(w,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),w=[]}},invoke:function(r,e,t){var a,o,n,i=v;return e=e||C,t=g||t,"string"==typeof r&&i!==T&&(r=r.split(/[\. ]/),a=r.length-1,E.each(r,function(e,t){var n=e!=a?t+r[e+1].charAt(0).toUpperCase()+r[e+1].slice(1):r;if(E.isPlainObject(i[n])&&e!=a)i=i[n];else{if(i[n]!==T)return o=i[n],!1;if(!E.isPlainObject(i[t])||e==a)return i[t]!==T?o=i[t]:f.error(s.method,r),!1;i=i[t]}})),E.isFunction(o)?n=o.apply(t,e):o!==T&&(n=o),E.isArray(b)?b.push(n):b!==T?b=[b,n]:n!==T&&(b=n),o}};V?(v===T&&f.initialize(),f.invoke(y)):(v!==T&&v.invoke("destroy"),f.initialize())}),b!==T?b:this},E.fn.progress.settings={name:"Progress",namespace:"progress",silent:!1,debug:!1,verbose:!1,performance:!0,random:{min:2,max:5},duration:300,updateInterval:"auto",autoSuccess:!0,showActivity:!0,limitValues:!0,label:"percent",precision:0,framerate:1e3/30,percent:!1,total:!1,value:!1,failSafeDelay:100,onLabelUpdate:function(e,t,n,r){return t},onChange:function(e,t,n){},onSuccess:function(e){},onActive:function(e,t){},onError:function(e,t){},onWarning:function(e,t){},error:{method:"The method you called is not defined.",nonNumeric:"Progress value is non numeric",tooHigh:"Value specified is above 100%",tooLow:"Value specified is below 0%"},regExp:{variable:/\{\$*[A-z0-9]+\}/g},metadata:{percent:"percent",total:"total",value:"value"},selector:{bar:"> .bar",label:"> .label",progress:".bar > .progress"},text:{active:!1,error:!1,success:!1,warning:!1,percent:"{percent}%",ratio:"{value} of {total}"},className:{active:"active",error:"error",success:"success",warning:"warning"}}}(jQuery,window,document);