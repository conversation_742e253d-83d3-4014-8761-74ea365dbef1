var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
import * as React from "react";
function SvgComponent(props) {
  return /* @__PURE__ */ React.createElement("svg", __spreadValues({
    width: 200,
    height: 200,
    viewBox: "0 0 200 200",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    focusable: false,
    "aria-hidden": true
  }, props), /* @__PURE__ */ React.createElement("rect", {
    width: 200,
    height: 200,
    fill: "transparent"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M64.09 64.86V8.56L5.76 78.94h44.26v20.3h14.07v-20.3h8.82V64.86h-8.82Zm110.18 0V8.56L124.1 69.08c.7-3.74 1.07-7.7 1.07-11.76 0-11-2.7-21.1-7.25-28.55-4.5-7.4-11.13-12.63-19-12.63-7.88 0-14.5 5.23-19.01 12.63-4.54 7.45-7.25 17.55-7.25 28.55s2.7 21.1 7.25 28.55c4.5 7.4 11.13 12.62 19 12.62 7.88 0 14.5-5.23 19.01-12.62a44.09 44.09 0 0 0 3.4-6.93h38.86v20.3h14.08v-20.3h8.81V64.86h-8.81Zm-138.57 0L50.02 47.6v17.26H35.7Zm124.5 0h-14.32L160.2 47.6v17.26Zm-49.1-7.54c0 8.84-2.2 16.32-5.19 21.22-3.08 5.08-5.87 5.87-6.98 5.87-1.12 0-3.9-.8-7-5.87-2.98-4.9-5.19-12.38-5.19-21.22 0-8.85 2.21-16.33 5.2-21.23 3.09-5.07 5.87-5.87 6.98-5.87 1.12 0 3.9.8 7 5.87 2.98 4.9 5.19 12.38 5.19 21.23Z",
    fill: "var(--semi-color-primary-light-default)"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M178.34 186.57c-1.85 2.2-6.66 1.02-8.13.24a2.46 2.46 0 0 1-.34-.22c-1.94.22-14.94-.5-15.12-.46-6.12 1.43-14.06 1.03-15.2 1-1.36-.05-4.53-1.33-5.75-5.08-1.2-3.67 0-7.12 2.16-13.37l.13-.38-1.43 2.83c-7.37 14.43-11.05 19.7-17.43 20.14-6.38.45-34.34-.09-41.09-.78-6.75-.7-9.42-1.68-9.35-2.83.05-.92 2.98-2.2 4.44-2.72-5.88-1.57-20.16-5.97-30.18-10.98-12.52-6.26-25.14-15.8-21.91-28.92 3.22-13.13 15.76-16.8 25.59-17.38 2.75-.16 5.14-.1 7.16.07 3.76.33 6.21 1.06 7.2 1.53-1.12-6.37-3.33-19.34-3.33-20.21 0-1.1-3.02-10.67-3.64-14.35-.1-.55-1.11-5.6 3.64-5.02 4.75.57 12.4 18.2 15.88 28.7a401.55 401.55 0 0 0 11.15-9.87c.84-1.02 9.36-9.75 14.87-13.84a60.09 60.09 0 0 1 33-11.76c16.29-.81 34.49 2.36 40.9 11.91 6.4 9.55 6.1 34.44 6.44 48.71.35 14.27.84 27.64 2.11 29.76 1.27 2.12 6.45 5.02 9.77 7.14 3.33 2.11 4.45 3.9 4.36 4.2-.1.3-.4.58-2.24 1.08.34.47.12 1.38-2.12 1.6-4.7.48-8.64-.74-11.54-.74Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M192 185.71c-1.6-2.3-9.7-5.34-15.04-5.75-5.34-.41-6.45.53-7.4 1.85-1 1.37-1.05 3.72.3 4.78m22.14-.88c.34.47.12 1.38-2.12 1.6-4.7.48-8.64-.74-11.54-.74-1.85 2.2-6.66 1.02-8.13.24a2.46 2.46 0 0 1-.34-.22m22.13-.88c1.84-.5 2.15-.78 2.24-1.08.09-.3-1.03-2.09-4.36-4.2-3.32-2.12-8.5-5.02-9.77-7.14-1.27-2.12-1.76-15.49-2.1-29.76-.36-14.27-.06-39.16-6.46-48.7-6.4-9.56-24.6-12.73-40.89-11.92a60.09 60.09 0 0 0-33 11.76c-5.5 4.09-14.03 12.82-14.87 13.84-.37.46-5.43 4.9-11.15 9.87-3.49-10.5-11.13-28.13-15.88-28.7-4.75-.58-3.73 4.47-3.64 5.02.62 3.68 3.64 13.26 3.64 14.35 0 .87 2.21 13.84 3.32 20.2-.98-.46-3.43-1.2-7.19-1.52m19.34 57.21c-5.88-1.57-20.16-5.97-30.18-10.98-12.52-6.26-25.14-15.8-21.91-28.92 3.22-13.13 15.76-16.8 25.59-17.38 2.75-.16 5.14-.1 7.16.07m19.34 57.21c-1.46.52-4.39 1.8-4.44 2.72-.07 1.15 2.6 2.14 9.35 2.83 6.75.7 34.71 1.23 41.09.78 6.38-.44 10.06-5.71 17.43-20.14l1.43-2.83m-64.86 16.64c5.89-2.49 12.35-6.32 16.49-10.11 5.07-4.65 9.55-13.46 9.96-14.28l.82-1.56m43.43 4.15c-.47-3-1.21-7.33.37-14.67 1.47-6.81 6.6-15.3 6.65-11.03.05 4-6.05 17.35-12.86 30.86m0 0c-2.25 6.5-3.51 10-2.3 13.75 1.23 3.75 4.4 5.03 5.76 5.07 1.14.04 9.08.44 15.2-.99m0 0a19.5 19.5 0 0 0 3.27-1.03c6.09-2.63 10.92-8.55 13.53-24.26 2.62-15.71 1.5-42.36-10.29-50.78-11.78-8.41-21.32-6.07-34.03 5.8-5.34 4.98-11.86 14.52-17.42 23.52m44.94 46.75c.18-.04 13.19.68 15.12.46m-60.06-47.2c-.8-3.66-1.44-7.16-1.94-10.43m1.94 10.42c-1.24 2-2.43 3.98-3.55 5.87m.32-30.9c0 3.75.41 8.8 1.3 14.61m0 0c-2.32 2.46-7.78 7.15-13.67 11.55m-42.32-12.78s1.24 7.12 3.87 11.42a29.93 29.93 0 0 0 6.61 7.8m36.13 12.04c-6.87-3.1-17.87-6.8-22.51-8.27m22.51 8.27a394.7 394.7 0 0 1 7.76-13.74M76 150.72c.39-.06.78-.14 1.17-.23 3.97-.93 10.76-5.29 17.05-9.98m-18.22 10.2c-4.7.8-9.36-.34-13.62-3.76m0 0c-3.29-.85-11.28-2.39-12.86-2.41m56.75.71c-2.32-.94-9-3.55-12.05-4.74",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "m77.74 73.31 23.73 43.2-1.7 2.5-22.73 9.55-24.58-44.48 21.9-10.77h3.38Z",
    fill: "#E6E8EA"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M74.35 73.31h3.4l23.72 43.2-1.7 2.5m-25.42-45.7-21.9 10.77 24.59 44.48L99.78 119m-25.43-45.7 25.43 45.7",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M83.9 116.95c.96 1.97-4.91 4.57-8.32 6.18-1.33.63-2.1.98-2.7 1.2.9 3.3 1.24 8.14-4.48 9.82-5.73 1.69-15.07-1.02-18-9.6-2.95-8.6.01-19.97 9.05-27.04 5.67-4.42 15.01-5.77 19.42-4.05 2.15.82 1.38 2.71-.56 4.44a58.1 58.1 0 0 1-4.53 3.58c.4-.01.8-.02 1.23 0 4.13.06 7.26 1.11 7.42 3.14.07.94-.8 2.13-1.95 3.45-.62.71-1.28 1.34-1.95 1.88 2.8-.22 5.94 1.05 2.9 4.2-.41.43-.85.83-1.29 1.2 1.73.02 3.24.56 3.75 1.6Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M69.73 125.1c1.65-.34 2.2-.4 3.14-.76m0 0c.6-.23 1.38-.58 2.7-1.21 3.42-1.6 9.3-4.21 8.32-6.17-.86-1.76-4.53-2.09-7.33-.97-2.84 1.13-3.58 2.24-3.46 ********** 4.75-.69 8.34-4.4 3.4-3.54-.96-4.7-3.9-4.06-2.26.5-6.4 2.06-6.16 ********* 5.33-.54 9.1-4.87 1.14-1.32 2.02-2.51 1.95-3.45-.16-2.03-3.3-3.08-7.42-3.15-8.83-.16-11.56 4.78-11.16 5.1 1.1.92 9.36-4.15 14.46-8.67 1.94-1.73 2.71-3.62.56-4.44-4.4-1.72-13.75-.37-19.42 4.05-9.04 7.07-12 18.44-9.06 27.03 2.94 8.6 12.28 11.3 18 9.61 5.73-1.68 5.4-6.53 4.48-9.81Z",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M106.48 120.15c-.2 1.56-.53 3.98-1.2 6.51-.68 2.53-1.71 5.16-3.3 7.14l.2.19 5.67-4.97-1.1-8.87h-.27Zm31.09 45.32-.25-.1-11.3 21.27h11.92l-.43-.26c-3.5-2.07-4.07-5.79-3.46-9.77.52-3.4 1.9-6.95 3-9.77l.52-1.37Zm-85.81-37.9-.24.13a55.3 55.3 0 0 0 1.65 6.39l.01.04.05.03c1.63.84 4.4 1.85 7.45 2.14 3.05.3 6.39-.12 9.1-2.15l-.12-.24c-3.14 1.2-11.14 1.63-17.9-6.33Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "m133.54 82.83.02-.16h-.16c-1.82.1-5.84.39-9.87.96-2.02.28-4.04.64-5.79 1.08-1.74.43-3.23.95-4.18 1.58l-.06.04v.07c-.04 1.8.13 3.69.65 5.35a7.28 7.28 0 0 0 2.8 4.05c2.81 1.86 6.69 1.62 9.98-.58 3.3-2.2 6.04-6.37 6.61-12.4Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    d: "M126.66 84.73a9.79 9.79 0 0 1-9.7 9.87 9.79 9.79 0 0 1-9.7-9.87 9.79 9.79 0 0 1 9.7-9.87c5.35 0 9.7 4.41 9.7 9.87Z",
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M109.41 70.6c.66.25 1.25.47 *********-.18.83-.7 1.18-1.3.75-1.27 1.73-2.93 5.56-2.56 3.86.38 4.6 2.17 5.1 *********.38.9.69 **********.84.33 **********.02 1.2.05 2.37.75 1.41.85 2.49 5.65.23 9.93l.26.43c-.45 2.15-3.05 4.37-3.05 4.14 0-3.04 0-4.75-2.92-7.43-4.26 1.6-10.89 1.13-13.94.14-4-1.28-5.98-6.81-2.53-9 1.72-1.1 2.88-.65 3.9-.26Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M116.7 81.6a.41.41 0 1 0-.74.4c.**********.*********.93.25 1.58.17a.41.41 0 1 0-.1-.82c-.58.07-.9.01-1.12-.09-.2-.1-.36-.28-.53-.6Zm-6.57 4.22a1.07 1.07 0 0 0 1-1.13 1.06 1.06 0 0 0-1.1-1.02c-.59.03-1.03.54-1 *********.52 1.05 1.1 1.02Zm6.82.73a1.07 1.07 0 0 0 1-1.13 1.06 1.06 0 0 0-1.1-1.01c-.58.02-1.03.53-1 *********.52 1.05 1.1 1.02Z",
    fill: "#515151"
  }), /* @__PURE__ */ React.createElement("ellipse", {
    cx: 127.42,
    cy: 86.0081,
    rx: 2.47934,
    ry: 2.47934,
    fill: "white",
    stroke: "#515151"
  }), /* @__PURE__ */ React.createElement("circle", {
    r: 4.67102,
    transform: "matrix(0.707106 0.707108 -0.707106 0.707108 75.0508 75.7794)",
    fill: "var(--semi-color-primary)",
    stroke: "var(--semi-color-primary)",
    strokeWidth: 3
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M77.9 72.97a1.1 1.1 0 0 0-1.57 0l-1.26 1.27-1.31-1.3a1.1 1.1 0 1 0-1.57 1.56l1.31 1.3-1.3 1.31a1.1 1.1 0 0 0 1.56 1.57l1.3-1.3 1.27 1.26a1.1 1.1 0 1 0 1.57-1.57l-1.26-1.26 1.26-1.27a1.1 1.1 0 0 0 0-1.57Z",
    fill: "white"
  }), /* @__PURE__ */ React.createElement("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M99.41 49.11a32.26 32.26 0 0 0-19-9.84 2.14 2.14 0 0 0-2.42 2.02c-.07 1.2.82 2.2 1.97 2.37a27.86 27.86 0 0 1 16.13 8.36c.83.87 2.24.99 **********-.79.99-2.17.15-3.06Zm-35.8-8.52a32.5 32.5 0 0 0-18.74 15.73 2.14 2.14 0 0 0 1.01 2.93c1.11.52 2.4.02 2.96-1.02a28.08 28.08 0 0 1 15.96-13.4 2.28 2.28 0 0 0 1.53-2.72 2.14 2.14 0 0 0-2.72-1.52Zm27.75 15.78c-3.02-3.14-7-5.38-11.53-6.22a2.11 2.11 0 0 0-2.47 2 2.33 2.33 0 0 0 1.9 2.38c3.41.69 6.43 2.37 8.75 ********** 2.26 1 *********-.78 1-2.15.15-3.03Zm-24.64-4.65a21.63 21.63 0 0 0-11.66 9.8 2.1 2.1 0 0 0 .99 2.94c1.12.52 2.4.01 2.98-1 2-3.45 5.17-6.11 8.91-7.5 1.1-.4 1.81-1.56 1.5-2.75a2.1 2.1 0 0 0-2.72-1.5Zm16.63 11.95a10.69 10.69 0 0 0-4-2.47c-1.35-.47-2.56.62-2.63 1.9-.07 1.14.73 2.04 1.65 ********** 1.07.56 **********.74 2.33 1 **********-.74 1-2.07.14-2.92Zm-14.28-.76c-1.82.83-3.39 2.16-4.5 3.83-.73 1.08-.16 2.42.9 2.92 1.14.53 2.38-.02 3.05-.88a6.35 6.35 0 0 1 1.93-1.63c.95-.52 1.68-1.63 1.37-2.83-.3-1.15-1.54-1.95-2.75-1.4Z",
    fill: "var(--semi-color-primary)"
  }));
}
var IllustrationNotFound_default = SvgComponent;
export {
  IllustrationNotFound_default as default
};
