!function(y,k,S){"use strict";k=void 0!==k&&k.Math==Math?k:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),y.fn.nag=function(d){var u,e=y(this),m=e.selector||"",f=(new Date).getTime(),p=[],h=d,b="string"==typeof h,v=[].slice.call(arguments,1);return e.each(function(){var n=y.isPlainObject(d)?y.extend(!0,{},y.fn.nag.settings,d):y.extend({},y.fn.nag.settings),e=(n.className,n.selector),r=n.error,o=n.namespace,t="."+o,i=o+"-module",s=y(this),a=(s.find(e.close),n.context?y(n.context):y("body")),l=this,c=s.data(i),g=(k.requestAnimationFrame||k.mozRequestAnimationFrame||k.webkitRequestAnimationFrame||k.msRequestAnimationFrame,{initialize:function(){g.verbose("Initializing element"),s.on("click"+t,e.close,g.dismiss).data(i,g),n.detachable&&s.parent()[0]!==a[0]&&s.detach().prependTo(a),0<n.displayTime&&setTimeout(g.hide,n.displayTime),g.show()},destroy:function(){g.verbose("Destroying instance"),s.removeData(i).off(t)},show:function(){g.should.show()&&!s.is(":visible")&&(g.debug("Showing nag",n.animation.show),"fade"==n.animation.show?s.fadeIn(n.duration,n.easing):s.slideDown(n.duration,n.easing))},hide:function(){g.debug("Showing nag",n.animation.hide),"fade"==n.animation.show?s.fadeIn(n.duration,n.easing):s.slideUp(n.duration,n.easing)},onHide:function(){g.debug("Removing nag",n.animation.hide),s.remove(),n.onHide&&n.onHide()},dismiss:function(e){n.storageMethod&&g.storage.set(n.key,n.value),g.hide(),e.stopImmediatePropagation(),e.preventDefault()},should:{show:function(){return n.persist?(g.debug("Persistent nag is set, can show nag"),!0):g.storage.get(n.key)!=n.value.toString()?(g.debug("Stored value is not set, can show nag",g.storage.get(n.key)),!0):(g.debug("Stored value is set, cannot show nag",g.storage.get(n.key)),!1)}},get:{storageOptions:function(){var e={};return n.expires&&(e.expires=n.expires),n.domain&&(e.domain=n.domain),n.path&&(e.path=n.path),e}},clear:function(){g.storage.remove(n.key)},storage:{set:function(e,o){var t=g.get.storageOptions();if("localstorage"==n.storageMethod&&k.localStorage!==S)k.localStorage.setItem(e,o),g.debug("Value stored using local storage",e,o);else if("sessionstorage"==n.storageMethod&&k.sessionStorage!==S)k.sessionStorage.setItem(e,o),g.debug("Value stored using session storage",e,o);else{if(y.cookie===S)return void g.error(r.noCookieStorage);y.cookie(e,o,t),g.debug("Value stored using cookie",e,o,t)}},get:function(e,o){var t;return"localstorage"==n.storageMethod&&k.localStorage!==S?t=k.localStorage.getItem(e):"sessionstorage"==n.storageMethod&&k.sessionStorage!==S?t=k.sessionStorage.getItem(e):y.cookie!==S?t=y.cookie(e):g.error(r.noCookieStorage),"undefined"!=t&&"null"!=t&&t!==S&&null!==t||(t=S),t},remove:function(e){var o=g.get.storageOptions();"localstorage"==n.storageMethod&&k.localStorage!==S?k.localStorage.removeItem(e):"sessionstorage"==n.storageMethod&&k.sessionStorage!==S?k.sessionStorage.removeItem(e):y.cookie!==S?y.removeCookie(e,o):g.error(r.noStorage)}},setting:function(e,o){if(g.debug("Changing setting",e,o),y.isPlainObject(e))y.extend(!0,n,e);else{if(o===S)return n[e];y.isPlainObject(n[e])?y.extend(!0,n[e],o):n[e]=o}},internal:function(e,o){if(y.isPlainObject(e))y.extend(!0,g,e);else{if(o===S)return g[e];g[e]=o}},debug:function(){!n.silent&&n.debug&&(n.performance?g.performance.log(arguments):(g.debug=Function.prototype.bind.call(console.info,console,n.name+":"),g.debug.apply(console,arguments)))},verbose:function(){!n.silent&&n.verbose&&n.debug&&(n.performance?g.performance.log(arguments):(g.verbose=Function.prototype.bind.call(console.info,console,n.name+":"),g.verbose.apply(console,arguments)))},error:function(){n.silent||(g.error=Function.prototype.bind.call(console.error,console,n.name+":"),g.error.apply(console,arguments))},performance:{log:function(e){var o,t;n.performance&&(t=(o=(new Date).getTime())-(f||o),f=o,p.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:l,"Execution Time":t})),clearTimeout(g.performance.timer),g.performance.timer=setTimeout(g.performance.display,500)},display:function(){var e=n.name+":",t=0;f=!1,clearTimeout(g.performance.timer),y.each(p,function(e,o){t+=o["Execution Time"]}),e+=" "+t+"ms",m&&(e+=" '"+m+"'"),(console.group!==S||console.table!==S)&&0<p.length&&(console.groupCollapsed(e),console.table?console.table(p):y.each(p,function(e,o){console.log(o.Name+": "+o["Execution Time"]+"ms")}),console.groupEnd()),p=[]}},invoke:function(n,e,o){var i,s,t,a=c;return e=e||v,o=l||o,"string"==typeof n&&a!==S&&(n=n.split(/[\. ]/),i=n.length-1,y.each(n,function(e,o){var t=e!=i?o+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(y.isPlainObject(a[t])&&e!=i)a=a[t];else{if(a[t]!==S)return s=a[t],!1;if(!y.isPlainObject(a[o])||e==i)return a[o]!==S?s=a[o]:g.error(r.method,n),!1;a=a[o]}})),y.isFunction(s)?t=s.apply(o,e):s!==S&&(t=s),y.isArray(u)?u.push(t):u!==S?u=[u,t]:t!==S&&(u=t),s}});b?(c===S&&g.initialize(),g.invoke(h)):(c!==S&&c.invoke("destroy"),g.initialize())}),u!==S?u:this},y.fn.nag.settings={name:"Nag",silent:!1,debug:!1,verbose:!1,performance:!0,namespace:"Nag",persist:!1,displayTime:0,animation:{show:"slide",hide:"slide"},context:!1,detachable:!1,expires:30,domain:!1,path:"/",storageMethod:"cookie",key:"nag",value:"dismiss",error:{noCookieStorage:"$.cookie is not included. A storage solution is required.",noStorage:"Neither $.cookie or store is defined. A storage solution is required for storing state",method:"The method you called is not defined."},className:{bottom:"bottom",fixed:"fixed"},selector:{close:".close.icon"},speed:500,easing:"easeOutQuad",onHide:function(){}},y.extend(y.easing,{easeOutQuad:function(e,o,t,n,i){return-n*(o/=i)*(o-2)+t}})}(jQuery,window,void document);