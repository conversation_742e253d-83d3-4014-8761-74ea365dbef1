!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).VDataset={})}(this,(function(t){"use strict";var n=Math.PI/3,r=[0,n,2*n,3*n,4*n,5*n];function e(t){return t[0]}function i(t){return t[1]}function o(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function a(t){if(t.__esModule)return t;var n=t.default;if("function"==typeof n){var r=function t(){if(this instanceof t){var r=[null];return r.push.apply(r,arguments),new(Function.bind.apply(n,r))}return n.apply(this,arguments)};r.prototype=n.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach((function(n){var e=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(r,n,e.get?e:{enumerable:!0,get:function(){return t[n]}})})),r}var s={exports:{}};!function(t){var n=Object.prototype.hasOwnProperty,r="~";function e(){}function i(t,n,r){this.fn=t,this.context=n,this.once=r||!1}function o(t,n,e,o,a){if("function"!=typeof e)throw new TypeError("The listener must be a function");var s=new i(e,o||t,a),u=r?r+n:n;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],s]:t._events[u].push(s):(t._events[u]=s,t._eventsCount++),t}function a(t,n){0==--t._eventsCount?t._events=new e:delete t._events[n]}function s(){this._events=new e,this._eventsCount=0}Object.create&&(e.prototype=Object.create(null),(new e).__proto__||(r=!1)),s.prototype.eventNames=function(){var t,e,i=[];if(0===this._eventsCount)return i;for(e in t=this._events)n.call(t,e)&&i.push(r?e.slice(1):e);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},s.prototype.listeners=function(t){var n=r?r+t:t,e=this._events[n];if(!e)return[];if(e.fn)return[e.fn];for(var i=0,o=e.length,a=new Array(o);i<o;i++)a[i]=e[i].fn;return a},s.prototype.listenerCount=function(t){var n=r?r+t:t,e=this._events[n];return e?e.fn?1:e.length:0},s.prototype.emit=function(t,n,e,i,o,a){var s=r?r+t:t;if(!this._events[s])return!1;var u,l,c=this._events[s],f=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),f){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,n),!0;case 3:return c.fn.call(c.context,n,e),!0;case 4:return c.fn.call(c.context,n,e,i),!0;case 5:return c.fn.call(c.context,n,e,i,o),!0;case 6:return c.fn.call(c.context,n,e,i,o,a),!0}for(l=1,u=new Array(f-1);l<f;l++)u[l-1]=arguments[l];c.fn.apply(c.context,u)}else{var h,p=c.length;for(l=0;l<p;l++)switch(c[l].once&&this.removeListener(t,c[l].fn,void 0,!0),f){case 1:c[l].fn.call(c[l].context);break;case 2:c[l].fn.call(c[l].context,n);break;case 3:c[l].fn.call(c[l].context,n,e);break;case 4:c[l].fn.call(c[l].context,n,e,i);break;default:if(!u)for(h=1,u=new Array(f-1);h<f;h++)u[h-1]=arguments[h];c[l].fn.apply(c[l].context,u)}}return!0},s.prototype.on=function(t,n,r){return o(this,t,n,r,!1)},s.prototype.once=function(t,n,r){return o(this,t,n,r,!0)},s.prototype.removeListener=function(t,n,e,i){var o=r?r+t:t;if(!this._events[o])return this;if(!n)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==n||i&&!s.once||e&&s.context!==e||a(this,o);else{for(var u=0,l=[],c=s.length;u<c;u++)(s[u].fn!==n||i&&!s[u].once||e&&s[u].context!==e)&&l.push(s[u]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},s.prototype.removeAllListeners=function(t){var n;return t?(n=r?r+t:t,this._events[n]&&a(this,n)):(this._events=new e,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,t.exports=s}(s);var u=o(s.exports);var l=(t,n)=>Object.prototype.toString.call(t)===`[object ${n}]`;var c=function(t){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?"boolean"==typeof t:!0===t||!1===t||l(t,"Boolean")};var f=t=>"function"==typeof t;var h=t=>null==t;var p=t=>null!=t;var g=t=>{const n=typeof t;return null!==t&&"object"===n||"function"===n};var d=t=>"object"==typeof t&&null!==t;var v=function(t){if(!d(t)||!l(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;let n=t;for(;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(t)===n};var y=function(t){const n=typeof t;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?"string"===n:"string"===n||l(t,"String")};var m=t=>Array.isArray?Array.isArray(t):l(t,"Array");var w=function(t){return null!==t&&"function"!=typeof t&&Number.isFinite(t.length)};var b=t=>l(t,"Date");var M=function(t){const n=typeof t;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?"number"===n:"number"===n||l(t,"Number")};var x=t=>M(t)&&Number.isFinite(t);function E(t,n,r){let e;if(!p(t)||"object"!=typeof t||n&&n(t))return t;const i=m(t),o=t.length;e=i?new Array(o):"object"==typeof t?{}:c(t)||M(t)||y(t)?t:b(t)?new Date(+t):void 0;const a=i?void 0:Object.keys(Object(t));let s=-1;if(e)for(;++s<(a||t).length;){const i=a?a[s]:s,o=t[i];r&&r.includes(i.toString())?e[i]=o:e[i]=E(o,n,r)}return e}function S(t,n){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(n){if(t===n)return;if(p(n)&&"object"==typeof n){const i=Object(n),o=[];for(const t in i)o.push(t);let{length:a}=o,s=-1;for(;a--;){const a=o[++s];!p(i[a])||"object"!=typeof i[a]||e&&m(t[a])?P(t,a,i[a]):_(t,n,a,r,e)}}}}function _(t,n,r){let e=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const o=t[r],a=n[r];let s=n[r],u=!0;if(m(a)){if(e)s=[];else if(m(o))s=o;else if(w(o)){s=new Array(o.length);let t=-1;const n=o.length;for(;++t<n;)s[t]=o[t]}}else v(a)?(s=null!=o?o:{},"function"!=typeof o&&"object"==typeof o||(s={})):u=!1;u&&S(s,a,e,i),P(t,r,s)}function P(t,n,r){(void 0!==r&&!function(t,n){return t===n||Number.isNaN(t)&&Number.isNaN(n)}(t[n],r)||void 0===r&&!(n in t))&&(t[n]=r)}function k(t){let n=-1;const r=arguments.length<=1?0:arguments.length-1;for(;++n<r;)S(t,n+1<1||arguments.length<=n+1?void 0:arguments[n+1],!0);return t}function A(t){return t&&m(t)?Array.from(new Set(function(t){return p(t)?m(t)?t:[t]:[]}(t))):t}function F(t){if(!m(t))return[t];const n=[];for(const r of t)n.push(...F(r));return n}const D="undefined"!=typeof console;function C(t,n,r){const e=[n].concat([].slice.call(r));D&&console[t].apply(console,e)}var L;!function(t){t[t.None=0]="None",t[t.Error=1]="Error",t[t.Warn=2]="Warn",t[t.Info=3]="Info",t[t.Debug=4]="Debug"}(L||(L={}));class N{static getInstance(t,n){return N._instance&&M(t)?N._instance.level(t):N._instance||(N._instance=new N(t,n)),N._instance}static setInstance(t){return N._instance=t}static setInstanceLevel(t){N._instance?N._instance.level(t):N._instance=new N(t)}static clearInstance(){N._instance=null}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:L.None,n=arguments.length>1?arguments[1]:void 0;this._onErrorHandler=[],this._level=t,this._method=n}addErrorHandler(t){this._onErrorHandler.find((n=>n===t))||this._onErrorHandler.push(t)}removeErrorHandler(t){const n=this._onErrorHandler.findIndex((n=>n===t));n<0||this._onErrorHandler.splice(n,1)}callErrorHandler(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];this._onErrorHandler.forEach((t=>t(...n)))}canLogInfo(){return this._level>=L.Info}canLogDebug(){return this._level>=L.Debug}canLogError(){return this._level>=L.Error}canLogWarn(){return this._level>=L.Warn}level(t){return arguments.length?(this._level=+t,this):this._level}error(){for(var t,n=arguments.length,r=new Array(n),e=0;e<n;e++)r[e]=arguments[e];return this._level>=L.Error&&(this._onErrorHandler.length?this.callErrorHandler(...r):C(null!==(t=this._method)&&void 0!==t?t:"error","ERROR",r)),this}warn(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this._level>=L.Warn&&C(this._method||"warn","WARN",n),this}info(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this._level>=L.Info&&C(this._method||"log","INFO",n),this}debug(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this._level>=L.Debug&&C(this._method||"log","DEBUG",n),this}}N._instance=null;var O=function(t,n,r){return t<n?n:t>r?r:t};class j{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;this.a=t,this.b=n,this.c=r,this.d=e,this.e=i,this.f=o}equalToMatrix(t){return!(this.e!==t.e||this.f!==t.f||this.a!==t.a||this.d!==t.d||this.b!==t.b||this.c!==t.c)}equalTo(t,n,r,e,i,o){return!(this.e!==i||this.f!==o||this.a!==t||this.d!==e||this.b!==n||this.c!==r)}setValue(t,n,r,e,i,o){return this.a=t,this.b=n,this.c=r,this.d=e,this.e=i,this.f=o,this}reset(){return this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0,this}getInverse(){const t=this.a,n=this.b,r=this.c,e=this.d,i=this.e,o=this.f,a=new j,s=t*e-n*r;return a.a=e/s,a.b=-n/s,a.c=-r/s,a.d=t/s,a.e=(r*o-e*i)/s,a.f=-(t*o-n*i)/s,a}rotate(t){const n=Math.cos(t),r=Math.sin(t),e=this.a*n+this.c*r,i=this.b*n+this.d*r,o=this.a*-r+this.c*n,a=this.b*-r+this.d*n;return this.a=e,this.b=i,this.c=o,this.d=a,this}rotateByCenter(t,n,r){const e=Math.cos(t),i=Math.sin(t),o=(1-e)*n+i*r,a=(1-e)*r-i*n,s=e*this.a-i*this.b,u=i*this.a+e*this.b,l=e*this.c-i*this.d,c=i*this.c+e*this.d,f=e*this.e-i*this.f+o,h=i*this.e+e*this.f+a;return this.a=s,this.b=u,this.c=l,this.d=c,this.e=f,this.f=h,this}scale(t,n){return this.a*=t,this.b*=t,this.c*=n,this.d*=n,this}setScale(t,n){return this.b=this.b/this.a*t,this.c=this.c/this.d*n,this.a=t,this.d=n,this}transform(t,n,r,e,i,o){return this.multiply(t,n,r,e,i,o),this}translate(t,n){return this.e+=this.a*t+this.c*n,this.f+=this.b*t+this.d*n,this}transpose(){const{a:t,b:n,c:r,d:e,e:i,f:o}=this;return this.a=n,this.b=t,this.c=e,this.d=r,this.e=o,this.f=i,this}multiply(t,n,r,e,i,o){const a=this.a,s=this.b,u=this.c,l=this.d,c=a*t+u*n,f=s*t+l*n,h=a*r+u*e,p=s*r+l*e,g=a*i+u*o+this.e,d=s*i+l*o+this.f;return this.a=c,this.b=f,this.c=h,this.d=p,this.e=g,this.f=d,this}interpolate(t,n){const r=new j;return r.a=this.a+(t.a-this.a)*n,r.b=this.b+(t.b-this.b)*n,r.c=this.c+(t.c-this.c)*n,r.d=this.d+(t.d-this.d)*n,r.e=this.e+(t.e-this.e)*n,r.f=this.f+(t.f-this.f)*n,r}transformPoint(t,n){const{a:r,b:e,c:i,d:o,e:a,f:s}=this,u=r*o-e*i,l=o/u,c=-e/u,f=-i/u,h=r/u,p=(i*s-o*a)/u,g=-(r*s-e*a)/u,{x:d,y:v}=t;n.x=d*l+v*f+p,n.y=d*c+v*h+g}onlyTranslate(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.a===t&&0===this.b&&0===this.c&&this.d===t}clone(){return new j(this.a,this.b,this.c,this.d,this.e,this.f)}toTransformAttrs(){const t=this.a,n=this.b,r=this.c,e=this.d,i=t*e-n*r,o={x:this.e,y:this.f,rotateDeg:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(0!==t||0!==n){const a=Math.sqrt(t*t+n*n);o.rotateDeg=n>0?Math.acos(t/a):-Math.acos(t/a),o.scaleX=a,o.scaleY=i/a,o.skewX=(t*r+n*e)/i,o.skewY=0}else if(0!==r||0!==e){const a=Math.sqrt(r*r+e*e);o.rotateDeg=Math.PI/2-(e>0?Math.acos(-r/a):-Math.acos(r/a)),o.scaleX=i/a,o.scaleY=a,o.skewX=0,o.skewY=(t*r+n*e)/i}return o.rotateDeg=180*o.rotateDeg/Math.PI,o}}function V(t,n,r){n/=100,r/=100;const e=(1-Math.abs(2*r-1))*n,i=e*(1-Math.abs(t/60%2-1)),o=r-e/2;let a=0,s=0,u=0;return 0<=t&&t<60?(a=e,s=i,u=0):60<=t&&t<120?(a=i,s=e,u=0):120<=t&&t<180?(a=0,s=e,u=i):180<=t&&t<240?(a=0,s=i,u=e):240<=t&&t<300?(a=i,s=0,u=e):300<=t&&t<360&&(a=e,s=0,u=i),a=Math.round(255*(a+o)),s=Math.round(255*(s+o)),u=Math.round(255*(u+o)),{r:a,g:s,b:u}}function T(t,n,r){t/=255,n/=255,r/=255;const e=Math.min(t,n,r),i=Math.max(t,n,r),o=i-e;let a=0,s=0,u=0;return a=0===o?0:i===t?(n-r)/o%6:i===n?(r-t)/o+2:(t-n)/o+4,a=Math.round(60*a),a<0&&(a+=360),u=(i+e)/2,s=0===o?0:o/(1-Math.abs(2*u-1)),s=+(100*s).toFixed(1),u=+(100*u).toFixed(1),{h:a,s:s,l:u}}const R=/^#([0-9a-f]{3,8})$/,q={transparent:4294967040},B={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function G(t){return((t=Math.max(0,Math.min(255,Math.round(t)||0)))<16?"0":"")+t.toString(16)}function I(t){return M(t)?new W(t>>16,t>>8&255,255&t,1):m(t)?new W(t[0],t[1],t[2]):new W(255,255,255)}function H(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function U(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}const $=(t,n)=>{const r=R.exec(t);if(n||r){const t=parseInt(r[1],16),n=r[1].length;return 3===n?new W((t>>8&15)+((t>>8&15)<<4),(t>>4&15)+((t>>4&15)<<4),(15&t)+((15&t)<<4),1):6===n?I(t):8===n?new W(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):null}};class z{static Brighter(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return 1===n?t:new z(t).brighter(n).toRGBA()}static SetOpacity(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return 1===n?t:new z(t).setOpacity(n).toRGBA()}static getColorBrightness(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"hsl";const r=t instanceof z?t:new z(t);switch(n){case"hsv":default:return r.getHSVBrightness();case"hsl":return r.getHSLBrightness();case"lum":return r.getLuminance();case"lum2":return r.getLuminance2();case"lum3":return r.getLuminance3();case"wcag":return r.getLuminanceWCAG()}}static parseColorString(t){if(p(q[t]))return function(t){return M(t)?new W(t>>>24,t>>>16&255,t>>>8&255,255&t):m(t)?new W(t[0],t[1],t[2],t[3]):new W(255,255,255,1)}(q[t]);if(p(B[t]))return I(B[t]);const n=`${t}`.trim().toLowerCase(),r=$(n);if(void 0!==r)return r;if(/^(rgb|RGB|rgba|RGBA)/.test(n)){const t=n.replace(/(?:\(|\)|rgba|RGBA|rgb|RGB)*/g,"").split(",");return new W(parseInt(t[0],10),parseInt(t[1],10),parseInt(t[2],10),parseFloat(t[3]))}if(/^(hsl|HSL|hsla|HSLA)/.test(n)){const t=n.replace(/(?:\(|\)|hsla|HSLA|hsl|HSL)*/g,"").split(","),r=V(parseInt(t[0],10),parseInt(t[1],10),parseInt(t[2],10));return new W(r.r,r.g,r.b,parseFloat(t[3]))}}constructor(t){const n=z.parseColorString(t);n?this.color=n:(console.warn(`Warn: 传入${t}无法解析为Color`),this.color=new W(255,255,255))}toRGBA(){return this.color.formatRgb()}toString(){return this.color.formatRgb()}toHex(){return this.color.formatHex()}toHsl(){return this.color.formatHsl()}brighter(t){const{r:n,g:r,b:e}=this.color;return this.color.r=Math.max(0,Math.min(255,Math.floor(n*t))),this.color.g=Math.max(0,Math.min(255,Math.floor(r*t))),this.color.b=Math.max(0,Math.min(255,Math.floor(e*t))),this}add(t){const{r:n,g:r,b:e}=this.color;return this.color.r+=Math.min(255,n+t.color.r),this.color.g+=Math.min(255,r+t.color.g),this.color.b+=Math.min(255,e+t.color.b),this}sub(t){return this.color.r=Math.max(0,this.color.r-t.color.r),this.color.g=Math.max(0,this.color.g-t.color.g),this.color.b=Math.max(0,this.color.b-t.color.b),this}multiply(t){const{r:n,g:r,b:e}=this.color;return this.color.r=Math.max(0,Math.min(255,Math.floor(n*t.color.r))),this.color.g=Math.max(0,Math.min(255,Math.floor(r*t.color.g))),this.color.b=Math.max(0,Math.min(255,Math.floor(e*t.color.b))),this}getHSVBrightness(){return Math.max(this.color.r,this.color.g,this.color.b)/255}getHSLBrightness(){return.5*(Math.max(this.color.r,this.color.g,this.color.b)/255+Math.min(this.color.r,this.color.g,this.color.b)/255)}setHsl(t,n,r){const e=this.color.opacity,i=T(this.color.r,this.color.g,this.color.b),o=V(h(t)?i.h:O(t,0,360),h(n)?i.s:n>=0&&n<=1?100*n:n,h(r)?i.l:r<=1&&r>=0?100*r:r);return this.color=new W(o.r,o.g,o.b,e),this}setRGB(t,n,r){return!h(t)&&(this.color.r=t),!h(n)&&(this.color.g=n),!h(r)&&(this.color.b=r),this}setHex(t){const n=`${t}`.trim().toLowerCase(),r=$(n,!0);return null!=r?r:this}setColorName(t){const n=B[t.toLowerCase()];return void 0!==n?this.setHex(n):console.warn("THREE.Color: Unknown color "+t),this}setScalar(t){return this.color.r=t,this.color.g=t,this.color.b=t,this}setOpacity(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.color.opacity=t,this}getLuminance(){return(.2126*this.color.r+.7152*this.color.g+.0722*this.color.b)/255}getLuminance2(){return(.2627*this.color.r+.678*this.color.g+.0593*this.color.b)/255}getLuminance3(){return(.299*this.color.r+.587*this.color.g+.114*this.color.b)/255}getLuminanceWCAG(){const t=this.color.r/255,n=this.color.g/255,r=this.color.b/255;let e,i,o;return e=t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4),i=n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4),o=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4),.2126*e+.7152*i+.0722*o}clone(){return new z(this.color.toString())}copyGammaToLinear(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return this.color.r=Math.pow(t.color.r,n),this.color.g=Math.pow(t.color.g,n),this.color.b=Math.pow(t.color.b,n),this}copyLinearToGamma(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;const r=n>0?1/n:1;return this.color.r=Math.pow(t.color.r,r),this.color.g=Math.pow(t.color.g,r),this.color.b=Math.pow(t.color.b,r),this}convertGammaToLinear(t){return this.copyGammaToLinear(this,t),this}convertLinearToGamma(t){return this.copyLinearToGamma(this,t),this}copySRGBToLinear(t){return this.color.r=H(t.color.r),this.color.g=H(t.color.g),this.color.b=H(t.color.b),this}copyLinearToSRGB(t){return this.color.r=U(t.color.r),this.color.g=U(t.color.g),this.color.b=U(t.color.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}}class W{constructor(t,n,r,e){this.r=isNaN(+t)?255:Math.max(0,Math.min(255,+t)),this.g=isNaN(+n)?255:Math.max(0,Math.min(255,+n)),this.b=isNaN(+r)?255:Math.max(0,Math.min(255,+r)),p(e)?this.opacity=isNaN(+e)?1:Math.max(0,Math.min(1,+e)):this.opacity=1}formatHex(){return`#${G(this.r)+G(this.g)+G(this.b)+(1===this.opacity?"":G(255*this.opacity))}`}formatRgb(){const t=this.opacity;return`${1===t?"rgb(":"rgba("}${this.r},${this.g},${this.b}${1===t?")":`,${t})`}`}formatHsl(){const t=this.opacity,{h:n,s:r,l:e}=T(this.r,this.g,this.b);return`${1===t?"hsl(":"hsla("}${n},${r}%,${e}%${1===t?")":`,${t})`}`}toString(){return this.formatHex()}}function J(t){return t.replace(/-([a-z])/g,((t,n)=>n.toUpperCase()))}function X(t,n,r){void 0===r&&(r={});var e={type:"Feature"};return(0===r.id||r.id)&&(e.id=r.id),r.bbox&&(e.bbox=r.bbox),e.properties=n||{},e.geometry=t,e}function Y(t,n){void 0===n&&(n={});var r={type:"FeatureCollection"};return n.id&&(r.id=n.id),n.bbox&&(r.bbox=n.bbox),r.features=t,r}function K(t){let n;return/^(rgba|RGBA)/.test(t)&&(n=function(t){const n=t.replace(/(?:\(|\)|rgba|RGBA)*/g,"").split(",");return{r:Number(n[0]),g:Number(n[1]),b:Number(n[2]),a:Number(n[3])}}(t)),{color:new z(t),transparent:!!n,opacity:n?n.a:1}}function Z(){return new Q}function Q(){this.reset()}Q.prototype={constructor:Q,reset:function(){this.s=this.t=0},add:function(t){nt(tt,t,this.t),nt(this,tt.s,this.s),this.s?this.t+=tt.t:this.s=tt.t},valueOf:function(){return this.s}};var tt=new Q;function nt(t,n,r){var e=t.s=n+r,i=e-n,o=e-i;t.t=n-o+(r-i)}var rt=1e-6,et=Math.PI,it=et/2,ot=et/4,at=2*et,st=180/et,ut=et/180,lt=Math.abs,ct=Math.atan,ft=Math.atan2,ht=Math.cos,pt=Math.exp,gt=Math.log,dt=Math.pow,vt=Math.sin,yt=Math.sign||function(t){return t>0?1:t<0?-1:0},mt=Math.sqrt,wt=Math.tan;function bt(t){return t>1?0:t<-1?et:Math.acos(t)}function Mt(t){return t>1?it:t<-1?-it:Math.asin(t)}function xt(){}function Et(t,n){t&&_t.hasOwnProperty(t.type)&&_t[t.type](t,n)}var St={Feature:function(t,n){Et(t.geometry,n)},FeatureCollection:function(t,n){for(var r=t.features,e=-1,i=r.length;++e<i;)Et(r[e].geometry,n)}},_t={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var r=t.coordinates,e=-1,i=r.length;++e<i;)t=r[e],n.point(t[0],t[1],t[2])},LineString:function(t,n){Pt(t.coordinates,n,0)},MultiLineString:function(t,n){for(var r=t.coordinates,e=-1,i=r.length;++e<i;)Pt(r[e],n,0)},Polygon:function(t,n){kt(t.coordinates,n)},MultiPolygon:function(t,n){for(var r=t.coordinates,e=-1,i=r.length;++e<i;)kt(r[e],n)},GeometryCollection:function(t,n){for(var r=t.geometries,e=-1,i=r.length;++e<i;)Et(r[e],n)}};function Pt(t,n,r){var e,i=-1,o=t.length-r;for(n.lineStart();++i<o;)e=t[i],n.point(e[0],e[1],e[2]);n.lineEnd()}function kt(t,n){var r=-1,e=t.length;for(n.polygonStart();++r<e;)Pt(t[r],n,1);n.polygonEnd()}function At(t,n){t&&St.hasOwnProperty(t.type)?St[t.type](t,n):Et(t,n)}function Ft(t){return[ft(t[1],t[0]),Mt(t[2])]}function Dt(t){var n=t[0],r=t[1],e=ht(r);return[e*ht(n),e*vt(n),vt(r)]}function Ct(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function Lt(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function Nt(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function Ot(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function jt(t){var n=mt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}function Vt(t,n){function r(r,e){return r=t(r,e),n(r[0],r[1])}return t.invert&&n.invert&&(r.invert=function(r,e){return(r=n.invert(r,e))&&t.invert(r[0],r[1])}),r}function Tt(t,n){return[lt(t)>et?t+Math.round(-t/at)*at:t,n]}function Rt(t,n,r){return(t%=at)?n||r?Vt(Bt(t),Gt(n,r)):Bt(t):n||r?Gt(n,r):Tt}function qt(t){return function(n,r){return[(n+=t)>et?n-at:n<-et?n+at:n,r]}}function Bt(t){var n=qt(t);return n.invert=qt(-t),n}function Gt(t,n){var r=ht(t),e=vt(t),i=ht(n),o=vt(n);function a(t,n){var a=ht(n),s=ht(t)*a,u=vt(t)*a,l=vt(n),c=l*r+s*e;return[ft(u*i-c*o,s*r-l*e),Mt(c*i+u*o)]}return a.invert=function(t,n){var a=ht(n),s=ht(t)*a,u=vt(t)*a,l=vt(n),c=l*i-u*o;return[ft(u*i+l*o,s*r+c*e),Mt(c*r-s*e)]},a}function It(t,n){(n=Dt(n))[0]-=t,jt(n);var r=bt(-n[1]);return((-n[2]<0?-r:r)+at-rt)%at}function Ht(){var t,n=[];return{point:function(n,r,e){t.push([n,r,e])},lineStart:function(){n.push(t=[])},lineEnd:xt,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var r=n;return n=[],t=null,r}}}function Ut(t,n){return lt(t[0]-n[0])<rt&&lt(t[1]-n[1])<rt}function $t(t,n,r,e){this.x=t,this.z=n,this.o=r,this.e=e,this.v=!1,this.n=this.p=null}function zt(t,n,r,e,i){var o,a,s=[],u=[];if(t.forEach((function(t){if(!((n=t.length-1)<=0)){var n,r,e=t[0],a=t[n];if(Ut(e,a)){if(!e[2]&&!a[2]){for(i.lineStart(),o=0;o<n;++o)i.point((e=t[o])[0],e[1]);return void i.lineEnd()}a[0]+=2*rt}s.push(r=new $t(e,t,null,!0)),u.push(r.o=new $t(e,null,r,!1)),s.push(r=new $t(a,t,null,!1)),u.push(r.o=new $t(a,null,r,!0))}})),s.length){for(u.sort(n),Wt(s),Wt(u),o=0,a=u.length;o<a;++o)u[o].e=r=!r;for(var l,c,f=s[0];;){for(var h=f,p=!0;h.v;)if((h=h.n)===f)return;l=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(p)for(o=0,a=l.length;o<a;++o)i.point((c=l[o])[0],c[1]);else e(h.x,h.n.x,1,i);h=h.n}else{if(p)for(l=h.p.z,o=l.length-1;o>=0;--o)i.point((c=l[o])[0],c[1]);else e(h.x,h.p.x,-1,i);h=h.p}l=(h=h.o).z,p=!p}while(!h.v);i.lineEnd()}}}function Wt(t){if(n=t.length){for(var n,r,e=0,i=t[0];++e<n;)i.n=r=t[e],r.p=i,i=r;i.n=r=t[0],r.p=i}}Tt.invert=Tt;var Jt=Z();function Xt(t){return lt(t[0])<=et?t[0]:yt(t[0])*((lt(t[0])+et)%at-et)}function Yt(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function Kt(t){for(var n,r,e,i=t.length,o=-1,a=0;++o<i;)a+=t[o].length;for(r=new Array(a);--i>=0;)for(n=(e=t[i]).length;--n>=0;)r[--a]=e[n];return r}function Zt(t,n,r,e){return function(i){var o,a,s,u=n(i),l=Ht(),c=n(l),f=!1,h={point:p,lineStart:d,lineEnd:v,polygonStart:function(){h.point=y,h.lineStart=m,h.lineEnd=w,a=[],o=[]},polygonEnd:function(){h.point=p,h.lineStart=d,h.lineEnd=v,a=Kt(a);var t=function(t,n){var r=Xt(n),e=n[1],i=vt(e),o=[vt(r),-ht(r),0],a=0,s=0;Jt.reset(),1===i?e=it+rt:-1===i&&(e=-it-rt);for(var u=0,l=t.length;u<l;++u)if(f=(c=t[u]).length)for(var c,f,h=c[f-1],p=Xt(h),g=h[1]/2+ot,d=vt(g),v=ht(g),y=0;y<f;++y,p=w,d=M,v=x,h=m){var m=c[y],w=Xt(m),b=m[1]/2+ot,M=vt(b),x=ht(b),E=w-p,S=E>=0?1:-1,_=S*E,P=_>et,k=d*M;if(Jt.add(ft(k*S*vt(_),v*x+k*ht(_))),a+=P?E+S*at:E,P^p>=r^w>=r){var A=Lt(Dt(h),Dt(m));jt(A);var F=Lt(o,A);jt(F);var D=(P^E>=0?-1:1)*Mt(F[2]);(e>D||e===D&&(A[0]||A[1]))&&(s+=P^E>=0?1:-1)}}return(a<-rt||a<rt&&Jt<-rt)^1&s}(o,e);a.length?(f||(i.polygonStart(),f=!0),zt(a,tn,t,r,i)):t&&(f||(i.polygonStart(),f=!0),i.lineStart(),r(null,null,1,i),i.lineEnd()),f&&(i.polygonEnd(),f=!1),a=o=null},sphere:function(){i.polygonStart(),i.lineStart(),r(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function p(n,r){t(n,r)&&i.point(n,r)}function g(t,n){u.point(t,n)}function d(){h.point=g,u.lineStart()}function v(){h.point=p,u.lineEnd()}function y(t,n){s.push([t,n]),c.point(t,n)}function m(){c.lineStart(),s=[]}function w(){y(s[0][0],s[0][1]),c.lineEnd();var t,n,r,e,u=c.clean(),h=l.result(),p=h.length;if(s.pop(),o.push(s),s=null,p)if(1&u){if((n=(r=h[0]).length-1)>0){for(f||(i.polygonStart(),f=!0),i.lineStart(),t=0;t<n;++t)i.point((e=r[t])[0],e[1]);i.lineEnd()}}else p>1&&2&u&&h.push(h.pop().concat(h.shift())),a.push(h.filter(Qt))}return h}}function Qt(t){return t.length>1}function tn(t,n){return((t=t.x)[0]<0?t[1]-it-rt:it-t[1])-((n=n.x)[0]<0?n[1]-it-rt:it-n[1])}!function(t){var n;1===t.length&&(n=t,t=function(t,r){return Yt(n(t),r)})}(Yt);var nn=Zt((function(){return!0}),(function(t){var n,r=NaN,e=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,a){var s=o>0?et:-et,u=lt(o-r);lt(u-et)<rt?(t.point(r,e=(e+a)/2>0?it:-it),t.point(i,e),t.lineEnd(),t.lineStart(),t.point(s,e),t.point(o,e),n=0):i!==s&&u>=et&&(lt(r-i)<rt&&(r-=i*rt),lt(o-s)<rt&&(o-=s*rt),e=function(t,n,r,e){var i,o,a=vt(t-r);return lt(a)>rt?ct((vt(n)*(o=ht(e))*vt(r)-vt(e)*(i=ht(n))*vt(t))/(i*o*a)):(n+e)/2}(r,e,o,a),t.point(i,e),t.lineEnd(),t.lineStart(),t.point(s,e),n=0),t.point(r=o,e=a),i=s},lineEnd:function(){t.lineEnd(),r=e=NaN},clean:function(){return 2-n}}}),(function(t,n,r,e){var i;if(null==t)i=r*it,e.point(-et,i),e.point(0,i),e.point(et,i),e.point(et,0),e.point(et,-i),e.point(0,-i),e.point(-et,-i),e.point(-et,0),e.point(-et,i);else if(lt(t[0]-n[0])>rt){var o=t[0]<n[0]?et:-et;i=r*o/2,e.point(-o,i),e.point(0,i),e.point(o,i)}else e.point(n[0],n[1])}),[-et,-it]);function rn(t){var n=ht(t),r=6*ut,e=n>0,i=lt(n)>rt;function o(t,r){return ht(t)*ht(r)>n}function a(t,r,e){var i=[1,0,0],o=Lt(Dt(t),Dt(r)),a=Ct(o,o),s=o[0],u=a-s*s;if(!u)return!e&&t;var l=n*a/u,c=-n*s/u,f=Lt(i,o),h=Ot(i,l);Nt(h,Ot(o,c));var p=f,g=Ct(h,p),d=Ct(p,p),v=g*g-d*(Ct(h,h)-1);if(!(v<0)){var y=mt(v),m=Ot(p,(-g-y)/d);if(Nt(m,h),m=Ft(m),!e)return m;var w,b=t[0],M=r[0],x=t[1],E=r[1];M<b&&(w=b,b=M,M=w);var S=M-b,_=lt(S-et)<rt;if(!_&&E<x&&(w=x,x=E,E=w),_||S<rt?_?x+E>0^m[1]<(lt(m[0]-b)<rt?x:E):x<=m[1]&&m[1]<=E:S>et^(b<=m[0]&&m[0]<=M)){var P=Ot(p,(-g+y)/d);return Nt(P,h),[m,Ft(P)]}}}function s(n,r){var i=e?t:et-t,o=0;return n<-i?o|=1:n>i&&(o|=2),r<-i?o|=4:r>i&&(o|=8),o}return Zt(o,(function(t){var n,r,u,l,c;return{lineStart:function(){l=u=!1,c=1},point:function(f,h){var p,g=[f,h],d=o(f,h),v=e?d?0:s(f,h):d?s(f+(f<0?et:-et),h):0;if(!n&&(l=u=d)&&t.lineStart(),d!==u&&(!(p=a(n,g))||Ut(n,p)||Ut(g,p))&&(g[2]=1),d!==u)c=0,d?(t.lineStart(),p=a(g,n),t.point(p[0],p[1])):(p=a(n,g),t.point(p[0],p[1],2),t.lineEnd()),n=p;else if(i&&n&&e^d){var y;v&r||!(y=a(g,n,!0))||(c=0,e?(t.lineStart(),t.point(y[0][0],y[0][1]),t.point(y[1][0],y[1][1]),t.lineEnd()):(t.point(y[1][0],y[1][1]),t.lineEnd(),t.lineStart(),t.point(y[0][0],y[0][1],3)))}!d||n&&Ut(n,g)||t.point(g[0],g[1]),n=g,u=d,r=v},lineEnd:function(){u&&t.lineEnd(),n=null},clean:function(){return c|(l&&u)<<1}}}),(function(n,e,i,o){!function(t,n,r,e,i,o){if(r){var a=ht(n),s=vt(n),u=e*r;null==i?(i=n+e*at,o=n-u/2):(i=It(a,i),o=It(a,o),(e>0?i<o:i>o)&&(i+=e*at));for(var l,c=i;e>0?c>o:c<o;c-=u)l=Ft([a,-s*ht(c),-s*vt(c)]),t.point(l[0],l[1])}}(o,t,r,i,n,e)}),e?[0,-t]:[-et,t-et])}var en=1e9,on=-en;function an(t,n,r,e){function i(i,o){return t<=i&&i<=r&&n<=o&&o<=e}function o(i,o,s,l){var c=0,f=0;if(null==i||(c=a(i,s))!==(f=a(o,s))||u(i,o)<0^s>0)do{l.point(0===c||3===c?t:r,c>1?e:n)}while((c=(c+s+4)%4)!==f);else l.point(o[0],o[1])}function a(e,i){return lt(e[0]-t)<rt?i>0?0:3:lt(e[0]-r)<rt?i>0?2:1:lt(e[1]-n)<rt?i>0?1:0:i>0?3:2}function s(t,n){return u(t.x,n.x)}function u(t,n){var r=a(t,1),e=a(n,1);return r!==e?r-e:0===r?n[1]-t[1]:1===r?t[0]-n[0]:2===r?t[1]-n[1]:n[0]-t[0]}return function(a){var u,l,c,f,h,p,g,d,v,y,m,w=a,b=Ht(),M={point:x,lineStart:function(){M.point=E,l&&l.push(c=[]);y=!0,v=!1,g=d=NaN},lineEnd:function(){u&&(E(f,h),p&&v&&b.rejoin(),u.push(b.result()));M.point=x,v&&w.lineEnd()},polygonStart:function(){w=b,u=[],l=[],m=!0},polygonEnd:function(){var n=function(){for(var n=0,r=0,i=l.length;r<i;++r)for(var o,a,s=l[r],u=1,c=s.length,f=s[0],h=f[0],p=f[1];u<c;++u)o=h,a=p,h=(f=s[u])[0],p=f[1],a<=e?p>e&&(h-o)*(e-a)>(p-a)*(t-o)&&++n:p<=e&&(h-o)*(e-a)<(p-a)*(t-o)&&--n;return n}(),r=m&&n,i=(u=Kt(u)).length;(r||i)&&(a.polygonStart(),r&&(a.lineStart(),o(null,null,1,a),a.lineEnd()),i&&zt(u,s,n,o,a),a.polygonEnd());w=a,u=l=c=null}};function x(t,n){i(t,n)&&w.point(t,n)}function E(o,a){var s=i(o,a);if(l&&c.push([o,a]),y)f=o,h=a,p=s,y=!1,s&&(w.lineStart(),w.point(o,a));else if(s&&v)w.point(o,a);else{var u=[g=Math.max(on,Math.min(en,g)),d=Math.max(on,Math.min(en,d))],b=[o=Math.max(on,Math.min(en,o)),a=Math.max(on,Math.min(en,a))];!function(t,n,r,e,i,o){var a,s=t[0],u=t[1],l=0,c=1,f=n[0]-s,h=n[1]-u;if(a=r-s,f||!(a>0)){if(a/=f,f<0){if(a<l)return;a<c&&(c=a)}else if(f>0){if(a>c)return;a>l&&(l=a)}if(a=i-s,f||!(a<0)){if(a/=f,f<0){if(a>c)return;a>l&&(l=a)}else if(f>0){if(a<l)return;a<c&&(c=a)}if(a=e-u,h||!(a>0)){if(a/=h,h<0){if(a<l)return;a<c&&(c=a)}else if(h>0){if(a>c)return;a>l&&(l=a)}if(a=o-u,h||!(a<0)){if(a/=h,h<0){if(a>c)return;a>l&&(l=a)}else if(h>0){if(a<l)return;a<c&&(c=a)}return l>0&&(t[0]=s+l*f,t[1]=u+l*h),c<1&&(n[0]=s+c*f,n[1]=u+c*h),!0}}}}}(u,b,t,n,r,e)?s&&(w.lineStart(),w.point(o,a),m=!1):(v||(w.lineStart(),w.point(u[0],u[1])),w.point(b[0],b[1]),s||w.lineEnd(),m=!1)}g=o,d=a,v=s}return M}}function sn(t){return t}var un,ln,cn,fn,hn=Z(),pn=Z(),gn={point:xt,lineStart:xt,lineEnd:xt,polygonStart:function(){gn.lineStart=dn,gn.lineEnd=mn},polygonEnd:function(){gn.lineStart=gn.lineEnd=gn.point=xt,hn.add(lt(pn)),pn.reset()},result:function(){var t=hn/2;return hn.reset(),t}};function dn(){gn.point=vn}function vn(t,n){gn.point=yn,un=cn=t,ln=fn=n}function yn(t,n){pn.add(fn*t-cn*n),cn=t,fn=n}function mn(){yn(un,ln)}var wn=gn,bn=1/0,Mn=bn,xn=-bn,En=xn,Sn={point:function(t,n){t<bn&&(bn=t);t>xn&&(xn=t);n<Mn&&(Mn=n);n>En&&(En=n)},lineStart:xt,lineEnd:xt,polygonStart:xt,polygonEnd:xt,result:function(){var t=[[bn,Mn],[xn,En]];return xn=En=-(Mn=bn=1/0),t}};var _n,Pn,kn,An,Fn=Sn,Dn=0,Cn=0,Ln=0,Nn=0,On=0,jn=0,Vn=0,Tn=0,Rn=0,qn={point:Bn,lineStart:Gn,lineEnd:Un,polygonStart:function(){qn.lineStart=$n,qn.lineEnd=zn},polygonEnd:function(){qn.point=Bn,qn.lineStart=Gn,qn.lineEnd=Un},result:function(){var t=Rn?[Vn/Rn,Tn/Rn]:jn?[Nn/jn,On/jn]:Ln?[Dn/Ln,Cn/Ln]:[NaN,NaN];return Dn=Cn=Ln=Nn=On=jn=Vn=Tn=Rn=0,t}};function Bn(t,n){Dn+=t,Cn+=n,++Ln}function Gn(){qn.point=In}function In(t,n){qn.point=Hn,Bn(kn=t,An=n)}function Hn(t,n){var r=t-kn,e=n-An,i=mt(r*r+e*e);Nn+=i*(kn+t)/2,On+=i*(An+n)/2,jn+=i,Bn(kn=t,An=n)}function Un(){qn.point=Bn}function $n(){qn.point=Wn}function zn(){Jn(_n,Pn)}function Wn(t,n){qn.point=Jn,Bn(_n=kn=t,Pn=An=n)}function Jn(t,n){var r=t-kn,e=n-An,i=mt(r*r+e*e);Nn+=i*(kn+t)/2,On+=i*(An+n)/2,jn+=i,Vn+=(i=An*t-kn*n)*(kn+t),Tn+=i*(An+n),Rn+=3*i,Bn(kn=t,An=n)}var Xn=qn;function Yn(t){this._context=t}Yn.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,at)}},result:xt};var Kn,Zn,Qn,tr,nr,rr=Z(),er={point:xt,lineStart:function(){er.point=ir},lineEnd:function(){Kn&&or(Zn,Qn),er.point=xt},polygonStart:function(){Kn=!0},polygonEnd:function(){Kn=null},result:function(){var t=+rr;return rr.reset(),t}};function ir(t,n){er.point=or,Zn=tr=t,Qn=nr=n}function or(t,n){tr-=t,nr-=n,rr.add(mt(tr*tr+nr*nr)),tr=t,nr=n}var ar=er;function sr(){this._string=[]}function ur(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}function lr(t,n){var r,e,i=4.5;function o(t){return t&&("function"==typeof i&&e.pointRadius(+i.apply(this,arguments)),At(t,r(e))),e.result()}return o.area=function(t){return At(t,r(wn)),wn.result()},o.measure=function(t){return At(t,r(ar)),ar.result()},o.bounds=function(t){return At(t,r(Fn)),Fn.result()},o.centroid=function(t){return At(t,r(Xn)),Xn.result()},o.projection=function(n){return arguments.length?(r=null==n?(t=null,sn):(t=n).stream,o):t},o.context=function(t){return arguments.length?(e=null==t?(n=null,new sr):new Yn(n=t),"function"!=typeof i&&e.pointRadius(i),o):n},o.pointRadius=function(t){return arguments.length?(i="function"==typeof t?t:(e.pointRadius(+t),+t),o):i},o.projection(t).context(n)}function cr(t){return function(n){var r=new fr;for(var e in t)r[e]=t[e];return r.stream=n,r}}function fr(){}function hr(t,n,r){var e=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=e&&t.clipExtent(null),At(r,t.stream(Fn)),n(Fn.result()),null!=e&&t.clipExtent(e),t}function pr(t,n,r){return hr(t,(function(r){var e=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(e/(r[1][0]-r[0][0]),i/(r[1][1]-r[0][1])),a=+n[0][0]+(e-o*(r[1][0]+r[0][0]))/2,s=+n[0][1]+(i-o*(r[1][1]+r[0][1]))/2;t.scale(150*o).translate([a,s])}),r)}function gr(t,n,r){return pr(t,[[0,0],n],r)}function dr(t,n,r){return hr(t,(function(r){var e=+n,i=e/(r[1][0]-r[0][0]),o=(e-i*(r[1][0]+r[0][0]))/2,a=-i*r[0][1];t.scale(150*i).translate([o,a])}),r)}function vr(t,n,r){return hr(t,(function(r){var e=+n,i=e/(r[1][1]-r[0][1]),o=-i*r[0][0],a=(e-i*(r[1][1]+r[0][1]))/2;t.scale(150*i).translate([o,a])}),r)}sr.prototype={_radius:4.5,_circle:ur(4.5),pointRadius:function(t){return(t=+t)!==this._radius&&(this._radius=t,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._string.push("Z"),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._string.push("M",t,",",n),this._point=1;break;case 1:this._string.push("L",t,",",n);break;default:null==this._circle&&(this._circle=ur(this._radius)),this._string.push("M",t,",",n,this._circle)}},result:function(){if(this._string.length){var t=this._string.join("");return this._string=[],t}return null}},fr.prototype={constructor:fr,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var yr=16,mr=ht(30*ut);function wr(t,n){return+n?function(t,n){function r(e,i,o,a,s,u,l,c,f,h,p,g,d,v){var y=l-e,m=c-i,w=y*y+m*m;if(w>4*n&&d--){var b=a+h,M=s+p,x=u+g,E=mt(b*b+M*M+x*x),S=Mt(x/=E),_=lt(lt(x)-1)<rt||lt(o-f)<rt?(o+f)/2:ft(M,b),P=t(_,S),k=P[0],A=P[1],F=k-e,D=A-i,C=m*F-y*D;(C*C/w>n||lt((y*F+m*D)/w-.5)>.3||a*h+s*p+u*g<mr)&&(r(e,i,o,a,s,u,k,A,_,b/=E,M/=E,x,d,v),v.point(k,A),r(k,A,_,b,M,x,l,c,f,h,p,g,d,v))}}return function(n){var e,i,o,a,s,u,l,c,f,h,p,g,d={point:v,lineStart:y,lineEnd:w,polygonStart:function(){n.polygonStart(),d.lineStart=b},polygonEnd:function(){n.polygonEnd(),d.lineStart=y}};function v(r,e){r=t(r,e),n.point(r[0],r[1])}function y(){c=NaN,d.point=m,n.lineStart()}function m(e,i){var o=Dt([e,i]),a=t(e,i);r(c,f,l,h,p,g,c=a[0],f=a[1],l=e,h=o[0],p=o[1],g=o[2],yr,n),n.point(c,f)}function w(){d.point=v,n.lineEnd()}function b(){y(),d.point=M,d.lineEnd=x}function M(t,n){m(e=t,n),i=c,o=f,a=h,s=p,u=g,d.point=m}function x(){r(c,f,l,h,p,g,i,o,e,a,s,u,yr,n),d.lineEnd=w,w()}return d}}(t,n):function(t){return cr({point:function(n,r){n=t(n,r),this.stream.point(n[0],n[1])}})}(t)}var br=cr({point:function(t,n){this.stream.point(t*ut,n*ut)}});function Mr(t,n,r,e,i){function o(o,a){return[n+t*(o*=e),r-t*(a*=i)]}return o.invert=function(o,a){return[(o-n)/t*e,(r-a)/t*i]},o}function xr(t,n,r,e,i,o){var a=ht(o),s=vt(o),u=a*t,l=s*t,c=a/t,f=s/t,h=(s*r-a*n)/t,p=(s*n+a*r)/t;function g(t,o){return[u*(t*=e)-l*(o*=i)+n,r-l*t-u*o]}return g.invert=function(t,n){return[e*(c*t-f*n+h),i*(p-f*t-c*n)]},g}function Er(t){return Sr((function(){return t}))()}function Sr(t){var n,r,e,i,o,a,s,u,l,c,f=150,h=480,p=250,g=0,d=0,v=0,y=0,m=0,w=0,b=1,M=1,x=null,E=nn,S=null,_=sn,P=.5;function k(t){return u(t[0]*ut,t[1]*ut)}function A(t){return(t=u.invert(t[0],t[1]))&&[t[0]*st,t[1]*st]}function F(){var t=xr(f,0,0,b,M,w).apply(null,n(g,d)),e=(w?xr:Mr)(f,h-t[0],p-t[1],b,M,w);return r=Rt(v,y,m),s=Vt(n,e),u=Vt(r,s),a=wr(s,P),D()}function D(){return l=c=null,k}return k.stream=function(t){return l&&c===t?l:l=br(function(t){return cr({point:function(n,r){var e=t(n,r);return this.stream.point(e[0],e[1])}})}(r)(E(a(_(c=t)))))},k.preclip=function(t){return arguments.length?(E=t,x=void 0,D()):E},k.postclip=function(t){return arguments.length?(_=t,S=e=i=o=null,D()):_},k.clipAngle=function(t){return arguments.length?(E=+t?rn(x=t*ut):(x=null,nn),D()):x*st},k.clipExtent=function(t){return arguments.length?(_=null==t?(S=e=i=o=null,sn):an(S=+t[0][0],e=+t[0][1],i=+t[1][0],o=+t[1][1]),D()):null==S?null:[[S,e],[i,o]]},k.scale=function(t){return arguments.length?(f=+t,F()):f},k.translate=function(t){return arguments.length?(h=+t[0],p=+t[1],F()):[h,p]},k.center=function(t){return arguments.length?(g=t[0]%360*ut,d=t[1]%360*ut,F()):[g*st,d*st]},k.rotate=function(t){return arguments.length?(v=t[0]%360*ut,y=t[1]%360*ut,m=t.length>2?t[2]%360*ut:0,F()):[v*st,y*st,m*st]},k.angle=function(t){return arguments.length?(w=t%360*ut,F()):w*st},k.reflectX=function(t){return arguments.length?(b=t?-1:1,F()):b<0},k.reflectY=function(t){return arguments.length?(M=t?-1:1,F()):M<0},k.precision=function(t){return arguments.length?(a=wr(s,P=t*t),D()):mt(P)},k.fitExtent=function(t,n){return pr(k,t,n)},k.fitSize=function(t,n){return gr(k,t,n)},k.fitWidth=function(t,n){return dr(k,t,n)},k.fitHeight=function(t,n){return vr(k,t,n)},function(){return n=t.apply(this,arguments),k.invert=n.invert&&A,F()}}function _r(t){var n=0,r=et/3,e=Sr(t),i=e(n,r);return i.parallels=function(t){return arguments.length?e(n=t[0]*ut,r=t[1]*ut):[n*st,r*st]},i}function Pr(t,n){var r=vt(t),e=(r+vt(n))/2;if(lt(e)<rt)return function(t){var n=ht(t);function r(t,r){return[t*n,vt(r)/n]}return r.invert=function(t,r){return[t/n,Mt(r*n)]},r}(t);var i=1+r*(2*e-r),o=mt(i)/e;function a(t,n){var r=mt(i-2*e*vt(n))/e;return[r*vt(t*=e),o-r*ht(t)]}return a.invert=function(t,n){var r=o-n,a=ft(t,lt(r))*yt(r);return r*e<0&&(a-=et*yt(t)*yt(r)),[a/e,Mt((i-(t*t+r*r)*e*e)/(2*e))]},a}function kr(){return _r(Pr).scale(155.424).center([0,33.6442])}function Ar(){return kr().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function Fr(t){return function(n,r){var e=ht(n),i=ht(r),o=t(e*i);return[o*i*vt(n),o*vt(r)]}}function Dr(t){return function(n,r){var e=mt(n*n+r*r),i=t(e),o=vt(i),a=ht(i);return[ft(n*o,e*a),Mt(e&&r*o/e)]}}var Cr=Fr((function(t){return mt(2/(1+t))}));Cr.invert=Dr((function(t){return 2*Mt(t/2)}));var Lr=Fr((function(t){return(t=bt(t))&&t/vt(t)}));function Nr(t,n){return[t,gt(wt((it+n)/2))]}function Or(){return jr(Nr).scale(961/at)}function jr(t){var n,r,e,i=Er(t),o=i.center,a=i.scale,s=i.translate,u=i.clipExtent,l=null;function c(){var o=et*a(),s=i(function(t){function n(n){return(n=t(n[0]*ut,n[1]*ut))[0]*=st,n[1]*=st,n}return t=Rt(t[0]*ut,t[1]*ut,t.length>2?t[2]*ut:0),n.invert=function(n){return(n=t.invert(n[0]*ut,n[1]*ut))[0]*=st,n[1]*=st,n},n}(i.rotate()).invert([0,0]));return u(null==l?[[s[0]-o,s[1]-o],[s[0]+o,s[1]+o]]:t===Nr?[[Math.max(s[0]-o,l),n],[Math.min(s[0]+o,r),e]]:[[l,Math.max(s[1]-o,n)],[r,Math.min(s[1]+o,e)]])}return i.scale=function(t){return arguments.length?(a(t),c()):a()},i.translate=function(t){return arguments.length?(s(t),c()):s()},i.center=function(t){return arguments.length?(o(t),c()):o()},i.clipExtent=function(t){return arguments.length?(null==t?l=n=r=e=null:(l=+t[0][0],n=+t[0][1],r=+t[1][0],e=+t[1][1]),c()):null==l?null:[[l,n],[r,e]]},c()}function Vr(t){return wt((it+t)/2)}function Tr(t,n){var r=ht(t),e=t===n?vt(t):gt(r/ht(n))/gt(Vr(n)/Vr(t)),i=r*dt(Vr(t),e)/e;if(!e)return Nr;function o(t,n){i>0?n<-it+rt&&(n=-it+rt):n>it-rt&&(n=it-rt);var r=i/dt(Vr(n),e);return[r*vt(e*t),i-r*ht(e*t)]}return o.invert=function(t,n){var r=i-n,o=yt(e)*mt(t*t+r*r),a=ft(t,lt(r))*yt(r);return r*e<0&&(a-=et*yt(t)*yt(r)),[a/e,2*ct(dt(i/o,1/e))-it]},o}function Rr(t,n){return[t,n]}function qr(t,n){var r=ht(t),e=t===n?vt(t):(r-ht(n))/(n-t),i=r/e+t;if(lt(e)<rt)return Rr;function o(t,n){var r=i-n,o=e*t;return[r*vt(o),i-r*ht(o)]}return o.invert=function(t,n){var r=i-n,o=ft(t,lt(r))*yt(r);return r*e<0&&(o-=et*yt(t)*yt(r)),[o/e,i-yt(e)*mt(t*t+r*r)]},o}Lr.invert=Dr((function(t){return t})),Nr.invert=function(t,n){return[t,2*ct(pt(n))-it]},Rr.invert=Rr;var Br=1.340264,Gr=-.081106,Ir=893e-6,Hr=.003796,Ur=mt(3)/2;function $r(t,n){var r=Mt(Ur*vt(n)),e=r*r,i=e*e*e;return[t*ht(r)/(Ur*(Br+3*Gr*e+i*(7*Ir+9*Hr*e))),r*(Br+Gr*e+i*(Ir+Hr*e))]}function zr(t,n){var r=ht(n),e=ht(t)*r;return[r*vt(t)/e,vt(n)/e]}function Wr(t,n){var r=n*n,e=r*r;return[t*(.8707-.131979*r+e*(e*(.003971*r-.001529*e)-.013791)),n*(1.007226+r*(.015085+e*(.028874*r-.044475-.005916*e)))]}function Jr(t,n){return[ht(n)*vt(t),vt(n)]}function Xr(t,n){var r=ht(n),e=1+ht(t)*r;return[r*vt(t)/e,vt(n)/e]}function Yr(t,n){return[gt(wt((it+n)/2)),-t]}$r.invert=function(t,n){for(var r,e=n,i=e*e,o=i*i*i,a=0;a<12&&(o=(i=(e-=r=(e*(Br+Gr*i+o*(Ir+Hr*i))-n)/(Br+3*Gr*i+o*(7*Ir+9*Hr*i)))*e)*i*i,!(lt(r)<1e-12));++a);return[Ur*t*(Br+3*Gr*i+o*(7*Ir+9*Hr*i))/ht(e),Mt(vt(e)/Ur)]},zr.invert=Dr(ct),Wr.invert=function(t,n){var r,e=n,i=25;do{var o=e*e,a=o*o;e-=r=(e*(1.007226+o*(.015085+a*(.028874*o-.044475-.005916*a)))-n)/(1.007226+o*(.045255+a*(.259866*o-.311325-.005916*11*a)))}while(lt(r)>rt&&--i>0);return[t/(.8707+(o=e*e)*(o*(o*o*o*(.003971-.001529*o)-.013791)-.131979)),e]},Jr.invert=Dr(Mt),Xr.invert=Dr((function(t){return 2*ct(t)})),Yr.invert=function(t,n){return[-n,2*ct(pt(t))-it]};const Kr=Or().translate([0,0]).center([0,0]).scale(63781);function Zr(t){const n=Kr;if(void 0===t[2]){const r=n(t);return r[1]*=-1,r}const r=n(t);return r[1]*=-1,r.push(t[2]),r}const Qr={webmercator:Zr};var te=Object.prototype.hasOwnProperty;function ne(t,n,r,e,i,o){3===arguments.length&&(e=o=Array,i=null);for(var a=new e(t=1<<Math.max(4,Math.ceil(Math.log(t)/Math.LN2))),s=new o(t),u=t-1,l=0;l<t;++l)a[l]=i;return{set:function(e,o){for(var l=n(e)&u,c=a[l],f=0;c!=i;){if(r(c,e))return s[l]=o;if(++f>=t)throw new Error("full hashmap");c=a[l=l+1&u]}return a[l]=e,s[l]=o,o},maybeSet:function(e,o){for(var l=n(e)&u,c=a[l],f=0;c!=i;){if(r(c,e))return s[l];if(++f>=t)throw new Error("full hashmap");c=a[l=l+1&u]}return a[l]=e,s[l]=o,o},get:function(e,o){for(var l=n(e)&u,c=a[l],f=0;c!=i;){if(r(c,e))return s[l];if(++f>=t)break;c=a[l=l+1&u]}return o},keys:function(){for(var t=[],n=0,r=a.length;n<r;++n){var e=a[n];e!=i&&t.push(e)}return t}}}function re(t,n){return t[0]===n[0]&&t[1]===n[1]}var ee=new ArrayBuffer(16),ie=new Float64Array(ee),oe=new Uint32Array(ee);function ae(t){ie[0]=t[0],ie[1]=t[1];var n=oe[0]^oe[1];return 2147483647&(n=n<<5^n>>7^oe[2]^oe[3])}function se(t){var n,r,e,i,o=t.coordinates,a=t.lines,s=t.rings,u=function(){for(var t=ne(1.4*o.length,M,x,Int32Array,-1,Int32Array),n=new Int32Array(o.length),r=0,e=o.length;r<e;++r)n[r]=t.maybeSet(r,r);return n}(),l=new Int32Array(o.length),c=new Int32Array(o.length),f=new Int32Array(o.length),h=new Int8Array(o.length),p=0;for(n=0,r=o.length;n<r;++n)l[n]=c[n]=f[n]=-1;for(n=0,r=a.length;n<r;++n){var g=a[n],d=g[0],v=g[1];for(e=u[d],i=u[++d],++p,h[e]=1;++d<=v;)b(n,e,e=i,i=u[d]);++p,h[i]=1}for(n=0,r=o.length;n<r;++n)l[n]=-1;for(n=0,r=s.length;n<r;++n){var y=s[n],m=y[0]+1,w=y[1];for(b(n,u[w-1],e=u[m-1],i=u[m]);++m<=w;)b(n,e,e=i,i=u[m])}function b(t,n,r,e){if(l[r]!==t){l[r]=t;var i=c[r];if(i>=0){var o=f[r];i===n&&o===e||i===e&&o===n||(++p,h[r]=1)}else c[r]=n,f[r]=e}}function M(t){return ae(o[t])}function x(t,n){return re(o[t],o[n])}l=c=f=null;var E,S=function(t,n,r,e,i){3===arguments.length&&(e=Array,i=null);for(var o=new e(t=1<<Math.max(4,Math.ceil(Math.log(t)/Math.LN2))),a=t-1,s=0;s<t;++s)o[s]=i;return{add:function(e){for(var s=n(e)&a,u=o[s],l=0;u!=i;){if(r(u,e))return!0;if(++l>=t)throw new Error("full hashset");u=o[s=s+1&a]}return o[s]=e,!0},has:function(e){for(var s=n(e)&a,u=o[s],l=0;u!=i;){if(r(u,e))return!0;if(++l>=t)break;u=o[s=s+1&a]}return!1},values:function(){for(var t=[],n=0,r=o.length;n<r;++n){var e=o[n];e!=i&&t.push(e)}return t}}}(1.4*p,ae,re);for(n=0,r=o.length;n<r;++n)h[E=u[n]]&&S.add(o[E]);return S}function ue(t,n,r,e){le(t,n,r),le(t,n,n+e),le(t,n+e,r)}function le(t,n,r){for(var e,i=n+(r---n>>1);n<i;++n,--r)e=t[n],t[n]=t[r],t[r]=e}function ce(t){var n,r,e={};for(n in t)e[n]=null==(r=t[n])?{type:null}:("FeatureCollection"===r.type?fe:"Feature"===r.type?he:pe)(r);return e}function fe(t){var n={type:"GeometryCollection",geometries:t.features.map(he)};return null!=t.bbox&&(n.bbox=t.bbox),n}function he(t){var n,r=pe(t.geometry);for(n in null!=t.id&&(r.id=t.id),null!=t.bbox&&(r.bbox=t.bbox),t.properties){r.properties=t.properties;break}return r}function pe(t){if(null==t)return{type:null};var n="GeometryCollection"===t.type?{type:"GeometryCollection",geometries:t.geometries.map(pe)}:"Point"===t.type||"MultiPoint"===t.type?{type:t.type,coordinates:t.coordinates}:{type:t.type,arcs:t.coordinates};return null!=t.bbox&&(n.bbox=t.bbox),n}function ge(t){var n,r=t[0],e=t[1];return e<r&&(n=r,r=e,e=n),r+31*e}function de(t,n){var r,e=t[0],i=t[1],o=n[0],a=n[1];return i<e&&(r=e,e=i,i=r),a<o&&(r=o,o=a,a=r),e===o&&i===a}var ve=Object.freeze({__proto__:null,topology:function(t,n){var r=function(t){var n=1/0,r=1/0,e=-1/0,i=-1/0;function o(t){null!=t&&te.call(a,t.type)&&a[t.type](t)}var a={GeometryCollection:function(t){t.geometries.forEach(o)},Point:function(t){s(t.coordinates)},MultiPoint:function(t){t.coordinates.forEach(s)},LineString:function(t){u(t.arcs)},MultiLineString:function(t){t.arcs.forEach(u)},Polygon:function(t){t.arcs.forEach(u)},MultiPolygon:function(t){t.arcs.forEach(l)}};function s(t){var o=t[0],a=t[1];o<n&&(n=o),o>e&&(e=o),a<r&&(r=a),a>i&&(i=a)}function u(t){t.forEach(s)}function l(t){t.forEach(u)}for(var c in t)o(t[c]);return e>=n&&i>=r?[n,r,e,i]:void 0}(t=ce(t)),e=n>0&&r&&function(t,n,r){var e=n[0],i=n[1],o=n[2],a=n[3],s=o-e?(r-1)/(o-e):1,u=a-i?(r-1)/(a-i):1;function l(t){return[Math.round((t[0]-e)*s),Math.round((t[1]-i)*u)]}function c(t,n){for(var r,o,a,l,c,f=-1,h=0,p=t.length,g=new Array(p);++f<p;)r=t[f],l=Math.round((r[0]-e)*s),c=Math.round((r[1]-i)*u),l===o&&c===a||(g[h++]=[o=l,a=c]);for(g.length=h;h<n;)h=g.push([g[0][0],g[0][1]]);return g}function f(t){return c(t,2)}function h(t){return c(t,4)}function p(t){return t.map(h)}function g(t){null!=t&&te.call(d,t.type)&&d[t.type](t)}var d={GeometryCollection:function(t){t.geometries.forEach(g)},Point:function(t){t.coordinates=l(t.coordinates)},MultiPoint:function(t){t.coordinates=t.coordinates.map(l)},LineString:function(t){t.arcs=f(t.arcs)},MultiLineString:function(t){t.arcs=t.arcs.map(f)},Polygon:function(t){t.arcs=p(t.arcs)},MultiPolygon:function(t){t.arcs=t.arcs.map(p)}};for(var v in t)g(t[v]);return{scale:[1/s,1/u],translate:[e,i]}}(t,r,n),i=function(t){var n,r,e,i,o=t.coordinates,a=t.lines,s=t.rings,u=a.length+s.length;for(delete t.lines,delete t.rings,e=0,i=a.length;e<i;++e)for(n=a[e];n=n.next;)++u;for(e=0,i=s.length;e<i;++e)for(r=s[e];r=r.next;)++u;var l=ne(2*u*1.4,ae,re),c=t.arcs=[];for(e=0,i=a.length;e<i;++e){n=a[e];do{f(n)}while(n=n.next)}for(e=0,i=s.length;e<i;++e)if((r=s[e]).next)do{f(r)}while(r=r.next);else h(r);function f(t){var n,r,e,i,a,s,u,f;if(e=l.get(n=o[t[0]]))for(u=0,f=e.length;u<f;++u)if(p(i=e[u],t))return t[0]=i[0],void(t[1]=i[1]);if(a=l.get(r=o[t[1]]))for(u=0,f=a.length;u<f;++u)if(g(s=a[u],t))return t[1]=s[0],void(t[0]=s[1]);e?e.push(t):l.set(n,[t]),a?a.push(t):l.set(r,[t]),c.push(t)}function h(t){var n,r,e,i,a;if(r=l.get(o[t[0]]))for(i=0,a=r.length;i<a;++i){if(d(e=r[i],t))return t[0]=e[0],void(t[1]=e[1]);if(v(e,t))return t[0]=e[1],void(t[1]=e[0])}if(r=l.get(n=o[t[0]+y(t)]))for(i=0,a=r.length;i<a;++i){if(d(e=r[i],t))return t[0]=e[0],void(t[1]=e[1]);if(v(e,t))return t[0]=e[1],void(t[1]=e[0])}r?r.push(t):l.set(n,[t]),c.push(t)}function p(t,n){var r=t[0],e=n[0],i=t[1];if(r-i!=e-n[1])return!1;for(;r<=i;++r,++e)if(!re(o[r],o[e]))return!1;return!0}function g(t,n){var r=t[0],e=n[0],i=t[1],a=n[1];if(r-i!=e-a)return!1;for(;r<=i;++r,--a)if(!re(o[r],o[a]))return!1;return!0}function d(t,n){var r=t[0],e=n[0],i=t[1]-r;if(i!==n[1]-e)return!1;for(var a=y(t),s=y(n),u=0;u<i;++u)if(!re(o[r+(u+a)%i],o[e+(u+s)%i]))return!1;return!0}function v(t,n){var r=t[0],e=n[0],i=t[1],a=n[1],s=i-r;if(s!==a-e)return!1;for(var u=y(t),l=s-y(n),c=0;c<s;++c)if(!re(o[r+(c+u)%s],o[a-(c+l)%s]))return!1;return!0}function y(t){for(var n=t[0],r=t[1],e=n,i=e,a=o[e];++e<r;){var s=o[e];(s[0]<a[0]||s[0]===a[0]&&s[1]<a[1])&&(i=e,a=s)}return i-n}return t}(function(t){var n,r,e,i=se(t),o=t.coordinates,a=t.lines,s=t.rings;for(r=0,e=a.length;r<e;++r)for(var u=a[r],l=u[0],c=u[1];++l<c;)i.has(o[l])&&(n={0:l,1:u[1]},u[1]=l,u=u.next=n);for(r=0,e=s.length;r<e;++r)for(var f=s[r],h=f[0],p=h,g=f[1],d=i.has(o[h]);++p<g;)i.has(o[p])&&(d?(n={0:p,1:f[1]},f[1]=p,f=f.next=n):(ue(o,h,g,g-p),o[g]=o[h],d=!0,p=h));return t}(function(t){var n=-1,r=[],e=[],i=[];function o(t){t&&te.call(a,t.type)&&a[t.type](t)}var a={GeometryCollection:function(t){t.geometries.forEach(o)},LineString:function(t){t.arcs=s(t.arcs)},MultiLineString:function(t){t.arcs=t.arcs.map(s)},Polygon:function(t){t.arcs=t.arcs.map(u)},MultiPolygon:function(t){t.arcs=t.arcs.map(l)}};function s(t){for(var e=0,o=t.length;e<o;++e)i[++n]=t[e];var a={0:n-o+1,1:n};return r.push(a),a}function u(t){for(var r=0,o=t.length;r<o;++r)i[++n]=t[r];var a={0:n-o+1,1:n};return e.push(a),a}function l(t){return t.map(u)}for(var c in t)o(t[c]);return{type:"Topology",coordinates:i,lines:r,rings:e,objects:t}}(t))),o=i.coordinates,a=ne(1.4*i.arcs.length,ge,de);function s(t){t&&te.call(u,t.type)&&u[t.type](t)}t=i.objects,i.bbox=r,i.arcs=i.arcs.map((function(t,n){return a.set(t,n),o.slice(t[0],t[1]+1)})),delete i.coordinates,o=null;var u={GeometryCollection:function(t){t.geometries.forEach(s)},LineString:function(t){t.arcs=l(t.arcs)},MultiLineString:function(t){t.arcs=t.arcs.map(l)},Polygon:function(t){t.arcs=t.arcs.map(l)},MultiPolygon:function(t){t.arcs=t.arcs.map(c)}};function l(t){var n=[];do{var r=a.get(t);n.push(t[0]<t[1]?r:~r)}while(t=t.next);return n}function c(t){return t.map(l)}for(var f in t)s(t[f]);return e&&(i.transform=e,i.arcs=function(t){for(var n=-1,r=t.length;++n<r;){for(var e,i,o=t[n],a=0,s=1,u=o.length,l=o[0],c=l[0],f=l[1];++a<u;)e=(l=o[a])[0],i=l[1],e===c&&i===f||(o[s++]=[e-c,i-f],c=e,f=i);1===s&&(o[s++]=[0,0]),o.length=s}return t}(i.arcs)),i}}),ye=a(ve);function me(t){return t}function we(t){if(null==t)return me;var n,r,e=t.scale[0],i=t.scale[1],o=t.translate[0],a=t.translate[1];return function(t,s){s||(n=r=0);var u=2,l=t.length,c=new Array(l);for(c[0]=(n+=t[0])*e+o,c[1]=(r+=t[1])*i+a;u<l;)c[u]=t[u],++u;return c}}function be(t){var n,r=we(t.transform),e=1/0,i=e,o=-e,a=-e;function s(t){(t=r(t))[0]<e&&(e=t[0]),t[0]>o&&(o=t[0]),t[1]<i&&(i=t[1]),t[1]>a&&(a=t[1])}function u(t){switch(t.type){case"GeometryCollection":t.geometries.forEach(u);break;case"Point":s(t.coordinates);break;case"MultiPoint":t.coordinates.forEach(s)}}for(n in t.arcs.forEach((function(t){for(var n,s=-1,u=t.length;++s<u;)(n=r(t[s],s))[0]<e&&(e=n[0]),n[0]>o&&(o=n[0]),n[1]<i&&(i=n[1]),n[1]>a&&(a=n[1])})),t.objects)u(t.objects[n]);return[e,i,o,a]}function Me(t,n){return"string"==typeof n&&(n=t.objects[n]),"GeometryCollection"===n.type?{type:"FeatureCollection",features:n.geometries.map((function(n){return xe(t,n)}))}:xe(t,n)}function xe(t,n){var r=n.id,e=n.bbox,i=null==n.properties?{}:n.properties,o=Ee(t,n);return null==r&&null==e?{type:"Feature",properties:i,geometry:o}:null==e?{type:"Feature",id:r,properties:i,geometry:o}:{type:"Feature",id:r,bbox:e,properties:i,geometry:o}}function Ee(t,n){var r=we(t.transform),e=t.arcs;function i(t,n){n.length&&n.pop();for(var i=e[t<0?~t:t],o=0,a=i.length;o<a;++o)n.push(r(i[o],o));t<0&&function(t,n){for(var r,e=t.length,i=e-n;i<--e;)r=t[i],t[i++]=t[e],t[e]=r}(n,a)}function o(t){return r(t)}function a(t){for(var n=[],r=0,e=t.length;r<e;++r)i(t[r],n);return n.length<2&&n.push(n[0]),n}function s(t){for(var n=a(t);n.length<4;)n.push(n[0]);return n}function u(t){return t.map(s)}return function t(n){var r,e=n.type;switch(e){case"GeometryCollection":return{type:e,geometries:n.geometries.map(t)};case"Point":r=o(n.coordinates);break;case"MultiPoint":r=n.coordinates.map(o);break;case"LineString":r=a(n.arcs);break;case"MultiLineString":r=n.arcs.map(a);break;case"Polygon":r=u(n.arcs);break;case"MultiPolygon":r=n.arcs.map(u);break;default:return null}return{type:e,coordinates:r}}(n)}function Se(t,n){var r={},e={},i={},o=[],a=-1;function s(t,n){for(var e in t){var i=t[e];delete n[i.start],delete i.start,delete i.end,i.forEach((function(t){r[t<0?~t:t]=1})),o.push(i)}}return n.forEach((function(r,e){var i,o=t.arcs[r<0?~r:r];o.length<3&&!o[1][0]&&!o[1][1]&&(i=n[++a],n[a]=r,n[e]=i)})),n.forEach((function(n){var r,o,a=function(n){var r,e=t.arcs[n<0?~n:n],i=e[0];t.transform?(r=[0,0],e.forEach((function(t){r[0]+=t[0],r[1]+=t[1]}))):r=e[e.length-1];return n<0?[r,i]:[i,r]}(n),s=a[0],u=a[1];if(r=i[s])if(delete i[r.end],r.push(n),r.end=u,o=e[u]){delete e[o.start];var l=o===r?r:r.concat(o);e[l.start=r.start]=i[l.end=o.end]=l}else e[r.start]=i[r.end]=r;else if(r=e[u])if(delete e[r.start],r.unshift(n),r.start=s,o=i[s]){delete i[o.end];var c=o===r?r:o.concat(r);e[c.start=o.start]=i[c.end=r.end]=c}else e[r.start]=i[r.end]=r;else e[(r=[n]).start=s]=i[r.end=u]=r})),s(i,e),s(e,i),n.forEach((function(t){r[t<0?~t:t]||o.push([t])})),o}function _e(t,n,r){var e,i,o;if(arguments.length>1)e=function(t,n,r){var e,i=[],o=[];function a(t){var n=t<0?~t:t;(o[n]||(o[n]=[])).push({i:t,g:e})}function s(t){t.forEach(a)}function u(t){t.forEach(s)}function l(t){t.forEach(u)}function c(t){switch(e=t,t.type){case"GeometryCollection":t.geometries.forEach(c);break;case"LineString":s(t.arcs);break;case"MultiLineString":case"Polygon":u(t.arcs);break;case"MultiPolygon":l(t.arcs)}}return c(n),o.forEach(null==r?function(t){i.push(t[0].i)}:function(t){r(t[0].g,t[t.length-1].g)&&i.push(t[0].i)}),i}(0,n,r);else for(i=0,e=new Array(o=t.arcs.length);i<o;++i)e[i]=i;return{type:"MultiLineString",arcs:Se(t,e)}}function Pe(t,n){var r={},e=[],i=[];function o(t){t.forEach((function(n){n.forEach((function(n){(r[n=n<0?~n:n]||(r[n]=[])).push(t)}))})),e.push(t)}function a(n){return function(t){for(var n,r=-1,e=t.length,i=t[e-1],o=0;++r<e;)n=i,i=t[r],o+=n[0]*i[1]-n[1]*i[0];return Math.abs(o)}(Ee(t,{type:"Polygon",arcs:[n]}).coordinates[0])}return n.forEach((function t(n){switch(n.type){case"GeometryCollection":n.geometries.forEach(t);break;case"Polygon":o(n.arcs);break;case"MultiPolygon":n.arcs.forEach(o)}})),e.forEach((function(t){if(!t._){var n=[],e=[t];for(t._=1,i.push(n);t=e.pop();)n.push(t),t.forEach((function(t){t.forEach((function(t){r[t<0?~t:t].forEach((function(t){t._||(t._=1,e.push(t))}))}))}))}})),e.forEach((function(t){delete t._})),{type:"MultiPolygon",arcs:i.map((function(n){var e,i=[];if(n.forEach((function(t){t.forEach((function(t){t.forEach((function(t){r[t<0?~t:t].length<2&&i.push(t)}))}))})),(e=(i=Se(t,i)).length)>1)for(var o,s,u=1,l=a(i[0]);u<e;++u)(o=a(i[u]))>l&&(s=i[0],i[0]=i[u],i[u]=s,l=o);return i})).filter((function(t){return t.length>0}))}}function ke(t,n){for(var r=0,e=t.length;r<e;){var i=r+e>>>1;t[i]<n?r=i+1:e=i}return r}function Ae(t){if(null==t)return me;var n,r,e=t.scale[0],i=t.scale[1],o=t.translate[0],a=t.translate[1];return function(t,s){s||(n=r=0);var u=2,l=t.length,c=new Array(l),f=Math.round((t[0]-o)/e),h=Math.round((t[1]-a)/i);for(c[0]=f-n,n=f,c[1]=h-r,r=h;u<l;)c[u]=t[u],++u;return c}}var Fe=Object.freeze({__proto__:null,bbox:be,feature:Me,merge:function(t){return Ee(t,Pe.apply(this,arguments))},mergeArcs:Pe,mesh:function(t){return Ee(t,_e.apply(this,arguments))},meshArcs:_e,neighbors:function(t){var n={},r=t.map((function(){return[]}));function e(t,r){t.forEach((function(t){t<0&&(t=~t);var e=n[t];e?e.push(r):n[t]=[r]}))}function i(t,n){t.forEach((function(t){e(t,n)}))}var o={LineString:e,MultiLineString:i,Polygon:i,MultiPolygon:function(t,n){t.forEach((function(t){i(t,n)}))}};for(var a in t.forEach((function t(n,r){"GeometryCollection"===n.type?n.geometries.forEach((function(n){t(n,r)})):n.type in o&&o[n.type](n.arcs,r)})),n)for(var s=n[a],u=s.length,l=0;l<u;++l)for(var c=l+1;c<u;++c){var f,h=s[l],p=s[c];(f=r[h])[a=ke(f,p)]!==p&&f.splice(a,0,p),(f=r[p])[a=ke(f,h)]!==h&&f.splice(a,0,h)}return r},quantize:function(t,n){if(t.transform)throw new Error("already quantized");if(n&&n.scale)s=t.bbox;else{if(!((r=Math.floor(n))>=2))throw new Error("n must be ≥2");var r,e=(s=t.bbox||be(t))[0],i=s[1],o=s[2],a=s[3];n={scale:[o-e?(o-e)/(r-1):1,a-i?(a-i)/(r-1):1],translate:[e,i]}}var s,u,l=Ae(n),c=t.objects,f={};function h(t){return l(t)}function p(t){var n;switch(t.type){case"GeometryCollection":n={type:"GeometryCollection",geometries:t.geometries.map(p)};break;case"Point":n={type:"Point",coordinates:h(t.coordinates)};break;case"MultiPoint":n={type:"MultiPoint",coordinates:t.coordinates.map(h)};break;default:return t}return null!=t.id&&(n.id=t.id),null!=t.bbox&&(n.bbox=t.bbox),null!=t.properties&&(n.properties=t.properties),n}for(u in c)f[u]=p(c[u]);return{type:"Topology",bbox:s,transform:n,objects:f,arcs:t.arcs.map((function(t){var n,r=0,e=1,i=t.length,o=new Array(i);for(o[0]=l(t[0],0);++r<i;)((n=l(t[r],r))[0]||n[1])&&(o[e++]=n);return 1===e&&(o[e++]=[0,0]),o.length=e,o}))}},transform:we,untransform:Ae}),De=a(Fe),Ce=function(t){var n=t.slice(),r=[];for(;n.length>0;){var e=n.shift();n=n.reduce((function(t,n){var r=Ne(e,n);return r?e=r:t.push(n),t}),[]),r.push(e)}r=1===r.length?r[0]:{type:"MultiLineString",coordinates:r.map((function(t){return t.coordinates}))};return r};function Le(t){return t[0].toString()+","+t[1].toString()}function Ne(t,n){var r,e=Le(t.coordinates[0]),i=Le(t.coordinates[t.coordinates.length-1]),o=Le(n.coordinates[0]),a=Le(n.coordinates[n.coordinates.length-1]);if(e===a)r=n.coordinates.concat(t.coordinates.slice(1));else if(o===i)r=t.coordinates.concat(n.coordinates.slice(1));else if(e===o)r=t.coordinates.slice(1).reverse().concat(n.coordinates);else{if(i!==a)return null;r=t.coordinates.concat(n.coordinates.reverse().slice(1))}return{type:"LineString",coordinates:r}}var Oe={};function je(t,n,r){var e,i,o,a,s,u,l,c,f,h,p=0,g=0,d="FeatureCollection"===t.type,v="Feature"===t.type,y=d?t.features.length:1;for(e=0;e<y;e++)for(l=(h="GeometryCollection"===(f=d?t.features[e].geometry:v?t.geometry:t).type)?f.geometries.length:1,a=0;a<l;a++)if(c=(u=h?f.geometries[a]:f).coordinates,p=!r||"Polygon"!==u.type&&"MultiPolygon"!==u.type?0:1,"Point"===u.type)n(c,g),g++;else if("LineString"===u.type||"MultiPoint"===u.type)for(i=0;i<c.length;i++)n(c[i],g),g++;else if("Polygon"===u.type||"MultiLineString"===u.type)for(i=0;i<c.length;i++)for(o=0;o<c[i].length-p;o++)n(c[i][o],g),g++;else if("MultiPolygon"===u.type)for(i=0;i<c.length;i++)for(o=0;o<c[i].length;o++)for(s=0;s<c[i][o].length-p;s++)n(c[i][o][s],g),g++;else{if("GeometryCollection"!==u.type)throw new Error("Unknown Geometry Type");for(i=0;i<u.geometries.length;i++)je(u.geometries[i],n,r)}}function Ve(t,n){var r;switch(t.type){case"FeatureCollection":for(r=0;r<t.features.length;r++)n(t.features[r].properties,r);break;case"Feature":n(t.properties,0)}}function Te(t,n){if("Feature"===t.type)n(t,0);else if("FeatureCollection"===t.type)for(var r=0;r<t.features.length;r++)n(t.features[r],r)}function Re(t,n){var r,e,i,o,a,s,u,l=0,c="FeatureCollection"===t.type,f="Feature"===t.type,h=c?t.features.length:1;for(r=0;r<h;r++)for(a=(u="GeometryCollection"===(s=c?t.features[r].geometry:f?t.geometry:t).type)?s.geometries.length:1,i=0;i<a;i++)if("Point"===(o=u?s.geometries[i]:s).type||"LineString"===o.type||"MultiPoint"===o.type||"Polygon"===o.type||"MultiLineString"===o.type||"MultiPolygon"===o.type)n(o,l),l++;else{if("GeometryCollection"!==o.type)throw new Error("Unknown Geometry Type");for(e=0;e<o.geometries.length;e++)n(o.geometries[e],l),l++}}Oe.coordEach=je,Oe.coordReduce=function(t,n,r,e){var i=r;return je(t,(function(t,e){i=0===e&&void 0===r?t:n(i,t,e)}),e),i},Oe.propEach=Ve,Oe.propReduce=function(t,n,r){var e=r;return Ve(t,(function(t,i){e=0===i&&void 0===r?t:n(e,t,i)})),e},Oe.featureEach=Te,Oe.featureReduce=function(t,n,r){var e=r;return Te(t,(function(t,i){e=0===i&&void 0===r?t:n(e,t,i)})),e},Oe.coordAll=function(t){var n=[];return je(t,(function(t){n.push(t)})),n},Oe.geomEach=Re,Oe.geomReduce=function(t,n,r){var e=r;return Re(t,(function(t,i){e=0===i&&void 0===r?t:n(e,t,i)})),e};var qe=ye.topology,Be=De.merge,Ge=Ce,Ie=Oe.geomEach,He=function t(n){switch(n&&n.type||null){case"FeatureCollection":return n.features=n.features.reduce((function(n,r){return n.concat(t(r))}),[]),n;case"Feature":return n.geometry?t(n.geometry).map((function(t){var r={type:"Feature",properties:JSON.parse(JSON.stringify(n.properties)),geometry:t};return void 0!==n.id&&(r.id=n.id),r})):n;case"MultiPoint":return n.coordinates.map((function(t){return{type:"Point",coordinates:t}}));case"MultiPolygon":return n.coordinates.map((function(t){return{type:"Polygon",coordinates:t}}));case"MultiLineString":return n.coordinates.map((function(t){return{type:"LineString",coordinates:t}}));case"GeometryCollection":return n.geometries.map(t).reduce((function(t,n){return t.concat(n)}),[]);case"Point":case"Polygon":case"LineString":return[n]}},Ue=function(){var t=(e=arguments,e.length?Array.isArray(e[0])?e[0]:Array.prototype.slice.call(e):[]),n=t.reduce((function(t,n){var r=He(n);Array.isArray(r)||(r=[r]);for(var e=0;e<r.length;e++)Ie(r[e],(function(n){t.push(n)}));return t}),[]),r=function(t){for(var n=null,r=0;r<t.length;r++)if(n){if(n!==t[r].type)return null}else n=t[r].type;return n}(n);var e;if(!r)throw new Error("List does not contain only homoegenous GeoJSON");switch(r){case"LineString":return Ge(n);case"Polygon":return function(t){var n={geoms:{type:"GeometryCollection",geometries:JSON.parse(JSON.stringify(t))}},r=qe(n);return Be(r,r.objects.geoms.geometries)}(n);default:return n}};var $e=o(Ue);var ze={exports:{}},We=function(t,n){this.p1=t,this.p2=n};We.prototype.rise=function(){return this.p2[1]-this.p1[1]},We.prototype.run=function(){return this.p2[0]-this.p1[0]},We.prototype.slope=function(){return this.rise()/this.run()},We.prototype.yIntercept=function(){return this.p1[1]-this.p1[0]*this.slope(this.p1,this.p2)},We.prototype.isVertical=function(){return!isFinite(this.slope())},We.prototype.isHorizontal=function(){return this.p1[1]==this.p2[1]},We.prototype._perpendicularDistanceHorizontal=function(t){return Math.abs(this.p1[1]-t[1])},We.prototype._perpendicularDistanceVertical=function(t){return Math.abs(this.p1[0]-t[0])},We.prototype._perpendicularDistanceHasSlope=function(t){var n=this.slope(),r=this.yIntercept();return Math.abs(n*t[0]-t[1]+r)/Math.sqrt(Math.pow(n,2)+1)},We.prototype.perpendicularDistance=function(t){return this.isVertical()?this._perpendicularDistanceVertical(t):this.isHorizontal()?this._perpendicularDistanceHorizontal(t):this._perpendicularDistanceHasSlope(t)};var Je=We,Xe=function(t,n){for(var r=0,e=0,i=1;i<=t.length-2;i++){var o=new Je(t[0],t[t.length-1]).perpendicularDistance(t[i]);o>r&&(e=i,r=o)}if(r>n)var a=Xe(t.slice(0,e),n),s=Xe(t.slice(e,t.length),n),u=a.concat(s);else u=t.length>1?[t[0],t[t.length-1]]:[t[0]];return u},Ye=Xe;!function(t){var n=Ye;function r(n,r){var e=n.geometry,i=e.type;if("LineString"===i)e.coordinates=t.exports.simplify(e.coordinates,r);else if("Polygon"===i||"MultiLineString"===i)for(var o=0;o<e.coordinates.length;o++)e.coordinates[o]=t.exports.simplify(e.coordinates[o],r);else if("MultiPolygon"===i)for(var a=0;a<e.coordinates.length;a++)for(var s=0;s<e.coordinates[a].length;s++)e.coordinates[a][s]=t.exports.simplify(e.coordinates[a][s],r);return n}t.exports=function(t,n,e){return e||(t=JSON.parse(JSON.stringify(t))),t.features?function(t,n){for(var e=0;e<t.features.length;e++)t.features[e]=r(t.features[e],n);return t}(t,n):t.type&&"Feature"===t.type?r(t,n):new Error("FeatureCollection or individual Feature required")},t.exports.simplify=function(t,r){return n(t,r)}}(ze);var Ke=o(ze.exports);const Ze=function(t,...n){return function(t,...n){t||(t={});let r=-1;const e=n.length;for(;++r<e;){ti(t,n[r])}return t}(E(t),...n)};function Qe(t,n,r){const e=n[r];if(void 0===e)t[r]=null;else if(g(e)){g(t[r])||(t[r]={});for(const n in e)Qe(t[r],e,n)}else t[r]=e}function ti(t,n){if(g(n)&&t!==n)for(const r in n)Qe(t,n,r)}const ni={tolerance:.01};function ri(t){if(0===t.length)return 0;var n,r=t[0],e=0;if("number"!=typeof r)return NaN;for(var i=1;i<t.length;i++){if("number"!=typeof t[i])return NaN;n=r+t[i],Math.abs(r)>=Math.abs(t[i])?e+=r-n+t[i]:e+=t[i]-n+r,r=n}return r+e}function ei(t){if(0===t.length)throw new Error("mean requires at least one data point");return ri(t)/t.length}function ii(t,n){var r,e,i=ei(t),o=0;if(2===n)for(e=0;e<t.length;e++)o+=(r=t[e]-i)*r;else for(e=0;e<t.length;e++)o+=Math.pow(t[e]-i,n);return o}function oi(t){if(0===t.length)throw new Error("variance requires at least one data point");return ii(t,2)/t.length}function ai(t){if(1===t.length)return 0;var n=oi(t);return Math.sqrt(n)}function si(t){if(0===t.length)throw new Error("mode requires at least one data point");if(1===t.length)return t[0];for(var n=t[0],r=NaN,e=0,i=1,o=1;o<t.length+1;o++)t[o]!==n?(i>e&&(e=i,r=n),i=1,n=t[o]):i++;return r}function ui(t){return t.slice().sort((function(t,n){return t-n}))}function li(t){if(0===t.length)throw new Error("min requires at least one data point");for(var n=t[0],r=1;r<t.length;r++)t[r]<n&&(n=t[r]);return n}function ci(t){if(0===t.length)throw new Error("max requires at least one data point");for(var n=t[0],r=1;r<t.length;r++)t[r]>n&&(n=t[r]);return n}function fi(t){for(var n=0,r=0;r<t.length;r++){if("number"!=typeof t[r])return NaN;n+=t[r]}return n}function hi(t,n){var r=t.length*n;if(0===t.length)throw new Error("quantile requires at least one data point.");if(n<0||n>1)throw new Error("quantiles must be between 0 and 1");return 1===n?t[t.length-1]:0===n?t[0]:r%1!=0?t[Math.ceil(r)-1]:t.length%2==0?(t[r-1]+t[r])/2:t[r]}function pi(t,n,r,e){for(r=r||0,e=e||t.length-1;e>r;){if(e-r>600){var i=e-r+1,o=n-r+1,a=Math.log(i),s=.5*Math.exp(2*a/3),u=.5*Math.sqrt(a*s*(i-s)/i);o-i/2<0&&(u*=-1),pi(t,n,Math.max(r,Math.floor(n-o*s/i+u)),Math.min(e,Math.floor(n+(i-o)*s/i+u)))}var l=t[n],c=r,f=e;for(gi(t,r,n),t[e]>l&&gi(t,r,e);c<f;){for(gi(t,c,f),c++,f--;t[c]<l;)c++;for(;t[f]>l;)f--}t[r]===l?gi(t,r,f):gi(t,++f,e),f<=n&&(r=f+1),n<=f&&(e=f-1)}}function gi(t,n,r){var e=t[n];t[n]=t[r],t[r]=e}function di(t,n){var r=t.slice();if(Array.isArray(n)){!function(t,n){for(var r=[0],e=0;e<n.length;e++)r.push(mi(t.length,n[e]));r.push(t.length-1),r.sort(yi);var i=[0,r.length-1];for(;i.length;){var o=Math.ceil(i.pop()),a=Math.floor(i.pop());if(!(o-a<=1)){var s=Math.floor((a+o)/2);vi(t,r[s],Math.floor(r[a]),Math.ceil(r[o])),i.push(a,s,s,o)}}}(r,n);for(var e=[],i=0;i<n.length;i++)e[i]=hi(r,n[i]);return e}return vi(r,mi(r.length,n),0,r.length-1),hi(r,n)}function vi(t,n,r,e){n%1==0?pi(t,n,r,e):(pi(t,n=Math.floor(n),r,e),pi(t,n+1,n+1,e))}function yi(t,n){return t-n}function mi(t,n){var r=t*n;return 1===n?t-1:0===n?0:r%1!=0?Math.ceil(r)-1:t%2==0?r-.5:r}function wi(t,n){if(n<t[0])return 0;if(n>t[t.length-1])return 1;var r=function(t,n){var r=0,e=0,i=t.length;for(;e<i;)n<=t[r=e+i>>>1]?i=r:e=-~r;return e}(t,n);if(t[r]!==n)return r/t.length;r++;var e=function(t,n){var r=0,e=0,i=t.length;for(;e<i;)n>=t[r=e+i>>>1]?e=-~r:i=r;return e}(t,n);if(e===r)return r/t.length;var i=e-r+1;return i*(e+r)/2/i/t.length}function bi(t){var n=di(t,.75),r=di(t,.25);if("number"==typeof n&&"number"==typeof r)return n-r}function Mi(t){return+di(t,.5)}function xi(t){for(var n=Mi(t),r=[],e=0;e<t.length;e++)r.push(Math.abs(t[e]-n));return Mi(r)}function Ei(t,n){n=n||Math.random;for(var r,e,i=t.length;i>0;)e=Math.floor(n()*i--),r=t[i],t[i]=t[e],t[e]=r;return t}function Si(t,n){return Ei(t.slice(),n)}function _i(t,n,r){return Si(t,r).slice(0,n)}function Pi(t,n){for(var r=[],e=0;e<t;e++){for(var i=[],o=0;o<n;o++)i.push(0);r.push(i)}return r}function ki(t){for(var n,r=0,e=0;e<t.length;e++)0!==e&&t[e]===n||(n=t[e],r++);return r}function Ai(t,n,r,e){var i;if(t>0){var o=(r[n]-r[t-1])/(n-t+1);i=e[n]-e[t-1]-(n-t+1)*o*o}else i=e[n]-r[n]*r[n]/(n+1);return i<0?0:i}function Fi(t,n,r,e,i,o,a){if(!(t>n)){var s=Math.floor((t+n)/2);e[r][s]=e[r-1][s-1],i[r][s]=s;var u=r;t>r&&(u=Math.max(u,i[r][t-1]||0)),u=Math.max(u,i[r-1][s]||0);var l,c,f,h=s-1;n<e[0].length-1&&(h=Math.min(h,i[r][n+1]||0));for(var p=h;p>=u&&!((l=Ai(p,s,o,a))+e[r-1][u-1]>=e[r][s]);--p)(c=Ai(u,s,o,a)+e[r-1][u-1])<e[r][s]&&(e[r][s]=c,i[r][s]=u),u++,(f=l+e[r-1][p-1])<e[r][s]&&(e[r][s]=f,i[r][s]=p);Fi(t,s-1,r,e,i,o,a),Fi(s+1,n,r,e,i,o,a)}}function Di(t,n){if(t.length!==n.length)throw new Error("sampleCovariance requires samples with equal lengths");if(t.length<2)throw new Error("sampleCovariance requires at least two data points in each sample");for(var r=ei(t),e=ei(n),i=0,o=0;o<t.length;o++)i+=(t[o]-r)*(n[o]-e);return i/(t.length-1)}function Ci(t){if(t.length<2)throw new Error("sampleVariance requires at least two data points");return ii(t,2)/(t.length-1)}function Li(t){var n=Ci(t);return Math.sqrt(n)}function Ni(t,n){return Di(t,n)/Li(t)/Li(n)}function Oi(t,n,r,e){return(t*n+r*e)/(n+e)}function ji(t){if(0===t.length)throw new Error("meanSimple requires at least one data point");return fi(t)/t.length}function Vi(t){if(0===t.length)throw new Error("rootMeanSquare requires at least one data point");for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r],2);return Math.sqrt(n/t.length)}var Ti=function(){this.totalCount=0,this.data={}};Ti.prototype.train=function(t,n){for(var r in this.data[n]||(this.data[n]={}),t){var e=t[r];void 0===this.data[n][r]&&(this.data[n][r]={}),void 0===this.data[n][r][e]&&(this.data[n][r][e]=0),this.data[n][r][e]++}this.totalCount++},Ti.prototype.score=function(t){var n,r={};for(var e in t){var i=t[e];for(n in this.data)r[n]={},this.data[n][e]?r[n][e+"_"+i]=(this.data[n][e][i]||0)/this.totalCount:r[n][e+"_"+i]=0}var o={};for(n in r)for(var a in o[n]=0,r[n])o[n]+=r[n][a];return o};var Ri=function(){this.weights=[],this.bias=0};Ri.prototype.predict=function(t){if(t.length!==this.weights.length)return null;for(var n=0,r=0;r<this.weights.length;r++)n+=this.weights[r]*t[r];return(n+=this.bias)>0?1:0},Ri.prototype.train=function(t,n){if(0!==n&&1!==n)return null;t.length!==this.weights.length&&(this.weights=t,this.bias=1);var r=this.predict(t);if("number"==typeof r&&r!==n){for(var e=n-r,i=0;i<this.weights.length;i++)this.weights[i]+=e*t[i];this.bias+=e}return this};var qi=1e-4;function Bi(t){if(t<0)throw new Error("factorial requires a non-negative value");if(Math.floor(t)!==t)throw new Error("factorial requires an integer input");for(var n=1,r=2;r<=t;r++)n*=r;return n}var Gi=[.9999999999999971,57.15623566586292,-59.59796035547549,14.136097974741746,-.4919138160976202,3399464998481189e-20,4652362892704858e-20,-9837447530487956e-20,.0001580887032249125,-.00021026444172410488,.00021743961811521265,-.0001643181065367639,8441822398385275e-20,-26190838401581408e-21,36899182659531625e-22],Ii=Math.log(Math.sqrt(2*Math.PI));var Hi={1:{.995:0,.99:0,.975:0,.95:0,.9:.02,.5:.45,.1:2.71,.05:3.84,.025:5.02,.01:6.63,.005:7.88},2:{.995:.01,.99:.02,.975:.05,.95:.1,.9:.21,.5:1.39,.1:4.61,.05:5.99,.025:7.38,.01:9.21,.005:10.6},3:{.995:.07,.99:.11,.975:.22,.95:.35,.9:.58,.5:2.37,.1:6.25,.05:7.81,.025:9.35,.01:11.34,.005:12.84},4:{.995:.21,.99:.3,.975:.48,.95:.71,.9:1.06,.5:3.36,.1:7.78,.05:9.49,.025:11.14,.01:13.28,.005:14.86},5:{.995:.41,.99:.55,.975:.83,.95:1.15,.9:1.61,.5:4.35,.1:9.24,.05:11.07,.025:12.83,.01:15.09,.005:16.75},6:{.995:.68,.99:.87,.975:1.24,.95:1.64,.9:2.2,.5:5.35,.1:10.65,.05:12.59,.025:14.45,.01:16.81,.005:18.55},7:{.995:.99,.99:1.25,.975:1.69,.95:2.17,.9:2.83,.5:6.35,.1:12.02,.05:14.07,.025:16.01,.01:18.48,.005:20.28},8:{.995:1.34,.99:1.65,.975:2.18,.95:2.73,.9:3.49,.5:7.34,.1:13.36,.05:15.51,.025:17.53,.01:20.09,.005:21.96},9:{.995:1.73,.99:2.09,.975:2.7,.95:3.33,.9:4.17,.5:8.34,.1:14.68,.05:16.92,.025:19.02,.01:21.67,.005:23.59},10:{.995:2.16,.99:2.56,.975:3.25,.95:3.94,.9:4.87,.5:9.34,.1:15.99,.05:18.31,.025:20.48,.01:23.21,.005:25.19},11:{.995:2.6,.99:3.05,.975:3.82,.95:4.57,.9:5.58,.5:10.34,.1:17.28,.05:19.68,.025:21.92,.01:24.72,.005:26.76},12:{.995:3.07,.99:3.57,.975:4.4,.95:5.23,.9:6.3,.5:11.34,.1:18.55,.05:21.03,.025:23.34,.01:26.22,.005:28.3},13:{.995:3.57,.99:4.11,.975:5.01,.95:5.89,.9:7.04,.5:12.34,.1:19.81,.05:22.36,.025:24.74,.01:27.69,.005:29.82},14:{.995:4.07,.99:4.66,.975:5.63,.95:6.57,.9:7.79,.5:13.34,.1:21.06,.05:23.68,.025:26.12,.01:29.14,.005:31.32},15:{.995:4.6,.99:5.23,.975:6.27,.95:7.26,.9:8.55,.5:14.34,.1:22.31,.05:25,.025:27.49,.01:30.58,.005:32.8},16:{.995:5.14,.99:5.81,.975:6.91,.95:7.96,.9:9.31,.5:15.34,.1:23.54,.05:26.3,.025:28.85,.01:32,.005:34.27},17:{.995:5.7,.99:6.41,.975:7.56,.95:8.67,.9:10.09,.5:16.34,.1:24.77,.05:27.59,.025:30.19,.01:33.41,.005:35.72},18:{.995:6.26,.99:7.01,.975:8.23,.95:9.39,.9:10.87,.5:17.34,.1:25.99,.05:28.87,.025:31.53,.01:34.81,.005:37.16},19:{.995:6.84,.99:7.63,.975:8.91,.95:10.12,.9:11.65,.5:18.34,.1:27.2,.05:30.14,.025:32.85,.01:36.19,.005:38.58},20:{.995:7.43,.99:8.26,.975:9.59,.95:10.85,.9:12.44,.5:19.34,.1:28.41,.05:31.41,.025:34.17,.01:37.57,.005:40},21:{.995:8.03,.99:8.9,.975:10.28,.95:11.59,.9:13.24,.5:20.34,.1:29.62,.05:32.67,.025:35.48,.01:38.93,.005:41.4},22:{.995:8.64,.99:9.54,.975:10.98,.95:12.34,.9:14.04,.5:21.34,.1:30.81,.05:33.92,.025:36.78,.01:40.29,.005:42.8},23:{.995:9.26,.99:10.2,.975:11.69,.95:13.09,.9:14.85,.5:22.34,.1:32.01,.05:35.17,.025:38.08,.01:41.64,.005:44.18},24:{.995:9.89,.99:10.86,.975:12.4,.95:13.85,.9:15.66,.5:23.34,.1:33.2,.05:36.42,.025:39.36,.01:42.98,.005:45.56},25:{.995:10.52,.99:11.52,.975:13.12,.95:14.61,.9:16.47,.5:24.34,.1:34.28,.05:37.65,.025:40.65,.01:44.31,.005:46.93},26:{.995:11.16,.99:12.2,.975:13.84,.95:15.38,.9:17.29,.5:25.34,.1:35.56,.05:38.89,.025:41.92,.01:45.64,.005:48.29},27:{.995:11.81,.99:12.88,.975:14.57,.95:16.15,.9:18.11,.5:26.34,.1:36.74,.05:40.11,.025:43.19,.01:46.96,.005:49.65},28:{.995:12.46,.99:13.57,.975:15.31,.95:16.93,.9:18.94,.5:27.34,.1:37.92,.05:41.34,.025:44.46,.01:48.28,.005:50.99},29:{.995:13.12,.99:14.26,.975:16.05,.95:17.71,.9:19.77,.5:28.34,.1:39.09,.05:42.56,.025:45.72,.01:49.59,.005:52.34},30:{.995:13.79,.99:14.95,.975:16.79,.95:18.49,.9:20.6,.5:29.34,.1:40.26,.05:43.77,.025:46.98,.01:50.89,.005:53.67},40:{.995:20.71,.99:22.16,.975:24.43,.95:26.51,.9:29.05,.5:39.34,.1:51.81,.05:55.76,.025:59.34,.01:63.69,.005:66.77},50:{.995:27.99,.99:29.71,.975:32.36,.95:34.76,.9:37.69,.5:49.33,.1:63.17,.05:67.5,.025:71.42,.01:76.15,.005:79.49},60:{.995:35.53,.99:37.48,.975:40.48,.95:43.19,.9:46.46,.5:59.33,.1:74.4,.05:79.08,.025:83.3,.01:88.38,.005:91.95},70:{.995:43.28,.99:45.44,.975:48.76,.95:51.74,.9:55.33,.5:69.33,.1:85.53,.05:90.53,.025:95.02,.01:100.42,.005:104.22},80:{.995:51.17,.99:53.54,.975:57.15,.95:60.39,.9:64.28,.5:79.33,.1:96.58,.05:101.88,.025:106.63,.01:112.33,.005:116.32},90:{.995:59.2,.99:61.75,.975:65.65,.95:69.13,.9:73.29,.5:89.33,.1:107.57,.05:113.14,.025:118.14,.01:124.12,.005:128.3},100:{.995:67.33,.99:70.06,.975:74.22,.95:77.93,.9:82.36,.5:99.33,.1:118.5,.05:124.34,.025:129.56,.01:135.81,.005:140.17}};var Ui=Math.sqrt(2*Math.PI),$i={gaussian:function(t){return Math.exp(-.5*t*t)/Ui}},zi={nrd:function(t){var n=Li(t),r=bi(t);return"number"==typeof r&&(n=Math.min(n,r/1.34)),1.06*n*Math.pow(t.length,-.2)}};function Wi(t,n,r){var e,i;if(void 0===n)e=$i.gaussian;else if("string"==typeof n){if(!$i[n])throw new Error('Unknown kernel "'+n+'"');e=$i[n]}else e=n;if(void 0===r)i=zi.nrd(t);else if("string"==typeof r){if(!zi[r])throw new Error('Unknown bandwidth method "'+r+'"');i=zi[r](t)}else i=r;return function(n){var r=0,o=0;for(r=0;r<t.length;r++)o+=e((n-t[r])/i);return o/i/t.length}}var Ji=Math.sqrt(2*Math.PI);function Xi(t){for(var n=t,r=t,e=1;e<15;e++)n+=r*=t*t/(2*e+1);return Math.round(1e4*(.5+n/Ji*Math.exp(-t*t/2)))/1e4}for(var Yi=[],Ki=0;Ki<=3.09;Ki+=.01)Yi.push(Xi(Ki));function Zi(t){var n=1/(1+.5*Math.abs(t)),r=n*Math.exp(-t*t+((((((((.17087277*n-.82215223)*n+1.48851587)*n-1.13520398)*n+.27886807)*n-.18628806)*n+.09678418)*n+.37409196)*n+1.00002368)*n-1.26551223);return t>=0?1-r:r-1}function Qi(t){var n=8*(Math.PI-3)/(3*Math.PI*(4-Math.PI)),r=Math.sqrt(Math.sqrt(Math.pow(2/(Math.PI*n)+Math.log(1-t*t)/2,2)-Math.log(1-t*t)/n)-(2/(Math.PI*n)+Math.log(1-t*t)/2));return t>=0?r:-r}function to(t){if("number"==typeof t)return t<0?-1:0===t?0:1;throw new TypeError("not a number")}function no(t,n){for(var r=0,e=0;e<t.length;e++){var i=t[e]-n[e];r+=i*i}return Math.sqrt(r)}function ro(t,n){return t.map((function(t){for(var r=Number.MAX_VALUE,e=-1,i=0;i<n.length;i++){var o=no(t,n[i]);o<r&&(r=o,e=i)}return e}))}function eo(t,n,r){for(var e=t[0].length,i=Pi(r,e),o=Array(r).fill(0),a=t.length,s=0;s<a;s++){for(var u=t[s],l=n[s],c=i[l],f=0;f<e;f++)c[f]+=u[f];o[l]+=1}for(var h=0;h<r;h++){if(0===o[h])throw new Error("Centroid "+h+" has no friends");for(var p=i[h],g=0;g<e;g++)p[g]/=o[h]}return i}function io(t,n){for(var r=0,e=0;e<t.length;e++)r+=no(t[e],n[e]);return r}function oo(t,n){if(t.length!==n.length)throw new Error("must have exactly as many labels as points");for(var r=function(t){for(var n=1+ci(t),r=Array(n),e=0;e<t.length;e++){var i=t[e];void 0===r[i]&&(r[i]=[]),r[i].push(e)}return r}(n),e=function(t){for(var n=t.length,r=Pi(n,n),e=0;e<n;e++)for(var i=0;i<e;i++)r[e][i]=no(t[e],t[i]),r[i][e]=r[e][i];return r}(t),i=[],o=0;o<t.length;o++){var a=0;if(r[n[o]].length>1){var s=so(o,r[n[o]],e),u=ao(o,n,r,e);a=(u-s)/Math.max(s,u)}i.push(a)}return i}function ao(t,n,r,e){for(var i=n[t],o=Number.MAX_VALUE,a=0;a<r.length;a++)if(a!==i){var s=so(t,r[a],e);s<o&&(o=s)}return o}function so(t,n,r){for(var e=0,i=0;i<n.length;i++)e+=r[t][n[i]];return e/n.length}function uo(t,n){return 0===t&&0===n?0:Math.abs((t-n)/n)}var lo,co=Object.freeze({__proto__:null,BayesianClassifier:Ti,PerceptronModel:Ri,addToMean:function(t,n,r){return t+(r-t)/(n+1)},approxEqual:function(t,n,r){return void 0===r&&(r=qi),uo(t,n)<=r},average:ei,averageSimple:ji,bayesian:Ti,bernoulliDistribution:function(t){if(t<0||t>1)throw new Error("bernoulliDistribution requires probability to be between 0 and 1 inclusive");return[1-t,t]},binomialDistribution:function(t,n){if(!(n<0||n>1||t<=0||t%1!=0)){var r=0,e=0,i=[],o=1;do{i[r]=o*Math.pow(n,r)*Math.pow(1-n,t-r),e+=i[r],o=o*(t-++r+1)/r}while(e<1-qi);return i}},bisect:function(t,n,r,e,i){if("function"!=typeof t)throw new TypeError("func must be a function");for(var o=0;o<e;o++){var a=(n+r)/2;if(0===t(a)||Math.abs((r-n)/2)<i)return a;to(t(a))===to(t(n))?n=a:r=a}throw new Error("maximum number of iterations exceeded")},chiSquaredDistributionTable:Hi,chiSquaredGoodnessOfFit:function(t,n,r){for(var e=0,i=n(ei(t)),o=[],a=[],s=0;s<t.length;s++)void 0===o[t[s]]&&(o[t[s]]=0),o[t[s]]++;for(var u=0;u<o.length;u++)void 0===o[u]&&(o[u]=0);for(var l in i)l in o&&(a[+l]=i[l]*t.length);for(var c=a.length-1;c>=0;c--)a[c]<3&&(a[c-1]+=a[c],a.pop(),o[c-1]+=o[c],o.pop());for(var f=0;f<o.length;f++)e+=Math.pow(o[f]-a[f],2)/a[f];var h=o.length-1-1;return Hi[h][r]<e},chunk:function(t,n){var r=[];if(n<1)throw new Error("chunk size must be a positive number");if(Math.floor(n)!==n)throw new Error("chunk size must be an integer");for(var e=0;e<t.length;e+=n)r.push(t.slice(e,e+n));return r},ckmeans:function(t,n){if(n>t.length)throw new Error("cannot generate more classes than there are data values");var r=ui(t);if(1===ki(r))return[r];var e=Pi(n,r.length),i=Pi(n,r.length);!function(t,n,r){for(var e=n[0].length,i=t[Math.floor(e/2)],o=[],a=[],s=0,u=void 0;s<e;++s)u=t[s]-i,0===s?(o.push(u),a.push(u*u)):(o.push(o[s-1]+u),a.push(a[s-1]+u*u)),n[0][s]=Ai(0,s,o,a),r[0][s]=0;for(var l=1;l<n.length;++l)Fi(l<n.length-1?l:e-1,e-1,l,n,r,o,a)}(r,e,i);for(var o=[],a=i[0].length-1,s=i.length-1;s>=0;s--){var u=i[s][a];o[s]=r.slice(u,a+1),s>0&&(a=u-1)}return o},coefficientOfVariation:function(t){return Li(t)/ei(t)},combinations:function t(n,r){var e,i,o,a,s=[];for(e=0;e<n.length;e++)if(1===r)s.push([n[e]]);else for(o=t(n.slice(e+1,n.length),r-1),i=0;i<o.length;i++)(a=o[i]).unshift(n[e]),s.push(a);return s},combinationsReplacement:function t(n,r){for(var e=[],i=0;i<n.length;i++)if(1===r)e.push([n[i]]);else for(var o=t(n.slice(i,n.length),r-1),a=0;a<o.length;a++)e.push([n[i]].concat(o[a]));return e},combineMeans:Oi,combineVariances:function(t,n,r,e,i,o){var a=Oi(n,r,i,o);return(r*(t+Math.pow(n-a,2))+o*(e+Math.pow(i-a,2)))/(r+o)},cumulativeStdLogisticProbability:function(t){return 1/(Math.exp(-t)+1)},cumulativeStdNormalProbability:function(t){var n=Math.abs(t),r=Math.min(Math.round(100*n),Yi.length-1);return t>=0?Yi[r]:+(1-Yi[r]).toFixed(4)},epsilon:qi,equalIntervalBreaks:function(t,n){if(t.length<2)return t;for(var r=li(t),e=ci(t),i=[r],o=(e-r)/n,a=1;a<n;a++)i.push(i[0]+o*a);return i.push(e),i},erf:Zi,errorFunction:Zi,extent:function(t){if(0===t.length)throw new Error("extent requires at least one data point");for(var n=t[0],r=t[0],e=1;e<t.length;e++)t[e]>r&&(r=t[e]),t[e]<n&&(n=t[e]);return[n,r]},extentSorted:function(t){return[t[0],t[t.length-1]]},factorial:Bi,gamma:function t(n){if(Number.isInteger(n))return n<=0?NaN:Bi(n-1);if(--n<0)return Math.PI/(Math.sin(Math.PI*-n)*t(-n));var r=n+1/4;return Math.pow(n/Math.E,n)*Math.sqrt(2*Math.PI*(n+1/6))*(1+1/144/Math.pow(r,2)-1/12960/Math.pow(r,3)-257/207360/Math.pow(r,4)-52/2612736/Math.pow(r,5)+5741173/9405849600/Math.pow(r,6)+37529/18811699200/Math.pow(r,7))},gammaln:function(t){if(t<=0)return 1/0;t--;for(var n=Gi[0],r=1;r<15;r++)n+=Gi[r]/(t+r);var e=5.2421875+t;return Ii+Math.log(n)-e+(t+.5)*Math.log(e)},geometricMean:function(t){if(0===t.length)throw new Error("geometricMean requires at least one data point");for(var n=1,r=0;r<t.length;r++){if(t[r]<0)throw new Error("geometricMean requires only non-negative numbers as input");n*=t[r]}return Math.pow(n,1/t.length)},harmonicMean:function(t){if(0===t.length)throw new Error("harmonicMean requires at least one data point");for(var n=0,r=0;r<t.length;r++){if(t[r]<=0)throw new Error("harmonicMean requires only positive numbers as input");n+=1/t[r]}return t.length/n},interquartileRange:bi,inverseErrorFunction:Qi,iqr:bi,jenks:function(t,n){if(n>t.length)return null;var r=function(t,n){var r,e,i=[],o=[],a=0;for(r=0;r<t.length+1;r++){var s=[],u=[];for(e=0;e<n+1;e++)s.push(0),u.push(0);i.push(s),o.push(u)}for(r=1;r<n+1;r++)for(i[1][r]=1,o[1][r]=0,e=2;e<t.length+1;e++)o[e][r]=1/0;for(var l=2;l<t.length+1;l++){for(var c=0,f=0,h=0,p=0,g=1;g<l+1;g++){var d=l-g+1,v=t[d-1];if(a=(f+=v*v)-(c+=v)*c/++h,0!=(p=d-1))for(e=2;e<n+1;e++)o[l][e]>=a+o[p][e-1]&&(i[l][e]=d,o[l][e]=a+o[p][e-1])}i[l][1]=1,o[l][1]=a}return{lowerClassLimits:i,varianceCombinations:o}}(t=t.slice().sort((function(t,n){return t-n})),n);return function(t,n,r){var e=t.length,i=[],o=r;for(i[r]=t[t.length-1];o>0;)i[o-1]=t[n[e][o]-1],e=n[e][o]-1,o--;return i}(t,r.lowerClassLimits,n)},kMeansCluster:function(t,n,r){void 0===r&&(r=Math.random);for(var e=null,i=_i(t,n,r),o=null,a=Number.MAX_VALUE;0!==a;)e=i,a=io(i=eo(t,o=ro(t,i),n),e);return{labels:o,centroids:i}},kde:Wi,kernelDensityEstimation:Wi,linearRegression:function(t){var n,r,e=t.length;if(1===e)n=0,r=t[0][1];else{for(var i,o,a,s=0,u=0,l=0,c=0,f=0;f<e;f++)s+=o=(i=t[f])[0],u+=a=i[1],l+=o*o,c+=o*a;r=u/e-(n=(e*c-s*u)/(e*l-s*s))*s/e}return{m:n,b:r}},linearRegressionLine:function(t){return function(n){return t.b+t.m*n}},logAverage:function(t){if(0===t.length)throw new Error("logAverage requires at least one data point");for(var n=0,r=0;r<t.length;r++){if(t[r]<0)throw new Error("logAverage requires only non-negative numbers as input");n+=Math.log(t[r])}return Math.exp(n/t.length)},logit:function(t){if(t<=0||t>=1)throw new Error("p must be strictly between zero and one");return Math.log(t/(1-t))},mad:xi,max:ci,maxSorted:function(t){return t[t.length-1]},mean:ei,meanSimple:ji,median:Mi,medianAbsoluteDeviation:xi,medianSorted:function(t){return hi(t,.5)},min:li,minSorted:function(t){return t[0]},mode:function(t){return si(ui(t))},modeFast:function(t){for(var n,r=new Map,e=0,i=0;i<t.length;i++){var o=r.get(t[i]);void 0===o?o=1:o++,o>e&&(n=t[i],e=o),r.set(t[i],o)}if(0===e)throw new Error("mode requires at last one data point");return n},modeSorted:si,numericSort:ui,perceptron:Ri,permutationTest:function(t,n,r,e,i){if(void 0===e&&(e=1e4),void 0===r&&(r="two_side"),"two_side"!==r&&"greater"!==r&&"less"!==r)throw new Error("`alternative` must be either 'two_side', 'greater', or 'less'.");for(var o=ei(t)-ei(n),a=new Array(e),s=t.concat(n),u=Math.floor(s.length/2),l=0;l<e;l++){Ei(s,i);var c=s.slice(0,u),f=s.slice(u,s.length),h=ei(c)-ei(f);a[l]=h}var p=0;if("two_side"===r)for(var g=0;g<=e;g++)Math.abs(a[g])>=Math.abs(o)&&(p+=1);else if("greater"===r)for(var d=0;d<=e;d++)a[d]>=o&&(p+=1);else for(var v=0;v<=e;v++)a[v]<=o&&(p+=1);return p/e},permutationsHeap:function(t){for(var n=new Array(t.length),r=[t.slice()],e=0;e<t.length;e++)n[e]=0;for(var i=0;i<t.length;)if(n[i]<i){var o=0;i%2!=0&&(o=n[i]);var a=t[o];t[o]=t[i],t[i]=a,r.push(t.slice()),n[i]++,i=0}else n[i]=0,i++;return r},poissonDistribution:function(t){if(!(t<=0)){var n=0,r=0,e=[],i=1;do{e[n]=Math.exp(-t)*Math.pow(t,n)/i,r+=e[n],i*=++n}while(r<1-qi);return e}},probit:function(t){return 0===t?t=qi:t>=1&&(t=1-qi),Math.sqrt(2)*Qi(2*t-1)},product:function(t){for(var n=1,r=0;r<t.length;r++)n*=t[r];return n},quantile:di,quantileRank:function(t,n){return wi(ui(t),n)},quantileRankSorted:wi,quantileSorted:hi,quickselect:pi,rSquared:function(t,n){if(t.length<2)return 1;for(var r=0,e=0;e<t.length;e++)r+=t[e][1];for(var i=r/t.length,o=0,a=0;a<t.length;a++)o+=Math.pow(i-t[a][1],2);for(var s=0,u=0;u<t.length;u++)s+=Math.pow(t[u][1]-n(t[u][0]),2);return 1-s/o},relativeError:uo,rms:Vi,rootMeanSquare:Vi,sample:_i,sampleCorrelation:Ni,sampleCovariance:Di,sampleKurtosis:function(t){var n=t.length;if(n<4)throw new Error("sampleKurtosis requires at least four data points");for(var r,e=ei(t),i=0,o=0,a=0;a<n;a++)i+=(r=t[a]-e)*r,o+=r*r*r*r;return(n-1)/((n-2)*(n-3))*(n*(n+1)*o/(i*i)-3*(n-1))},sampleRankCorrelation:function(t,n){for(var r=t.map((function(t,n){return[t,n]})).sort((function(t,n){return t[0]-n[0]})).map((function(t){return t[1]})),e=n.map((function(t,n){return[t,n]})).sort((function(t,n){return t[0]-n[0]})).map((function(t){return t[1]})),i=Array(r.length),o=Array(r.length),a=0;a<r.length;a++)i[r[a]]=a,o[e[a]]=a;return Ni(i,o)},sampleSkewness:function(t){if(t.length<3)throw new Error("sampleSkewness requires at least three data points");for(var n,r=ei(t),e=0,i=0,o=0;o<t.length;o++)e+=(n=t[o]-r)*n,i+=n*n*n;var a=t.length-1,s=Math.sqrt(e/a),u=t.length;return u*i/((u-1)*(u-2)*Math.pow(s,3))},sampleStandardDeviation:Li,sampleVariance:Ci,sampleWithReplacement:function(t,n,r){if(0===t.length)return[];r=r||Math.random;for(var e=t.length,i=[],o=0;o<n;o++){var a=Math.floor(r()*e);i.push(t[a])}return i},shuffle:Si,shuffleInPlace:Ei,sign:to,silhouette:oo,silhouetteMetric:function(t,n){return ci(oo(t,n))},standardDeviation:ai,standardNormalTable:Yi,subtractFromMean:function(t,n,r){return(t*n-r)/(n-1)},sum:ri,sumNthPowerDeviations:ii,sumSimple:fi,tTest:function(t,n){return(ei(t)-n)/(ai(t)/Math.sqrt(t.length))},tTestTwoSample:function(t,n,r){var e=t.length,i=n.length;if(!e||!i)return null;r||(r=0);var o=ei(t),a=ei(n),s=Ci(t),u=Ci(n);if("number"==typeof o&&"number"==typeof a&&"number"==typeof s&&"number"==typeof u){var l=((e-1)*s+(i-1)*u)/(e+i-2);return(o-a-r)/Math.sqrt(l*(1/e+1/i))}},uniqueCountSorted:ki,variance:oi,wilcoxonRankSum:function(t,n){if(!t.length||!n.length)throw new Error("Neither sample can be empty");for(var r=t.map((function(t){return{label:"x",value:t}})).concat(n.map((function(t){return{label:"y",value:t}}))).sort((function(t,n){return t.value-n.value})),e=0;e<r.length;e++)r[e].rank=e;for(var i=[r[0].rank],o=1;o<r.length;o++)r[o].value===r[o-1].value?(i.push(r[o].rank),o===r.length-1&&a(r,i)):i.length>1?a(r,i):i=[r[o].rank];function a(t,n){for(var r=(n[0]+n[n.length-1])/2,e=0;e<n.length;e++)t[n[e]].rank=r}for(var s=0,u=0;u<r.length;u++){var l=r[u];"x"===l.label&&(s+=l.rank+1)}return s},zScore:function(t,n,r){return(t-n)/r}});!function(t){t.DSV="dsv",t.TREE="tree",t.GEO="geo",t.BYTE="bytejson",t.HEX="hex",t.GRAPH="graph",t.TABLE="table",t.GEO_GRATICULE="geo-graticule"}(lo||(lo={}));const fo={as:[],fields:[],groupBy:null,operations:["count","max","min","average","sum"]},ho={count:t=>t.length,distinct:(t,n)=>A(t.map((t=>+t[n]))).length};["max","mean","median","min","mode","product","standardDeviation","sum","sumSimple","variance"].forEach((t=>{ho[t]=(n,r)=>{let e=n.map((t=>+t[r]));return m(e)&&m(e[0])&&(e=F(e)),co[t](e)}})),ho.average=ho.mean;const po=(t,n)=>{var r,e;if(!(null==n?void 0:n.fields))return t;if(0===t.length)return t;const i=n.fields,o=t[0],a={},s=[];for(const n in i)if(Object.prototype.hasOwnProperty.call(i,n)){const u=i[n];if(!u.type){let e=o;n in o||(e=null!==(r=t.find((t=>n in t)))&&void 0!==r?r:o),u.type="number"==typeof e[n]?"linear":"ordinal"}let l;if("number"==typeof u.sortIndex&&(l={key:n,type:u.type,index:u.sortIndex,sortIndex:{},sortIndexCount:0,sortReverse:!0===u.sortReverse},s.push(l)),(null===(e=u.domain)||void 0===e?void 0:e.length)>0)if("ordinal"===u.type){u._domainCache={},a[n]=u;const t={};u.domain.forEach(((n,r)=>{t[n]=r,u._domainCache[n]=r})),l&&(l.sortIndex=t,l.sortIndexCount=u.domain.length)}else u.domain.length>=2&&(a[n]=u)}return Object.keys(a).length>0&&(t=t.filter((t=>{for(const n in a){const r=a[n];if("ordinal"===r.type){if(!(t[n]in r._domainCache))return!1}else if(r.domain[0]>t[n]||r.domain[1]<t[n])return!1}return!0}))),s.sort(((t,n)=>t.index-n.index)),t.sort(((t,n)=>function(t,n,r){for(let e=0;e<r.length;e++){const i=r[e];let o=0;if("ordinal"===i.type?(void 0===i.sortIndex[n[i.key]]&&(i.sortIndex[n[i.key]]=i.sortIndexCount++),void 0===i.sortIndex[t[i.key]]&&(i.sortIndex[t[i.key]]=i.sortIndexCount++),o=i.sortIndex[t[i.key]]-i.sortIndex[n[i.key]]):"linear"===i.type&&(o=t[i.key]-n[i.key]),i.sortReverse&&(o=-o),0!==o)return o}return 0}(t,n,s))),t};var go={},vo={};function yo(t){return new Function("d","return {"+t.map((function(t,n){return JSON.stringify(t)+": d["+n+'] || ""'})).join(",")+"}")}function mo(t){var n=Object.create(null),r=[];return t.forEach((function(t){for(var e in t)e in n||r.push(n[e]=e)})),r}function wo(t,n){var r=t+"",e=r.length;return e<n?new Array(n-e+1).join(0)+r:r}function bo(t){var n,r=t.getUTCHours(),e=t.getUTCMinutes(),i=t.getUTCSeconds(),o=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":((n=t.getUTCFullYear())<0?"-"+wo(-n,6):n>9999?"+"+wo(n,6):wo(n,4))+"-"+wo(t.getUTCMonth()+1,2)+"-"+wo(t.getUTCDate(),2)+(o?"T"+wo(r,2)+":"+wo(e,2)+":"+wo(i,2)+"."+wo(o,3)+"Z":i?"T"+wo(r,2)+":"+wo(e,2)+":"+wo(i,2)+"Z":e||r?"T"+wo(r,2)+":"+wo(e,2)+"Z":"")}function Mo(t){var n=new RegExp('["'+t+"\n\r]"),r=t.charCodeAt(0);function e(t,n){var e,i=[],o=t.length,a=0,s=0,u=o<=0,l=!1;function c(){if(u)return vo;if(l)return l=!1,go;var n,e,i=a;if(34===t.charCodeAt(i)){for(;a++<o&&34!==t.charCodeAt(a)||34===t.charCodeAt(++a););return(n=a)>=o?u=!0:10===(e=t.charCodeAt(a++))?l=!0:13===e&&(l=!0,10===t.charCodeAt(a)&&++a),t.slice(i+1,n-1).replace(/""/g,'"')}for(;a<o;){if(10===(e=t.charCodeAt(n=a++)))l=!0;else if(13===e)l=!0,10===t.charCodeAt(a)&&++a;else if(e!==r)continue;return t.slice(i,n)}return u=!0,t.slice(i,o)}for(10===t.charCodeAt(o-1)&&--o,13===t.charCodeAt(o-1)&&--o;(e=c())!==vo;){for(var f=[];e!==go&&e!==vo;)f.push(e),e=c();n&&null==(f=n(f,s++))||i.push(f)}return i}function i(n,r){return n.map((function(n){return r.map((function(t){return a(n[t])})).join(t)}))}function o(n){return n.map(a).join(t)}function a(t){return null==t?"":t instanceof Date?bo(t):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,n){var r,i,o=e(t,(function(t,e){if(r)return r(t,e-1);i=t,r=n?function(t,n){var r=yo(t);return function(e,i){return n(r(e),i,t)}}(t,n):yo(t)}));return o.columns=i||[],o},parseRows:e,format:function(n,r){return null==r&&(r=mo(n)),[r.map(a).join(t)].concat(i(n,r)).join("\n")},formatBody:function(t,n){return null==n&&(n=mo(t)),i(t,n).join("\n")},formatRows:function(t){return t.map(o).join("\n")},formatRow:o,formatValue:a}}var xo=Mo(",").parse,Eo=Mo("\t").parse;const So={delimiter:","};var _o,Po,ko,Ao,Fo,Do=function(t){Ao=2,Fo=Math.pow(10,6),ko=null,_o=[],Po=[];var n=t.readFields(Lo,{});return _o=null,n},Co=["Point","MultiPoint","LineString","MultiLineString","Polygon","MultiPolygon","GeometryCollection"];function Lo(t,n,r){1===t?_o.push(r.readString()):2===t?Ao=r.readVarint():3===t?Fo=Math.pow(10,r.readVarint()):4===t?function(t,n){n.type="FeatureCollection",n.features=[],t.readMessage(jo,n)}(r,n):5===t?No(r,n):6===t&&Oo(r,n)}function No(t,n){n.type="Feature";var r=t.readMessage(Vo,n);return"geometry"in r||(r.geometry=null),r}function Oo(t,n){return n.type="Point",t.readMessage(To,n)}function jo(t,n,r){1===t?n.features.push(No(r,{})):13===t?Po.push(Ro(r)):15===t&&qo(r,n)}function Vo(t,n,r){1===t?n.geometry=Oo(r,{}):11===t?n.id=r.readString():12===t?n.id=r.readSVarint():13===t?Po.push(Ro(r)):14===t?n.properties=qo(r,{}):15===t&&qo(r,n)}function To(t,n,r){1===t?n.type=Co[r.readVarint()]:2===t?ko=r.readPackedVarint():3===t?function(t,n,r){"Point"===r?t.coordinates=function(t){var n=t.readVarint()+t.pos,r=[];for(;t.pos<n;)r.push(t.readSVarint()/Fo);return r}(n):"MultiPoint"===r||"LineString"===r?t.coordinates=Go(n):"MultiLineString"===r?t.coordinates=Io(n):"Polygon"===r?t.coordinates=Io(n,!0):"MultiPolygon"===r&&(t.coordinates=function(t){var n=t.readVarint()+t.pos;if(!ko)return[[Bo(t,n,null,!0)]];for(var r=[],e=1,i=0;i<ko[0];i++){for(var o=[],a=0;a<ko[e];a++)o.push(Bo(t,n,ko[e+1+a],!0));e+=ko[e]+1,r.push(o)}return ko=null,r}(n))}(n,r,n.type):4===t?(n.geometries=n.geometries||[],n.geometries.push(Oo(r,{}))):13===t?Po.push(Ro(r)):15===t&&qo(r,n)}function Ro(t){for(var n=t.readVarint()+t.pos,r=null;t.pos<n;){var e=t.readVarint()>>3;1===e?r=t.readString():2===e?r=t.readDouble():3===e?r=t.readVarint():4===e?r=-t.readVarint():5===e?r=t.readBoolean():6===e&&(r=JSON.parse(t.readString()))}return r}function qo(t,n){for(var r=t.readVarint()+t.pos;t.pos<r;)n[_o[t.readVarint()]]=Po[t.readVarint()];return Po=[],n}function Bo(t,n,r,e){var i,o,a=0,s=[],u=[];for(o=0;o<Ao;o++)u[o]=0;for(;r?a<r:t.pos<n;){for(i=[],o=0;o<Ao;o++)u[o]+=t.readSVarint(),i[o]=u[o]/Fo;s.push(i),a++}return e&&s.push(s[0]),s}function Go(t){return Bo(t,t.readVarint()+t.pos)}function Io(t,n){var r=t.readVarint()+t.pos;if(!ko)return[Bo(t,r,null,n)];for(var e=[],i=0;i<ko.length;i++)e.push(Bo(t,r,ko[i],n));return ko=null,e}var Ho=Do,Uo={
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
read:function(t,n,r,e,i){var o,a,s=8*i-e-1,u=(1<<s)-1,l=u>>1,c=-7,f=r?i-1:0,h=r?-1:1,p=t[n+f];for(f+=h,o=p&(1<<-c)-1,p>>=-c,c+=s;c>0;o=256*o+t[n+f],f+=h,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=e;c>0;a=256*a+t[n+f],f+=h,c-=8);if(0===o)o=1-l;else{if(o===u)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,e),o-=l}return(p?-1:1)*a*Math.pow(2,o-e)},write:function(t,n,r,e,i,o){var a,s,u,l=8*o-i-1,c=(1<<l)-1,f=c>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=e?0:o-1,g=e?1:-1,d=n<0||0===n&&1/n<0?1:0;for(n=Math.abs(n),isNaN(n)||n===1/0?(s=isNaN(n)?1:0,a=c):(a=Math.floor(Math.log(n)/Math.LN2),n*(u=Math.pow(2,-a))<1&&(a--,u*=2),(n+=a+f>=1?h/u:h*Math.pow(2,1-f))*u>=2&&(a++,u/=2),a+f>=c?(s=0,a=c):a+f>=1?(s=(n*u-1)*Math.pow(2,i),a+=f):(s=n*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;t[r+p]=255&s,p+=g,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;t[r+p]=255&a,p+=g,a/=256,l-=8);t[r+p-g]|=128*d}},$o=Wo,zo=Uo;function Wo(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.pos=0,this.type=0,this.length=this.buf.length}Wo.Varint=0,Wo.Fixed64=1,Wo.Bytes=2,Wo.Fixed32=5;var Jo=4294967296,Xo=1/Jo,Yo="undefined"==typeof TextDecoder?null:new TextDecoder("utf8");function Ko(t){return t.type===Wo.Bytes?t.readVarint()+t.pos:t.pos+1}function Zo(t,n,r){return r?4294967296*n+(t>>>0):4294967296*(n>>>0)+(t>>>0)}function Qo(t,n,r){var e=n<=16383?1:n<=2097151?2:n<=268435455?3:Math.floor(Math.log(n)/(7*Math.LN2));r.realloc(e);for(var i=r.pos-1;i>=t;i--)r.buf[i+e]=r.buf[i]}function ta(t,n){for(var r=0;r<t.length;r++)n.writeVarint(t[r])}function na(t,n){for(var r=0;r<t.length;r++)n.writeSVarint(t[r])}function ra(t,n){for(var r=0;r<t.length;r++)n.writeFloat(t[r])}function ea(t,n){for(var r=0;r<t.length;r++)n.writeDouble(t[r])}function ia(t,n){for(var r=0;r<t.length;r++)n.writeBoolean(t[r])}function oa(t,n){for(var r=0;r<t.length;r++)n.writeFixed32(t[r])}function aa(t,n){for(var r=0;r<t.length;r++)n.writeSFixed32(t[r])}function sa(t,n){for(var r=0;r<t.length;r++)n.writeFixed64(t[r])}function ua(t,n){for(var r=0;r<t.length;r++)n.writeSFixed64(t[r])}function la(t,n){return(t[n]|t[n+1]<<8|t[n+2]<<16)+16777216*t[n+3]}function ca(t,n,r){t[r]=n,t[r+1]=n>>>8,t[r+2]=n>>>16,t[r+3]=n>>>24}function fa(t,n){return(t[n]|t[n+1]<<8|t[n+2]<<16)+(t[n+3]<<24)}Wo.prototype={destroy:function(){this.buf=null},readFields:function(t,n,r){for(r=r||this.length;this.pos<r;){var e=this.readVarint(),i=e>>3,o=this.pos;this.type=7&e,t(i,n,this),this.pos===o&&this.skip(e)}return n},readMessage:function(t,n){return this.readFields(t,n,this.readVarint()+this.pos)},readFixed32:function(){var t=la(this.buf,this.pos);return this.pos+=4,t},readSFixed32:function(){var t=fa(this.buf,this.pos);return this.pos+=4,t},readFixed64:function(){var t=la(this.buf,this.pos)+la(this.buf,this.pos+4)*Jo;return this.pos+=8,t},readSFixed64:function(){var t=la(this.buf,this.pos)+fa(this.buf,this.pos+4)*Jo;return this.pos+=8,t},readFloat:function(){var t=zo.read(this.buf,this.pos,!0,23,4);return this.pos+=4,t},readDouble:function(){var t=zo.read(this.buf,this.pos,!0,52,8);return this.pos+=8,t},readVarint:function(t){var n,r,e=this.buf;return n=127&(r=e[this.pos++]),r<128?n:(n|=(127&(r=e[this.pos++]))<<7,r<128?n:(n|=(127&(r=e[this.pos++]))<<14,r<128?n:(n|=(127&(r=e[this.pos++]))<<21,r<128?n:function(t,n,r){var e,i,o=r.buf;if(i=o[r.pos++],e=(112&i)>>4,i<128)return Zo(t,e,n);if(i=o[r.pos++],e|=(127&i)<<3,i<128)return Zo(t,e,n);if(i=o[r.pos++],e|=(127&i)<<10,i<128)return Zo(t,e,n);if(i=o[r.pos++],e|=(127&i)<<17,i<128)return Zo(t,e,n);if(i=o[r.pos++],e|=(127&i)<<24,i<128)return Zo(t,e,n);if(i=o[r.pos++],e|=(1&i)<<31,i<128)return Zo(t,e,n);throw new Error("Expected varint not more than 10 bytes")}(n|=(15&(r=e[this.pos]))<<28,t,this))))},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var t=this.readVarint();return t%2==1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos,n=this.pos;return this.pos=t,t-n>=12&&Yo?function(t,n,r){return Yo.decode(t.subarray(n,r))}(this.buf,n,t):function(t,n,r){var e="",i=n;for(;i<r;){var o,a,s,u=t[i],l=null,c=u>239?4:u>223?3:u>191?2:1;if(i+c>r)break;1===c?u<128&&(l=u):2===c?128==(192&(o=t[i+1]))&&(l=(31&u)<<6|63&o)<=127&&(l=null):3===c?(o=t[i+1],a=t[i+2],128==(192&o)&&128==(192&a)&&((l=(15&u)<<12|(63&o)<<6|63&a)<=2047||l>=55296&&l<=57343)&&(l=null)):4===c&&(o=t[i+1],a=t[i+2],s=t[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&((l=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s)<=65535||l>=1114112)&&(l=null)),null===l?(l=65533,c=1):l>65535&&(l-=65536,e+=String.fromCharCode(l>>>10&1023|55296),l=56320|1023&l),e+=String.fromCharCode(l),i+=c}return e}(this.buf,n,t)},readBytes:function(){var t=this.readVarint()+this.pos,n=this.buf.subarray(this.pos,t);return this.pos=t,n},readPackedVarint:function(t,n){if(this.type!==Wo.Bytes)return t.push(this.readVarint(n));var r=Ko(this);for(t=t||[];this.pos<r;)t.push(this.readVarint(n));return t},readPackedSVarint:function(t){if(this.type!==Wo.Bytes)return t.push(this.readSVarint());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readSVarint());return t},readPackedBoolean:function(t){if(this.type!==Wo.Bytes)return t.push(this.readBoolean());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readBoolean());return t},readPackedFloat:function(t){if(this.type!==Wo.Bytes)return t.push(this.readFloat());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readFloat());return t},readPackedDouble:function(t){if(this.type!==Wo.Bytes)return t.push(this.readDouble());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readDouble());return t},readPackedFixed32:function(t){if(this.type!==Wo.Bytes)return t.push(this.readFixed32());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){if(this.type!==Wo.Bytes)return t.push(this.readSFixed32());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){if(this.type!==Wo.Bytes)return t.push(this.readFixed64());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){if(this.type!==Wo.Bytes)return t.push(this.readSFixed64());var n=Ko(this);for(t=t||[];this.pos<n;)t.push(this.readSFixed64());return t},skip:function(t){var n=7&t;if(n===Wo.Varint)for(;this.buf[this.pos++]>127;);else if(n===Wo.Bytes)this.pos=this.readVarint()+this.pos;else if(n===Wo.Fixed32)this.pos+=4;else{if(n!==Wo.Fixed64)throw new Error("Unimplemented type: "+n);this.pos+=8}},writeTag:function(t,n){this.writeVarint(t<<3|n)},realloc:function(t){for(var n=this.length||16;n<this.pos+t;)n*=2;if(n!==this.length){var r=new Uint8Array(n);r.set(this.buf),this.buf=r,this.length=n}},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4),ca(this.buf,t,this.pos),this.pos+=4},writeSFixed32:function(t){this.realloc(4),ca(this.buf,t,this.pos),this.pos+=4},writeFixed64:function(t){this.realloc(8),ca(this.buf,-1&t,this.pos),ca(this.buf,Math.floor(t*Xo),this.pos+4),this.pos+=8},writeSFixed64:function(t){this.realloc(8),ca(this.buf,-1&t,this.pos),ca(this.buf,Math.floor(t*Xo),this.pos+4),this.pos+=8},writeVarint:function(t){(t=+t||0)>268435455||t<0?function(t,n){var r,e;t>=0?(r=t%4294967296|0,e=t/4294967296|0):(e=~(-t/4294967296),4294967295^(r=~(-t%4294967296))?r=r+1|0:(r=0,e=e+1|0));if(t>=0x10000000000000000||t<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");n.realloc(10),function(t,n,r){r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos]=127&t}(r,0,n),function(t,n){var r=(7&t)<<4;if(n.buf[n.pos++]|=r|((t>>>=3)?128:0),!t)return;if(n.buf[n.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(n.buf[n.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(n.buf[n.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(n.buf[n.pos++]=127&t|((t>>>=7)?128:0),!t)return;n.buf[n.pos++]=127&t}(e,n)}(t,this):(this.realloc(4),this.buf[this.pos++]=127&t|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=t>>>7&127))))},writeSVarint:function(t){this.writeVarint(t<0?2*-t-1:2*t)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t),this.realloc(4*t.length),this.pos++;var n=this.pos;this.pos=function(t,n,r){for(var e,i,o=0;o<n.length;o++){if((e=n.charCodeAt(o))>55295&&e<57344){if(!i){e>56319||o+1===n.length?(t[r++]=239,t[r++]=191,t[r++]=189):i=e;continue}if(e<56320){t[r++]=239,t[r++]=191,t[r++]=189,i=e;continue}e=i-55296<<10|e-56320|65536,i=null}else i&&(t[r++]=239,t[r++]=191,t[r++]=189,i=null);e<128?t[r++]=e:(e<2048?t[r++]=e>>6|192:(e<65536?t[r++]=e>>12|224:(t[r++]=e>>18|240,t[r++]=e>>12&63|128),t[r++]=e>>6&63|128),t[r++]=63&e|128)}return r}(this.buf,t,this.pos);var r=this.pos-n;r>=128&&Qo(n,r,this),this.pos=n-1,this.writeVarint(r),this.pos+=r},writeFloat:function(t){this.realloc(4),zo.write(this.buf,t,this.pos,!0,23,4),this.pos+=4},writeDouble:function(t){this.realloc(8),zo.write(this.buf,t,this.pos,!0,52,8),this.pos+=8},writeBytes:function(t){var n=t.length;this.writeVarint(n),this.realloc(n);for(var r=0;r<n;r++)this.buf[this.pos++]=t[r]},writeRawMessage:function(t,n){this.pos++;var r=this.pos;t(n,this);var e=this.pos-r;e>=128&&Qo(r,e,this),this.pos=r-1,this.writeVarint(e),this.pos+=e},writeMessage:function(t,n,r){this.writeTag(t,Wo.Bytes),this.writeRawMessage(n,r)},writePackedVarint:function(t,n){n.length&&this.writeMessage(t,ta,n)},writePackedSVarint:function(t,n){n.length&&this.writeMessage(t,na,n)},writePackedBoolean:function(t,n){n.length&&this.writeMessage(t,ia,n)},writePackedFloat:function(t,n){n.length&&this.writeMessage(t,ra,n)},writePackedDouble:function(t,n){n.length&&this.writeMessage(t,ea,n)},writePackedFixed32:function(t,n){n.length&&this.writeMessage(t,oa,n)},writePackedSFixed32:function(t,n){n.length&&this.writeMessage(t,aa,n)},writePackedFixed64:function(t,n){n.length&&this.writeMessage(t,sa,n)},writePackedSFixed64:function(t,n){n.length&&this.writeMessage(t,ua,n)},writeBytesField:function(t,n){this.writeTag(t,Wo.Bytes),this.writeBytes(n)},writeFixed32Field:function(t,n){this.writeTag(t,Wo.Fixed32),this.writeFixed32(n)},writeSFixed32Field:function(t,n){this.writeTag(t,Wo.Fixed32),this.writeSFixed32(n)},writeFixed64Field:function(t,n){this.writeTag(t,Wo.Fixed64),this.writeFixed64(n)},writeSFixed64Field:function(t,n){this.writeTag(t,Wo.Fixed64),this.writeSFixed64(n)},writeVarintField:function(t,n){this.writeTag(t,Wo.Varint),this.writeVarint(n)},writeSVarintField:function(t,n){this.writeTag(t,Wo.Varint),this.writeSVarint(n)},writeStringField:function(t,n){this.writeTag(t,Wo.Bytes),this.writeString(n)},writeFloatField:function(t,n){this.writeTag(t,Wo.Fixed32),this.writeFloat(n)},writeDoubleField:function(t,n){this.writeTag(t,Wo.Fixed64),this.writeDouble(n)},writeBooleanField:function(t,n){this.writeVarintField(t,Boolean(n))}};var ha=o($o);function pa(t){if(!t)throw new Error("geojson is required");switch(t.type){case"Feature":return ga(t);case"FeatureCollection":return function(t){var n={type:"FeatureCollection"};return Object.keys(t).forEach((function(r){switch(r){case"type":case"features":return;default:n[r]=t[r]}})),n.features=t.features.map((function(t){return ga(t)})),n}(t);case"Point":case"LineString":case"Polygon":case"MultiPoint":case"MultiLineString":case"MultiPolygon":case"GeometryCollection":return va(t);default:throw new Error("unknown GeoJSON type")}}function ga(t){var n={type:"Feature"};return Object.keys(t).forEach((function(r){switch(r){case"type":case"properties":case"geometry":return;default:n[r]=t[r]}})),n.properties=da(t.properties),n.geometry=va(t.geometry),n}function da(t){var n={};return t?(Object.keys(t).forEach((function(r){var e=t[r];"object"==typeof e?null===e?n[r]=null:Array.isArray(e)?n[r]=e.map((function(t){return t})):n[r]=da(e):n[r]=e})),n):n}function va(t){var n={type:t.type};return t.bbox&&(n.bbox=t.bbox),"GeometryCollection"===t.type?(n.geometries=t.geometries.map((function(t){return va(t)})),n):(n.coordinates=ya(t.coordinates),n)}function ya(t){var n=t;return"object"!=typeof n[0]?n.slice():n.map((function(t){return ya(t)}))}function ma(t){if(Array.isArray(t))return t;if("Feature"===t.type){if(null!==t.geometry)return t.geometry.coordinates}else if(t.coordinates)return t.coordinates;throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array")}function wa(t){for(var n,r,e=ma(t),i=0,o=1;o<e.length;)n=r||e[0],i+=((r=e[o])[0]-n[0])*(r[1]+n[1]),o++;return i>0}function ba(t,n){if("Feature"===t.type)n(t,0);else if("FeatureCollection"===t.type)for(var r=0;r<t.features.length&&!1!==n(t.features[r],r);r++);}function Ma(t,n){var r,e,i,o,a,s,u,l,c,f,h=0,p="FeatureCollection"===t.type,g="Feature"===t.type,d=p?t.features.length:1;for(r=0;r<d;r++){for(s=p?t.features[r].geometry:g?t.geometry:t,l=p?t.features[r].properties:g?t.properties:{},c=p?t.features[r].bbox:g?t.bbox:void 0,f=p?t.features[r].id:g?t.id:void 0,a=(u=!!s&&"GeometryCollection"===s.type)?s.geometries.length:1,i=0;i<a;i++)if(null!==(o=u?s.geometries[i]:s))switch(o.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":if(!1===n(o,h,l,c,f))return!1;break;case"GeometryCollection":for(e=0;e<o.geometries.length;e++)if(!1===n(o.geometries[e],h,l,c,f))return!1;break;default:throw new Error("Unknown Geometry Type")}else if(!1===n(null,h,l,c,f))return!1;h++}}function xa(t,n){if(!(r=n=n||{})||r.constructor!==Object)throw new Error("options is invalid");var r,e=n.reverse||!1,i=n.mutate||!1;if(!t)throw new Error("<geojson> is required");if("boolean"!=typeof e)throw new Error("<reverse> must be a boolean");if("boolean"!=typeof i)throw new Error("<mutate> must be a boolean");!1===i&&(t=pa(t));var o=[];switch(t.type){case"GeometryCollection":return Ma(t,(function(t){Ea(t,e)})),t;case"FeatureCollection":return ba(t,(function(t){ba(Ea(t,e),(function(t){o.push(t)}))})),Y(o)}return Ea(t,e)}function Ea(t,n){switch("Feature"===t.type?t.geometry.type:t.type){case"GeometryCollection":return Ma(t,(function(t){Ea(t,n)})),t;case"LineString":return Sa(ma(t),n),t;case"Polygon":return _a(ma(t),n),t;case"MultiLineString":return ma(t).forEach((function(t){Sa(t,n)})),t;case"MultiPolygon":return ma(t).forEach((function(t){_a(t,n)})),t;case"Point":case"MultiPoint":return t}}function Sa(t,n){wa(t)===n&&t.reverse()}function _a(t,n){wa(t[0])!==n&&t[0].reverse();for(var r=1;r<t.length;r++)wa(t[r])===n&&t[r].reverse()}function Pa(t){if(!t)throw new Error("geojson is required");var n=[];return function(t,n){Ma(t,(function(t,r,e,i,o){var a,s=null===t?null:t.type;switch(s){case null:case"Point":case"LineString":case"Polygon":return!1!==n(X(t,e,{bbox:i,id:o}),r,0)&&void 0}switch(s){case"MultiPoint":a="Point";break;case"MultiLineString":a="LineString";break;case"MultiPolygon":a="Polygon"}for(var u=0;u<t.coordinates.length;u++){var l=t.coordinates[u];if(!1===n(X({type:a,coordinates:l},e),r,u))return!1}}))}(t,(function(t){n.push(t)})),Y(n)}const ka=lr(),Aa={centroid:!1,name:!1,bbox:!1,rewind:!1},Fa=t=>{if(t.geometry.type.startsWith("Multi")){const n=Pa(t).features[0];return Object.assign(Object.assign({},n),n.properties)}return Object.assign(Object.assign({},t),t.properties)},Da=(t,n={},r)=>{r.type=lo.GEO;const e=Ze(Aa,n),{centroid:i,name:o,bbox:a,rewind:s}=e;if(Array.isArray(t))return(t=>{const n=[];return t.forEach((t=>{"FeatureCollection"===t.type?t.features.forEach((t=>{n.push(Fa(t))})):n.push(Fa(t))})),n})(t);let u=t.features;return s&&(u=xa(t,{reverse:!g(s)||s.reverse}).features),u.forEach((t=>{if(i){const n=ka.centroid(t);t.centroidX=n[0],t.centroidY=n[1]}if(o&&(t.name=t.properties.name),a){const n=ka.bounds(t);t.bbox=n}})),t.features=u,t},Ca={},La={};"function"==typeof SuppressedError&&SuppressedError;function Na(t){var n=0,r=t.children,e=r&&r.length;if(e)for(;--e>=0;)n+=r[e].value;else n=1;t.value=n}function Oa(t,n){t instanceof Map?(t=[void 0,t],void 0===n&&(n=Va)):void 0===n&&(n=ja);for(var r,e,i,o,a,s=new qa(t),u=[s];r=u.pop();)if((i=n(r.data))&&(a=(i=Array.from(i)).length))for(r.children=i,o=a-1;o>=0;--o)u.push(e=i[o]=new qa(i[o])),e.parent=r,e.depth=r.depth+1;return s.eachBefore(Ra)}function ja(t){return t.children}function Va(t){return Array.isArray(t)?t[1]:null}function Ta(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function Ra(t){var n=0;do{t.height=n}while((t=t.parent)&&t.height<++n)}function qa(t){this.data=t,this.depth=this.height=0,this.parent=null}qa.prototype=Oa.prototype={constructor:qa,count:function(){return this.eachAfter(Na)},each:function(t,n){let r=-1;for(const e of this)t.call(n,e,++r,this);return this},eachAfter:function(t,n){for(var r,e,i,o=this,a=[o],s=[],u=-1;o=a.pop();)if(s.push(o),r=o.children)for(e=0,i=r.length;e<i;++e)a.push(r[e]);for(;o=s.pop();)t.call(n,o,++u,this);return this},eachBefore:function(t,n){for(var r,e,i=this,o=[i],a=-1;i=o.pop();)if(t.call(n,i,++a,this),r=i.children)for(e=r.length-1;e>=0;--e)o.push(r[e]);return this},find:function(t,n){let r=-1;for(const e of this)if(t.call(n,e,++r,this))return e},sum:function(t){return this.eachAfter((function(n){for(var r=+t(n.data)||0,e=n.children,i=e&&e.length;--i>=0;)r+=e[i].value;n.value=r}))},sort:function(t){return this.eachBefore((function(n){n.children&&n.children.sort(t)}))},path:function(t){for(var n=this,r=function(t,n){if(t===n)return t;var r=t.ancestors(),e=n.ancestors(),i=null;t=r.pop(),n=e.pop();for(;t===n;)i=t,t=r.pop(),n=e.pop();return i}(n,t),e=[n];n!==r;)n=n.parent,e.push(n);for(var i=e.length;t!==r;)e.splice(i,0,t),t=t.parent;return e},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore((function(n){n.children||t.push(n)})),t},links:function(){var t=this,n=[];return t.each((function(r){r!==t&&n.push({source:r.parent,target:r})})),n},copy:function(){return Oa(this).eachBefore(Ta)},[Symbol.iterator]:function*(){var t,n,r,e,i=this,o=[i];do{for(t=o.reverse(),o=[];i=t.pop();)if(yield i,n=i.children)for(r=0,e=n.length;r<e;++r)o.push(n[r])}while(o.length)}};const Ba={children:t=>t.children,pureData:!1},Ga={svg:"group",rect:"rect",line:"rule",polygon:"polygon",path:"path",polyline:"line",g:"group",circle:"arc",ellipse:"arc"},Ia=Object.keys(Ga),Ha=["g","svg","text","tspan","switch"],Ua=["font-size","font-family","font-weight","font-style","text-align","text-anchor"],$a=["visibility","x","y","width","height","d","points","stroke","stroke-width","fill","fill-opacity","stroke-opacity",...Ua,"cx","cy","r","cx","cy","rx","ry","x1","x2","y1","y2"],za=["visible","fill","stroke","stroke-width","fill-opacity","stroke-opacity",...Ua],Wa=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;let Ja=0;function Xa(t){let n;const{parent:r,attributes:e}=t,i=t=>t?za.reduce(((n,r)=>{const e=J(r);return p(t[e])&&(n[e]=t[e]),n}),{}):{};return r?(r._inheritStyle||(r._inheritStyle=i(r.attributes)),n=k({},r._inheritStyle,i(e))):n=i(e),n}function Ya(t){var n,r,e;const i={},o=null!==(n=t.attributes)&&void 0!==n?n:{},a=null!==(r=t.style)&&void 0!==r?r:{};for(let t=0;t<$a.length;t++){const n=$a[t],r=p(a[n])&&""!==a[n]?a[n]:null===(e=o[n])||void 0===e?void 0:e.value;p(r)&&(i[J(n)]=isNaN(+r)?r:parseFloat(r))}return"none"===a.display&&(i.visible=!1),["fontSize","strokeWidth","width","height"].forEach((t=>{const n=i[t];y(i[t])&&(i[t]=parseFloat(n))})),i}function Ka(t,n){var r,e,i,o,a;const s=null===(r=t.tagName)||void 0===r?void 0:r.toLowerCase();if(3===t.nodeType||"text"===s||"tspan"===s)return function(t,n){var r,e,i,o,a,s;if(!n)return null;const u=null===(r=t.tagName)||void 0===r?void 0:r.toLowerCase();if(!u&&"group"!==n.graphicType)return null;const l="text"===u||"tspan"===u,c=l?"group":"text",f=l||null===(e=t.textContent)||void 0===e?void 0:e.replace(/\n/g," ").replace(/\s+/g," ");if(" "===f)return null;let h;h=l?{tagName:u,graphicType:c,attributes:Ya(t),parent:n,name:t.getAttribute("name"),id:null!==(i=t.getAttribute("id"))&&void 0!==i?i:`${u}-${Ja++}`,transform:Za(t),value:f}:{tagName:u,graphicType:"text",attributes:Ya(t),parent:n,name:null==n?void 0:n.name,id:null!==(a=null===(o=t.getAttribute)||void 0===o?void 0:o.call(t,"id"))&&void 0!==a?a:`${u}-${Ja++}`,value:f};h._inheritStyle=Xa(h),p(h.name)||(h._nameFromParent=null!==(s=n.name)&&void 0!==s?s:n._nameFromParent);l?n._textGroupStyle?h._textGroupStyle=k({},n._textGroupStyle,Ya(t)):h._textGroupStyle=Ya(t):h.attributes=h._inheritStyle;return h}(t,n);if(!Ia.includes(s))return null;const u={tagName:s,graphicType:Ga[s],attributes:Ya(t),parent:n,name:null!==(e=t.getAttribute("name"))&&void 0!==e?e:null===(i=null==n?void 0:n.attributes)||void 0===i?void 0:i.name,id:null!==(o=t.getAttribute("id"))&&void 0!==o?o:`${s}-${Ja++}`,transform:Za(t)};return u._inheritStyle=Xa(u),n&&!p(u.name)&&(u._nameFromParent=null!==(a=n.name)&&void 0!==a?a:n._nameFromParent),u}function Za(t){var n,r;const e=null===(n=t.transform)||void 0===n?void 0:n.baseVal;if(!e)return null;const i=null===(r=e.consolidate())||void 0===r?void 0:r.matrix;if(!i)return null;const{a:o,b:a,c:s,d:u,e:l,f:c}=i;return new j(o,a,s,u,l,c)}function Qa(t,n,r=[]){var e;if(!t)return;let i;"svg"!==t.nodeName&&(i=Ka(t,n)),i&&r.push(i);let o=Ha.includes(null===(e=t.tagName)||void 0===e?void 0:e.toLocaleLowerCase())?t.firstChild:null;for(;o;)Qa(o,null!=i?i:n,r),o=o.nextSibling}let ts=0;function ns(t="dataset"){return ts>1e8&&(ts=0),t+"_"+ts++}const rs="_data-view-diff-rank";class es{constructor(t,n){let r;this.dataSet=t,this.options=n,this.isDataView=!0,this.target=new u,this.parseOption=null,this.transformsArr=[],this.isRunning=!1,this.rawData={},this.history=!1,this.parserData={},this.latestData={},this._fields=null,this.reRunAllTransform=(t={pushHistory:!0,emitMessage:!0})=>(this.isRunning=!0,this.resetTransformData(),this.transformsArr.forEach((n=>{this.executeTransform(n,{pushHistory:t.pushHistory,emitMessage:!1}),this.isLastTransform(n)&&this.diffLastData()})),this.isRunning=!1,!1!==t.emitMessage&&this.target.emit("change",[]),this),this.markRunning=()=>{this.isRunning=!0,this.target.emit("markRunning",[])},r=(null==n?void 0:n.name)?n.name:ns("dataview"),this.name=r,(null==n?void 0:n.history)&&(this.history=n.history,this.historyData=[]),this.dataSet.setDataView(r,this),this.setFields(null==n?void 0:n.fields)}parse(t,n,r=!1){var e;this.isRunning=!0,r&&this.target.emit("beforeParse",[]),n&&(this.parseOption=n);const i=this.cloneParseData(t,n);if(null==n?void 0:n.type){const t=(null!==(e=this.dataSet.getParser(n.type))&&void 0!==e?e:this.dataSet.getParser("bytejson"))(i,n.options,this);this.rawData=i,this.parserData=t,this.history&&this.historyData.push(i,t),this.latestData=t}else this.parserData=i,this.rawData=i,this.history&&this.historyData.push(i),this.latestData=i;return this.isRunning=!1,r&&this.target.emit("afterParse",[]),this}transform(t,n=!0){if(this.isRunning=!0,t&&t.type){let r=!0;if("fields"===t.type){this._fields=t.options.fields;const n=this.transformsArr.findIndex((n=>n.type===t.type));n>=0&&(r=!1,this.transformsArr[n].options.fields=this._fields)}if(r&&this.transformsArr.push(t),n){const n=this.isLastTransform(t);this.executeTransform(t),n&&this.diffLastData()}}return this.sortTransform(),this.isRunning=!1,this}isLastTransform(t){return this.transformsArr[this.transformsArr.length-1]===t}sortTransform(){this.transformsArr.length>=2&&this.transformsArr.sort(((t,n)=>{var r,e;return(null!==(r=t.level)&&void 0!==r?r:0)-(null!==(e=n.level)&&void 0!==e?e:0)}))}executeTransform(t,n={pushHistory:!0,emitMessage:!0}){const{pushHistory:r,emitMessage:e}=n,i=this.dataSet.getTransform(t.type)(this.latestData,t.options);this.history&&!1!==r&&this.historyData.push(i),this.latestData=i,!1!==e&&this.target.emit("change",[])}resetTransformData(){this.latestData=this.parserData,this.history&&(this.historyData.length=0,this.historyData.push(this.rawData,this.parserData))}enableDiff(t){this._diffData=!0,this._diffKeys=t,this._diffMap=new Map,this._diffRank=0}disableDiff(){this._diffData=!1,this._diffMap=null,this._diffRank=null}resetDiff(){this._diffMap=new Map,this._diffRank=0}diffLastData(){var t;if(!this._diffData)return;if(!this.latestData.forEach)return;if(!(null===(t=this._diffKeys)||void 0===t?void 0:t.length))return;const n=this._diffRank+1;if(0===this._diffRank)this.latestData.forEach((t=>{t[rs]=n,this._diffMap.set(this._diffKeys.reduce(((n,r)=>n+t[r]),""),t)})),this.latestDataAUD={add:Array.from(this.latestData),del:[],update:[]};else{let t;this.latestDataAUD={add:[],del:[],update:[]},this.latestData.forEach((r=>{r[rs]=n,t=this._diffKeys.reduce(((t,n)=>t+r[n]),""),this._diffMap.get(t)?this.latestDataAUD.update.push(r):this.latestDataAUD.add.push(r),this._diffMap.set(t,r)})),this._diffMap.forEach(((t,r)=>{t[rs]<n&&(this.latestDataAUD.del.push(t),this._diffMap.delete(r))}))}this._diffRank=n}cloneParseData(t,n){let r=!1;return t instanceof es||!0!==(null==n?void 0:n.clone)||(r=!0),r?E(t):t}parseNewData(t,n){this.parse(t,n||this.parseOption),this.reRunAllTransform()}updateRawData(t,n){const r=this.cloneParseData(t,n);this.rawData=r,this.parserData=r,this.latestData=r,this.reRunAllTransform()}getFields(){var t;return this._fields?this._fields:"dataview"===(null===(t=this.parseOption)||void 0===t?void 0:t.type)&&1===this.rawData.length&&this.rawData[0].getFields?this.rawData[0].getFields():null}setFields(t,n=!1){this._fields=t&&n?k({},this._fields,t):t;const r=this.transformsArr.find((t=>"fields"===t.type));!h(this._fields)&&h(r)?(this.dataSet.registerTransform("fields",po),this.transform({type:"fields",options:{fields:this._fields}},!1)):r&&(r.options.fields=this._fields)}destroy(){this.dataSet.removeDataView(this.name),this._diffMap=null,this._diffRank=null,this.latestData=null,this.rawData=null,this.parserData=null,this.transformsArr=null,this.target=null}}const is=lr();const os={},as={albers:Ar,albersusa:function(){var t,n,r,e,i,o,a=Ar(),s=kr().rotate([154,0]).center([-2,58.5]).parallels([55,65]),u=kr().rotate([157,0]).center([-3,19.9]).parallels([8,18]),l={point:function(t,n){o=[t,n]}};function c(t){var n=t[0],a=t[1];return o=null,r.point(n,a),o||(e.point(n,a),o)||(i.point(n,a),o)}function f(){return t=n=null,c}return c.invert=function(t){var n=a.scale(),r=a.translate(),e=(t[0]-r[0])/n,i=(t[1]-r[1])/n;return(i>=.12&&i<.234&&e>=-.425&&e<-.214?s:i>=.166&&i<.234&&e>=-.214&&e<-.115?u:a).invert(t)},c.stream=function(r){return t&&n===r?t:(e=[a.stream(n=r),s.stream(r),u.stream(r)],i=e.length,t={point:function(t,n){for(var r=-1;++r<i;)e[r].point(t,n)},sphere:function(){for(var t=-1;++t<i;)e[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)e[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)e[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)e[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)e[t].polygonEnd()}});var e,i},c.precision=function(t){return arguments.length?(a.precision(t),s.precision(t),u.precision(t),f()):a.precision()},c.scale=function(t){return arguments.length?(a.scale(t),s.scale(.35*t),u.scale(t),c.translate(a.translate())):a.scale()},c.translate=function(t){if(!arguments.length)return a.translate();var n=a.scale(),o=+t[0],c=+t[1];return r=a.translate(t).clipExtent([[o-.455*n,c-.238*n],[o+.455*n,c+.238*n]]).stream(l),e=s.translate([o-.307*n,c+.201*n]).clipExtent([[o-.425*n+rt,c+.12*n+rt],[o-.214*n-rt,c+.234*n-rt]]).stream(l),i=u.translate([o-.205*n,c+.212*n]).clipExtent([[o-.214*n+rt,c+.166*n+rt],[o-.115*n-rt,c+.234*n-rt]]).stream(l),f()},c.fitExtent=function(t,n){return pr(c,t,n)},c.fitSize=function(t,n){return gr(c,t,n)},c.fitWidth=function(t,n){return dr(c,t,n)},c.fitHeight=function(t,n){return vr(c,t,n)},c.scale(1070)},azimuthalequalarea:function(){return Er(Cr).scale(124.75).clipAngle(179.999)},azimuthalequidistant:function(){return Er(Lr).scale(79.4188).clipAngle(179.999)},conicconformal:function(){return _r(Tr).scale(109.5).parallels([30,30])},conicequalarea:kr,conicequidistant:function(){return _r(qr).scale(131.154).center([0,13.9389])},equalEarth:function(){return Er($r).scale(177.158)},equirectangular:function(){return Er(Rr).scale(152.63)},gnomonic:function(){return Er(zr).scale(144.049).clipAngle(60)},identity:function(){var t,n,r,e,i,o,a,s=1,u=0,l=0,c=1,f=1,h=0,p=null,g=1,d=1,v=cr({point:function(t,n){var r=w([t,n]);this.stream.point(r[0],r[1])}}),y=sn;function m(){return g=s*c,d=s*f,o=a=null,w}function w(r){var e=r[0]*g,i=r[1]*d;if(h){var o=i*t-e*n;e=e*t+i*n,i=o}return[e+u,i+l]}return w.invert=function(r){var e=r[0]-u,i=r[1]-l;if(h){var o=i*t+e*n;e=e*t-i*n,i=o}return[e/g,i/d]},w.stream=function(t){return o&&a===t?o:o=v(y(a=t))},w.postclip=function(t){return arguments.length?(y=t,p=r=e=i=null,m()):y},w.clipExtent=function(t){return arguments.length?(y=null==t?(p=r=e=i=null,sn):an(p=+t[0][0],r=+t[0][1],e=+t[1][0],i=+t[1][1]),m()):null==p?null:[[p,r],[e,i]]},w.scale=function(t){return arguments.length?(s=+t,m()):s},w.translate=function(t){return arguments.length?(u=+t[0],l=+t[1],m()):[u,l]},w.angle=function(r){return arguments.length?(n=vt(h=r%360*ut),t=ht(h),m()):h*st},w.reflectX=function(t){return arguments.length?(c=t?-1:1,m()):c<0},w.reflectY=function(t){return arguments.length?(f=t?-1:1,m()):f<0},w.fitExtent=function(t,n){return pr(w,t,n)},w.fitSize=function(t,n){return gr(w,t,n)},w.fitWidth=function(t,n){return dr(w,t,n)},w.fitHeight=function(t,n){return vr(w,t,n)},w},mercator:Or,naturalEarth1:function(){return Er(Wr).scale(175.295)},orthographic:function(){return Er(Jr).scale(249.5).clipAngle(90+rt)},stereographic:function(){return Er(Xr).scale(250).clipAngle(142)},transversemercator:function(){var t=jr(Yr),n=t.center,r=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?r([t[0],t[1],t.length>2?t[2]+90:90]):[(t=r())[0],t[1],t[2]-90]},r([0,0,90]).scale(159.155)}},ss=["clipAngle","clipExtent","scale","translate","center","rotate","precision","reflectX","reflectY","parallels","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];function us(t,n){return function r(){const e=n();return e.type=t,e.path=lr().projection(e),e.copy=e.copy||function(){const t=r();return ss.forEach((n=>{e[n]&&t[n](e[n]())})),t.path.pointRadius(e.path.pointRadius()),t},e}}t.DataSet=class{constructor(t){var n;let r;this.options=t,this.isDataSet=!0,this.transformMap={},this.parserMap={},this.dataViewMap={},this.target=new u,r=(null==t?void 0:t.name)?t.name:ns("dataset"),this.name=r,this._logger=null!==(n=null==t?void 0:t.logger)&&void 0!==n?n:N.getInstance()}setLogger(t){this._logger=t}getDataView(t){return this.dataViewMap[t]}setDataView(t,n){var r;this.dataViewMap[t]&&(null===(r=this._logger)||void 0===r||r.error(`Error: dataView ${t} 之前已存在，请重新命名`)),this.dataViewMap[t]=n}removeDataView(t){this.dataViewMap[t]=null,delete this.dataViewMap[t]}registerParser(t,n){var r;this.parserMap[t]&&(null===(r=this._logger)||void 0===r||r.warn(`Warn: transform ${t} 之前已注册，执行覆盖逻辑`)),this.parserMap[t]=n}removeParser(t){this.parserMap[t]=null,delete this.parserMap[t]}getParser(t){return this.parserMap[t]||this.parserMap.default}registerTransform(t,n){var r;this.transformMap[t]&&(null===(r=this._logger)||void 0===r||r.warn(`Warn: transform ${t} 之前已注册，执行覆盖逻辑`)),this.transformMap[t]=n}removeTransform(t){this.transformMap[t]=null,delete this.transformMap[t]}getTransform(t){return this.transformMap[t]}multipleDataViewAddListener(t,n,r){this._callMap||(this._callMap=new Map);let e=this._callMap.get(r);e||(e=()=>{t.some((t=>t.isRunning))||r()}),t.forEach((t=>{t.target.addListener(n,e)})),this._callMap.set(r,e)}allDataViewAddListener(t,n){this.multipleDataViewAddListener(Object.values(this.dataViewMap),t,n)}multipleDataViewRemoveListener(t,n,r){if(this._callMap){const e=this._callMap.get(r);e&&t.forEach((t=>{t.target.removeListener(n,e)})),this._callMap.delete(r)}}multipleDataViewUpdateInParse(t){t.forEach((t=>{var n;return null===(n=this.getDataView(t.name))||void 0===n?void 0:n.markRunning()})),t.forEach((t=>{var n;return null===(n=this.getDataView(t.name))||void 0===n?void 0:n.parseNewData(t.data,t.options)}))}multipleDataViewUpdateInRawData(t){t.forEach((t=>{var n;return null===(n=this.getDataView(t.name))||void 0===n?void 0:n.markRunning()})),t.forEach((t=>{var n;return null===(n=this.getDataView(t.name))||void 0===n?void 0:n.updateRawData(t.data,t.options)}))}destroy(){this.transformMap=null,this.parserMap=null,this.dataViewMap=null,this._callMap=null,this.target.removeAllListeners()}},t.DataView=es,t.byteJSONParser=(t,n,r)=>{r.type=lo.BYTE;const e=[],{layerType:i}=n;return t.forEach((t=>{let n=i,r=[];if(t.from&&(n="FlyLine",r=[t.from,t.to]),t.lng&&(n="Point",r=[t.lng,t.lat]),t.coordinates){const e="Line"===n?"LineString":"Polygon";n=Array.isArray(t.coordinates[0][0])?`Multi${e}`:e,r=t.coordinates}const o=function(t,n){var r={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(r[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(e=Object.getOwnPropertySymbols(t);i<e.length;i++)n.indexOf(e[i])<0&&Object.prototype.propertyIsEnumerable.call(t,e[i])&&(r[e[i]]=t[e[i]])}return r}(t,["coordinates"]),a=Object.assign(Object.assign({},o),{geometry:{type:n,coordinates:r}});e.push(a)})),e},t.createProjection=function(t,n){const r=t.toLowerCase();return arguments.length>1?us(r,n):as[r]?(os[r]||(os[r]=us(r,as[r])),os[r]):null},t.csvParser=(t,n={},r)=>(r.type=lo.DSV,xo(t)),t.dataViewParser=(t,n,r)=>{const e=!c(null==n?void 0:n.dependencyUpdate)||(null==n?void 0:n.dependencyUpdate);if(!t||!m(t))throw new TypeError("Invalid data: must be DataView array!");return m(r.rawData)&&r.rawData.forEach((t=>{t.target&&(t.target.removeListener("change",r.reRunAllTransform),t.target.removeListener("markRunning",r.markRunning))})),e&&t.forEach((t=>{t.target.addListener("change",r.reRunAllTransform),t.target.addListener("markRunning",r.markRunning)})),t},t.dissolve=(t,n)=>$e(t),t.dsvParser=(t,n={},r)=>{r.type=lo.DSV;const e=Ze(So,n),{delimiter:i}=e;if(!y(i))throw new TypeError("Invalid delimiter: must be a string!");return Mo(i).parse(t)},t.fields=po,t.filter=(t,n)=>{const{callback:r}=n;return r&&(t=t.filter(r)),t},t.fold=(t,n)=>{const{fields:r,key:e,value:i,retains:o}=n,a=[];for(let n=0;n<t.length;n++)r.forEach((s=>{const u={};if(u[e]=s,u[i]=t[n][s],o)o.forEach((r=>{u[r]=t[n][r]}));else for(const e in t[n])-1===r.indexOf(e)&&(u[e]=t[n][e]);a.push(u)}));return a},t.geoBufParser=(t,n={},r)=>{r.type=lo.GEO;const e=Ze(Aa,Ca,n),i=Ho(new ha(t));return Da(i,e,r)},t.geoJSONParser=Da,t.getProjectionPath=function(t){return t&&t.path||is},t.isDataView=function(t){return t instanceof es},t.map=(t,n)=>{const{callback:r}=n;return r&&(t=t.map(r)),t},t.mercator=(t,n)=>{const r=[];return t.forEach((t=>{const[n,e]=Zr([t.lng,t.lat]);r.push(Object.assign(Object.assign({},t),{coordinates:[n,e]}))})),r},t.pointToHexbin=(t,o)=>{const{size:a=10,angle:s=0,calcMethod:u="sum",padding:l=0,field:c="value",colorConfig:f}=o;if(0===t.length)return null;const{type:h,field:p,range:g}=f,d=function(){var t,o,a,s=0,u=0,l=1,c=1,f=e,h=i;function p(t){var n,r={},e=[],i=t.length;for(n=0;n<i;++n)if(!isNaN(u=+f.call(null,s=t[n],n,t))&&!isNaN(l=+h.call(null,s,n,t))){var s,u,l,c=Math.round(l/=a),p=Math.round(u=u/o-(1&c)/2),g=l-c;if(3*Math.abs(g)>1){var d=u-p,v=p+(u<p?-1:1)/2,y=c+(l<c?-1:1),m=u-v,w=l-y;d*d+g*g>m*m+w*w&&(p=v+(1&c?1:-1)/2,c=y)}var b=p+"-"+c,M=r[b];M?M.push(s):(e.push(M=r[b]=[s]),M.x=(p+(1&c)/2)*o,M.y=c*a)}return e}function g(t){var n=0,e=0;return r.map((function(r){var i=Math.sin(r)*t,o=-Math.cos(r)*t,a=i-n,s=o-e;return n=i,e=o,[a,s]}))}return p.hexagon=function(n){return"m"+g(null==n?t:+n).join("l")+"z"},p.centers=function(){for(var n=[],r=Math.round(u/a),e=Math.round(s/o),i=r*a;i<c+t;i+=a,++r)for(var f=e*o+(1&r)*o/2;f<l+o/2;f+=o)n.push([f,i]);return n},p.mesh=function(){var n=g(t).slice(0,4).join("l");return p.centers().map((function(t){return"M"+t+"m"+n})).join("")},p.x=function(t){return arguments.length?(f=t,p):f},p.y=function(t){return arguments.length?(h=t,p):h},p.radius=function(r){return arguments.length?(o=2*(t=+r)*Math.sin(n),a=1.5*t,p):t},p.size=function(t){return arguments.length?(s=u=0,l=+t[0],c=+t[1],p):[l-s,c-u]},p.extent=function(t){return arguments.length?(s=+t[0][0],u=+t[0][1],l=+t[1][0],c=+t[1][1],p):[[s,u],[l,c]]},p.radius(1)}().radius(a).x((t=>t.coordinates[0])).y((t=>t.coordinates[1])),v=d(t).map(((t,n)=>{const r=t.map((t=>t[c])).reduce(((t,n)=>t+n));return{_id:n,hexCenterCoord:[t.x,t.y],rawData:t,count:t.length,[c]:r}}));v.sort(((t,n)=>t[c]-n[c])),function(t,n,r,e){if(!t)return void console.warn("Warn: 颜色 range 未传入 startColor");if(!n){const n=K(t);return void r.forEach(((t,r)=>{t.colorObj=n}))}const{color:i,opacity:o}=K(t),{r:a,g:s,b:u}=i.color,{color:l,opacity:c}=K(n),{r:f,g:h,b:p}=l.color,g=f-a,d=h-s,v=p-u,y=c-o,m=r.length;if(0===m)return;if(1===m)return void(r[0].colorObj={color:new z(new W(a,s,u).toString()),transparent:!0,opacity:o});if(2===m)return r[0].colorObj={color:new z(new W(a,s,u).toString()),transparent:!0,opacity:o},void(r[1].colorObj={color:new z(new W(f,h,p).toString()),transparent:!0,opacity:c});const w=r[m-1][e]-r[0][e];r.forEach(((t,n)=>{const i=0===w?0:(t[e]-r[0][e])/w,l=K(`rgba(${Math.floor(255*(a+g*i))},${Math.floor(255*(s+d*i))},${Math.floor(255*(u+v*i))}, ${o+y*i})`);t.colorObj=l}))}(g[0],g[1],v,c);const y=((t,n,r,e=0)=>{const i=[],o=[];let a=[0,1,2,2,3,4,4,5,0,0,2,4];const s=[],u=[];return t.forEach(((l,c)=>{const f=l.hexCenterCoord[0]-t[0].hexCenterCoord[0],h=l.hexCenterCoord[1]-t[0].hexCenterCoord[1];u.push(f,h,r);for(let t=0;t<6;t++){const o=60*t-30+e,a=Math.PI/180*o;i.push(l.hexCenterCoord[0]+n*Math.cos(a),l.hexCenterCoord[1]+n*Math.sin(a),r)}0===c||(a=a.map((t=>t+6))),s.push(...a);const{color:p,opacity:g}=l.colorObj,d=[p.r,p.g,p.b,g];o.push(...d)})),{position:i,indexes:s,centerPoints:[...t[0].hexCenterCoord,r],colors:o,offset:u}})(v,a-l,0,s);return y},t.projection=(t,n)=>{if(!t||0===t.length)return t;const{projection:r,as:e}=n,i=Qr[r];if(t[0].lng){return t.map((t=>Object.assign(Object.assign({},t),{[e]:i([t.lng,t.lat])})))}return t.map((t=>{const{coordinates:n}=t.geometry||{};if(!Array.isArray(n[0])){const r=i(n);return Object.assign(Object.assign({},t),{[e]:r})}const r=n.map((t=>Array.isArray(t[0])?t.map((t=>i(t))):i(t)));return Object.assign(Object.assign({},t),{[e]:r,geometry:Object.assign(Object.assign({},t.geometry),{[e]:r})})}))},t.projectionProperties=ss,t.readCSVTopNLine=function(t,n){let r="";return["\r\n","\r","\n"].some((e=>!!t.includes(e)&&(r=t.split(e).slice(0,n+1).join(e),!0)))?r:t},t.simplify=(t,n)=>{const r=Ze(ni,n),{tolerance:e}=r;return Ke(t,e)},t.statistics=(t,n)=>{const r=Ze(fo,n),{as:e,fields:i,groupBy:o,operations:a}=r,s={};t.forEach((t=>{s[t[o]]=s[t[o]]||[],s[t[o]].push(t)}));const u=[];for(const t in s){const n={group:t},r=s[t];a.forEach(((t,o)=>{var a,s;const u=null!==(a=e[o])&&void 0!==a?a:t,l=null!==(s=i[o])&&void 0!==s?s:i[0];n[u]=ho[t](r,l)})),u.push(n)}return u},t.svgParser=(t,n={},r)=>{let e=n.customDOMParser;if(e||(null===window||void 0===window?void 0:window.DOMParser)&&(e=t=>(new DOMParser).parseFromString(t,"text/xml")),!e)throw new Error("No Available DOMParser!");const i=e(t);let o=9===i.nodeType?i.firstChild:i;for(;o&&("svg"!==o.nodeName.toLowerCase()||1!==o.nodeType);)o=o.nextSibling;if(o){const t=function(t,n={}){const r=[],e=Ka(t,null);let i=parseFloat(t.getAttribute("width")||n.width),o=parseFloat(t.getAttribute("height")||n.height);!x(i)&&(i=null),!x(o)&&(o=null);const a=t.getAttribute("viewBox");let s;if(a){const t=a.match(Wa)||[];if(t.length>=4&&(s={x:parseFloat(t[0]||0),y:parseFloat(t[1]||0),width:parseFloat(t[2]),height:parseFloat(t[3])},i||o)){const t={x:0,y:0,width:i,height:o},n=t.width/s.width,r=t.height/s.height,a=Math.min(n,r),u=-(s.x+s.width/2)*a+(t.x+t.width/2),l=-(s.y+s.height/2)*a+(t.y+t.height/2),c=(new j).translate(u,l).scale(a,a);e.transform=c}}return Qa(t,e,r),{root:e,width:i,height:o,elements:r,viewBoxRect:s}}(o);return t}return null},t.topoJSONParser=(t,n,r)=>{r.type=lo.GEO;const e=Ze(Aa,La,n),{object:i}=e;if(!y(i))throw new TypeError("Invalid object: must be a string!");const o=Me(t,t.objects[i]);return Da(o,e,r)},t.treeParser=(t,n,r)=>{r.type=lo.TREE;const e=Ze(Ba,n),{children:i}=e;if(i&&!f(i))throw new TypeError("Invalid children: must be a function!");return Oa(t,i)},t.tsvParser=(t,n={},r)=>(r.type=lo.DSV,Eo(t))}));
