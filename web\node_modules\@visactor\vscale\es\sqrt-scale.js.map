{"version": 3, "sources": ["../src/sqrt-scale.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AAGnC,MAAM,OAAO,SAAU,SAAQ,WAAW;IAGxC;QACE,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAHb,SAAI,GAAwB,SAAS,CAAC,IAAI,CAAC;IAIpD,CAAC;IAED,KAAK;QACH,OAAO,IAAI,SAAS,EAAE;aACnB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;aAC1B,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;aACxB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;aACtB,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;aAC/B,WAAW,CAAC,IAAI,CAAC,YAAY,CAAgB,CAAC;IACnD,CAAC;CACF", "file": "sqrt-scale.js", "sourcesContent": ["import { sqrt, square } from './utils/utils';\nimport { LinearScale } from './linear-scale';\nimport { ScaleEnum } from './type';\nimport type { ContinuousScaleType } from './interface';\n\nexport class SqrtScale extends LinearScale {\n  readonly type: ContinuousScaleType = ScaleEnum.Sqrt;\n\n  constructor() {\n    super(sqrt, square);\n  }\n\n  clone(): SqrtScale {\n    return new SqrtScale()\n      .domain(this._domain, true)\n      .range(this._range, true)\n      .unknown(this._unknown)\n      .clamp(this.clamp(), null, true)\n      .interpolate(this._interpolate) as LinearScale;\n  }\n}\n"]}