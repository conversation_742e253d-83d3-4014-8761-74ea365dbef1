{"version": 3, "sources": ["../src/threshold-scale.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAEhE,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AAEnC,MAAM,OAAO,cAAc;IAA3B;QACW,SAAI,GAA0B,SAAS,CAAC,SAAS,CAAC;QAEjD,WAAM,GAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvB,YAAO,GAAa,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAC,GAAW,CAAC,CAAC;IAiD1B,CAAC;IA3CC,OAAO,CAAC,CAAO;QACb,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACrB,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;QACD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,CAAM;QACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC1G,CAAC;IAED,YAAY,CAAC,CAAM;QACjB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAID,MAAM,CAAC,CAAS;QACd,IAAI,CAAC,CAAC,EAAE;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SAC7B;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAID,KAAK,CAAC,CAAS;QACb,IAAI,CAAC,CAAC,EAAE;YACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SAC5B;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK;QACH,OAAO,IAAI,cAAc,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7F,CAAC;CACF", "file": "threshold-scale.js", "sourcesContent": ["import { isNil, is<PERSON>alid<PERSON><PERSON>ber, bisect } from '@visactor/vutils';\nimport type { DiscretizingScaleType, IBaseScale } from './interface';\nimport { ScaleEnum } from './type';\n\nexport class ThresholdScale implements IBaseScale {\n  readonly type: DiscretizingScaleType = ScaleEnum.Threshold;\n\n  protected _range: any[] = [0, 1];\n  protected _domain: number[] = [0.5];\n  protected n: number = 1;\n\n  protected _unknown: any;\n\n  unknown(): any[];\n  unknown(_: any): this;\n  unknown(_?: any): this | any {\n    if (!arguments.length) {\n      return this._unknown;\n    }\n    this._unknown = _;\n    return this;\n  }\n\n  scale(x: any) {\n    return !isNil(x) && isValidNumber(+x) ? this._range[bisect(this._domain, x, 0, this.n)] : this._unknown;\n  }\n\n  invertExtent(y: any) {\n    const i = this._range.indexOf(y);\n    return [this._domain[i - 1], this._domain[i]];\n  }\n\n  domain(): any[];\n  domain(_: any[]): this;\n  domain(_?: any[]): this | any {\n    if (!_) {\n      return this._domain.slice();\n    }\n    this._domain = Array.from(_);\n    this.n = Math.min(this._domain.length, this._range.length - 1);\n    return this;\n  }\n\n  range(): any[];\n  range(_: any[]): this;\n  range(_?: any[]): this | any {\n    if (!_) {\n      return this._range.slice();\n    }\n    this._range = Array.from(_);\n    this.n = Math.min(this._domain.length, this._range.length - 1);\n\n    return this;\n  }\n\n  clone(): ThresholdScale {\n    return new ThresholdScale().domain(this._domain).range(this._range).unknown(this._unknown);\n  }\n}\n"]}