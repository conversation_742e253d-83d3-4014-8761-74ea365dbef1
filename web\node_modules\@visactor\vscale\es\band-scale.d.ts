import { OrdinalScale } from './ordinal-scale';
import type { DiscreteScaleType, IBandLikeScale, ScaleFishEyeOptions, TickData } from './interface';
export declare class BandScale extends OrdinalScale implements IBandLikeScale {
    readonly type: DiscreteScaleType;
    protected _step?: number;
    protected _bandwidth?: number;
    protected _isFixed?: boolean;
    protected _userBandwidth?: number | 'auto';
    protected _maxBandwidth?: number;
    protected _minBandwidth?: number;
    protected _round: boolean;
    protected _paddingInner: number;
    protected _paddingOuter: number;
    protected _align: number;
    protected _range: Array<number>;
    protected _bandRangeState?: {
        reverse: boolean;
        start: number;
        min: number;
        max: number;
        count: number;
    };
    constructor(slience?: boolean);
    rescale(slience?: boolean, changeProperty?: keyof IBandLikeScale): this;
    scale(d: any): any;
    protected _calculateWholeRange(range: any[], changeProperty?: keyof IBandLikeScale): any[];
    calculateWholeRangeSize(): number;
    calculateVisibleDomain(range: any[]): number[];
    domain(): any[];
    domain(_: any[], slience?: boolean): this;
    range(): any[];
    range(_: any[], slience?: boolean): this;
    rangeRound(_: any[], slience?: boolean): this;
    ticks(count?: number): number[];
    tickData(count?: number): TickData[];
    forceTicks(count?: number): any[];
    stepTicks(step: number): any[];
    protected _getInvertIndex(d: any): any;
    invert(d: any): any;
    padding(p: number | number[], slience?: boolean): this;
    padding(): number;
    paddingInner(p: number, slience?: boolean): this;
    paddingInner(): number;
    paddingOuter(p: number, slience?: boolean): this;
    paddingOuter(): number;
    step(): number;
    round(_: boolean, slience?: boolean): this;
    round(): boolean;
    align(_: number, slience?: boolean): this;
    align(): number;
    rangeFactor(): [number, number];
    rangeFactor(_: [number, number], slience?: boolean): this;
    rangeFactorStart(): number;
    rangeFactorStart(_: number, slience?: boolean): this;
    rangeFactorEnd(): number;
    rangeFactorEnd(_: number, slience?: boolean): this;
    bandwidth(): number;
    bandwidth(_: number | 'auto', slience?: boolean): this;
    maxBandwidth(): number;
    maxBandwidth(_: number | 'auto', slience?: boolean): this;
    minBandwidth(): number;
    minBandwidth(_: number | 'auto', slience?: boolean): this;
    fishEye(): ScaleFishEyeOptions;
    fishEye(options: ScaleFishEyeOptions, slience?: boolean, clear?: boolean): this;
    isBandwidthFixed(): boolean;
    protected _isBandwidthFixedByUser(): boolean;
    clone(): IBandLikeScale;
}
