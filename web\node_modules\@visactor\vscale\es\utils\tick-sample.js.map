{"version": 3, "sources": ["../src/utils/tick-sample.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5D,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAErD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAgClC,MAAM,CAAC,MAAM,2BAA2B,GAAG,CAAC,KAAa,EAAE,SAAiB,EAAE,UAAoB,EAAE,EAAE;IACpG,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAE/B,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,SAAS,EAAE;QAC3C,KAAK,GAAG,CAAC,CAAC;KACX;SAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;QAClD,KAAK,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;KAC1B;SAAM,IAAI,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;KAEjC;SAAM,IAAI,UAAU,IAAI,MAAM,GAAG,CAAC,EAAE;QACnC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC;KAChD;IAED,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5B;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE;YAEpB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SAClD;QAED,OAAO,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,KAAa,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;KACzE;IAED,OAAO,KAAK,GAAG,CAAC;QACd,CAAC,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC;QACxD,CAAC,CAAC,oBAAoB,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5D,CAAC,CAAC;AASF,MAAM,CAAC,MAAM,OAAO,GAAG,OAAO,CAC5B,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,OAAkC,EAAE,EAAE;IACjF,IAAI,OAAO,CAAC;IACZ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,IAAI,CAAC,CAAC;IACN,IAAI,KAAK,CAAC;IACV,IAAI,IAAI,CAAC;IAET,IAAI,GAAG,CAAC,IAAI,CAAC;IACb,KAAK,GAAG,CAAC,KAAK,CAAC;IACf,KAAK,GAAG,CAAC,KAAK,CAAC;IAGf,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;IAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE;QAC3D,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;IACD,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;QAC5B,CAAC,GAAG,KAAK,CAAC;QACV,KAAK,GAAG,IAAI,CAAC;QACb,IAAI,GAAG,CAAC,CAAC;KACV;IACD,IAAI,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC;IAG9C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,EAAE,CAAC;KACX;IAED,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QACjC,IAAI,EAAE,GAAG,IAAI,GAAG,KAAK,EAAE;YACrB,EAAE,EAAE,CAAC;SACN;QACD,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;YACpB,EAAE,EAAE,CAAC;SACN;QACD,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;YACd,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;KACF;SAAM,IAAI,IAAI,GAAG,CAAC,KAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,CAAA,EAAE;QAC1C,IAAI,GAAG,CAAC,CAAC;QACT,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,EAAE,IAAI,EAAE,EAAE;YACZ,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YACrC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;gBACd,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aACnB;SACF;aAAM;YACL,OAAO,EAAE,CAAC;SACX;KACF;SAAM;QACL,IAAI,GAAG,CAAC,IAAI,CAAC;QACb,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QACjC,IAAI,EAAE,GAAG,IAAI,GAAG,KAAK,EAAE;YACrB,EAAE,EAAE,CAAC;SACN;QACD,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;YACpB,EAAE,EAAE,CAAC;SACN;QACD,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;YACd,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;KACF;IAED,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,EAAE,CAAC;KACjB;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CACF,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY,EAAE,EAAE;IACzE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,IAAI,CAAC,CAAC;IACN,IAAI,KAAK,CAAC;IAEV,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE;YAC3B,EAAE,EAAE,CAAC;SACN;QACD,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;YAC1B,EAAE,EAAE,CAAC;SACN;QACD,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;YACd,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;KACF;SAAM;QACL,IAAI,GAAG,CAAC,IAAI,CAAC;QACb,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE;YAC3B,EAAE,EAAE,CAAC;SACN;QACD,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;YAC1B,EAAE,EAAE,CAAC;SACN;QACD,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;YACd,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,KAAe,EAAE,KAAa,EAAE,IAAY,EAAE,EAAE;IACjF,IAAI,CAAS,CAAC;IACd,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzC,MAAM,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;IAEzC,IAAI,QAAQ,IAAI,CAAC,EAAE;QACjB,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,KAAK,CAAC,GAAG,WAAW,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACjC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAChC;SAAM,IAAI,SAAS,IAAI,CAAC,EAAE;QAEzB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,WAAW,EAAE,CAAC,EAAE,EAAE;YACjC,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;SACjC;QAED,OAAO,KAAK,CAAC;KACd;IACD,IAAI,SAAS,GAAa,EAAE,CAAC;IAC7B,MAAM,SAAS,GAAa,EAAE,CAAC;IAE/B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,WAAW,EAAE,CAAC,EAAE,EAAE;QACjC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACf,SAAS,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACtE;aAAM;YACL,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SACpD;KACF;IAED,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACnD,CAAC,CAAC;AASF,MAAM,CAAC,MAAM,KAAK,GAAG,OAAO,CAC1B,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,OAAkC,EAAE,EAAE;IACjF,IAAI,OAAO,CAAC;IACZ,IAAI,KAAK,CAAC;IACV,IAAI,CAAC,CAAC;IACN,MAAM,aAAa,GAAG,CAAC,CAAC;IAExB,IAAI,GAAG,CAAC,IAAI,CAAC;IACb,KAAK,GAAG,CAAC,KAAK,CAAC;IACf,KAAK,GAAG,CAAC,KAAK,CAAC;IAGf,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,2BAA2B,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,CAAC,CAAC;KACvE;IAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE;QAC3D,OAAO,2BAA2B,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,CAAC,CAAC;KACvE;IACD,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;QAC5B,CAAC,GAAG,KAAK,CAAC;QACV,KAAK,GAAG,IAAI,CAAC;QACb,IAAI,GAAG,CAAC,CAAC;KACV;IACD,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAClD,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAGxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,EAAE,CAAC;KACX;IAED,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;QAC/B,MAAM,SAAS,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC;QACjE,OACE,GAAG,IAAI,aAAa;YACpB,CAAC,CAAC,KAAK,GAAG,oBAAoB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;YAC7E,KAAK,GAAG,CAAC,EACT;YACA,IAAI,IAAI,SAAS,CAAC;YAElB,GAAG,IAAI,CAAC,CAAC;SACV;QAED,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,EAAE;YACzC,KAAK,GAAG,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SAChD;KACF;SAAM;QACL,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,KAAI,IAAI,GAAG,CAAC,EAAE;YACnC,IAAI,GAAG,CAAC,CAAC;SACV;QACD,KAAK,GAAG,oBAAoB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACjD;IAED,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,EAAE,CAAC;KACjB;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CACF,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;IACnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE,IAAI,KAAK,CAAC;IAEjC,IAAI,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,KAAK,IAAI,GAAG,EAAE;QAChB,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;KACtB;SAAM,IAAI,KAAK,IAAI,EAAE,EAAE;QACtB,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;KACtB;SAAM,IAAI,KAAK,IAAI,EAAE,EAAE;QACtB,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;KACtB;IAED,IAAI,KAAK,IAAI,CAAC,EAAE;QACd,OAAO,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE,IAAI,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;KAChD;IACD,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACrD,CAAC,CAAC;AAEF,MAAM,UAAU,aAAa,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa;IACtE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACjD,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AASD,MAAM,UAAU,UAAU,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa;IACnE,IAAI,IAAI,CAAC;IAET,IAAI,GAAG,CAAC,IAAI,CAAC;IACb,KAAK,GAAG,CAAC,KAAK,CAAC;IACf,KAAK,GAAG,CAAC,KAAK,CAAC;IACf,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;QAC/B,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;IACD,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC1F,OAAO,EAAE,CAAC;KACX;IAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;IAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;KAC7B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa;IAE3E,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IACrD,OAAO,IAAI,CAAC;AACd,CAAC;AASD,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY;IACjE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,IAAI,CAAC,CAAC;IACN,IAAI,OAAO,CAAC;IAEZ,IAAI,GAAG,CAAC,IAAI,CAAC;IACb,KAAK,GAAG,CAAC,KAAK,CAAC;IACf,IAAI,GAAG,CAAC,IAAI,CAAC;IACb,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;QAC5B,CAAC,GAAG,KAAK,CAAC;QACV,KAAK,GAAG,IAAI,CAAC;QACb,IAAI,GAAG,CAAC,CAAC;KACV;IACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,EAAE;QAC3C,OAAO,CAAC,KAAK,CAAC,CAAC;KAChB;IACD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;IACpD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,EAAE,CAAC,GAAG,KAAK,EAAE;QAClB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;KAC7B;IACD,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,EAAE,CAAC;KACjB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,CAAW,EAAE,QAAgB,EAAE;IACxD,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACtB,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB,IAAI,OAAO,CAAC;IACZ,IAAI,IAAI,CAAC;IACT,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,IAAI,IAAI,GAAG,KAAK,EAAE;QAChB,IAAI,GAAG,KAAK,CAAC;QACb,KAAK,GAAG,IAAI,CAAC;QACb,IAAI,GAAG,IAAI,CAAC;QACZ,IAAI,GAAG,EAAE,CAAC;QACV,EAAE,GAAG,EAAE,CAAC;QACR,EAAE,GAAG,IAAI,CAAC;KACX;IAED,OAAO,OAAO,EAAE,GAAG,CAAC,EAAE;QACpB,IAAI,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC;QAC9C,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;YACd,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACb,OAAO,CAAC,CAAC;SACV;aAAM,IAAI,IAAI,GAAG,CAAC,EAAE;YACnB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACxC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;SACtC;aAAM,IAAI,IAAI,GAAG,CAAC,EAAE;YACnB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACvC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;SACvC;aAAM;YACL,MAAM;SACP;QACD,OAAO,GAAG,IAAI,CAAC;KAChB;IAED,OAAO;AACT,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,cAAwB,EAAE,MAAmB;IAC5E,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC9C,IAAI,QAAQ,GAAa,IAAI,CAAC;IAC9B,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,UAAU,GAAa,IAAI,CAAC;IAEhC,MAAM,eAAe,GACnB,WAAW,IAAI,WAAW;QACxB,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ;QAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ;YACrC,CAAC,CAAC,WAAW;gBACb,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ;gBACrC,CAAC,CAAC,IAAI,CAAC;IAEX,IAAI,WAAW,EAAE;QACf,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;KACjC;SAAM,IACL,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;QACpB,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EACpF;QACA,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;KAC5B;IAED,IAAI,WAAW,EAAE;QACf,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;KACjC;SAAM,IACL,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;QACpB,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EACpF;QACA,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;KAC5B;IAED,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QACtD,UAAU,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC;QACpC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9B,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;KACnD;SAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/D,QAAQ,GAAG,KAAK,CAAC;KAClB;SAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,QAAQ,GAAG,KAAK,CAAC;KAClB;SAAM;QACL,QAAQ,GAAG,KAAK,CAAC;KAClB;IAED,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;AAC/D,CAAC;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,EAAE;IACzE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7E,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,OAAO,CAClC,CACE,KAAa,EACb,IAAY,EACZ,KAAa,EACb,IAAY,EACZ,WAA0B,EAC1B,aAA4B,EAC5B,OAAkC,EAClC,EAAE;IACF,IAAI,CAAC,GAAG,KAAK,CAAC;IACd,IAAI,CAAC,GAAG,IAAI,CAAC;IACb,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEhB,IAAI,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACjB;IAED,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,CAAC;IACN,IAAI,CAAC,GAAG,EAAE,CAAC;IAEX,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;QAEhC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;gBAClB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,EAAE;oBACzB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;oBACzD,IAAI,CAAC,GAAG,CAAC,EAAE;wBACT,SAAS;qBACV;oBACD,IAAI,CAAC,GAAG,CAAC,EAAE;wBACT,MAAM;qBACP;oBACD,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACX;aACF;SACF;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;gBAClB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;oBAC9B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;oBACzD,IAAI,CAAC,GAAG,CAAC,EAAE;wBACT,SAAS;qBACV;oBACD,IAAI,CAAC,GAAG,CAAC,EAAE;wBACT,MAAM;qBACP;oBACD,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACX;aACF;SACF;QACD,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE;YACxB,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;SACxB;KACF;SAAM;QACL,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KAC5D;IACD,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACrC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,EAAE;QACvB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9D;IACD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,OAAO,CACvC,CACE,KAAa,EACb,IAAY,EACZ,KAAa,EACb,IAAY,EACZ,WAA0B,EAC1B,aAA4B,EAC5B,EAAE;IACF,MAAM,WAAW,GAAa,EAAE,CAAC;IACjC,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IACpC,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,QAAQ,GAAG,EAAE,CAAC;IAElB,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;QAC1B,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;KAC5C;SAAM;QACL,MAAM,OAAO,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC9B,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;SACvC;KACF;IACD,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAU,EAAE,EAAE;QAE9B,MAAM,KAAK,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;QAEhC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;YAClC,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,cAAc,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAEvF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACtE,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC9B;IACH,CAAC,CAAC,CAAC;IACH,OAAO,WAAW,CAAC;AACrB,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAG,OAAO,CAC5C,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,WAA0B,EAAE,aAA4B,EAAE,EAAE;IACvG,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IACpC,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAU,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAAG,OAAO,CAChD,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY,EAAE,WAA0B,EAAE,aAA4B,EAAE,EAAE;IACtG,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;IACpC,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACpD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAU,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CACF,CAAC", "file": "tick-sample.js", "sourcesContent": ["import { range, memoize, isNumber } from '@visactor/vutils';\nimport type { TransformType, ContinuousTicksFunc, NiceOptions, NiceType } from '../interface';\nimport { niceNumber, restrictNumber } from './utils';\n\nconst e10 = Math.sqrt(50);\nconst e5 = Math.sqrt(10);\nconst e2 = Math.sqrt(2);\nconst niceNumbers = [1, 2, 5, 10];\n\ntype TicksFunc = (start: number, stop: number, count: number) => number[];\n// eslint-disable-next-line max-len\ntype TicksBaseTransformFunc = (\n  start: number,\n  stop: number,\n  count: number,\n  base: number,\n  transformer: TransformType,\n  untransformer: TransformType\n) => number[];\n// eslint-disable-next-line max-len\ntype ForceTicksBaseTransformFunc = (\n  start: number,\n  stop: number,\n  count: number,\n  transformer: TransformType,\n  untransformer: TransformType\n) => number[];\ntype D3TicksForLogTransformFunc = (\n  start: number,\n  stop: number,\n  count: number,\n  base: number,\n  transformer: TransformType,\n  untransformer: TransformType,\n  options?: {\n    noDecimals?: boolean;\n  }\n) => number[];\n\nexport const calculateTicksOfSingleValue = (value: number, tickCount: number, noDecimals?: boolean) => {\n  let step = 1;\n  let start = value;\n  const middleIndex = Math.floor((tickCount - 1) / 2);\n  const absVal = Math.abs(value);\n\n  if (value >= 0 && value <= Number.MIN_VALUE) {\n    start = 0;\n  } else if (value < 0 && value >= -Number.MIN_VALUE) {\n    start = -(tickCount - 1);\n  } else if (!noDecimals && absVal < 1) {\n    step = getNickStep(absVal).step;\n    // middle = new Decimal(Math.floor(middle.div(step).toNumber())).mul(step);\n  } else if (noDecimals || absVal > 1) {\n    start = Math.floor(value) - middleIndex * step;\n  }\n\n  if (step > 0) {\n    if (value > 0) {\n      start = Math.max(start, 0);\n    } else if (value < 0) {\n      // < 0;\n      start = Math.min(start, -(tickCount - 1) * step);\n    }\n\n    return range(0, tickCount).map((index: number) => start + index * step);\n  }\n\n  return value > 0\n    ? calculateTicksByStep(0, -(tickCount - 1) / step, step)\n    : calculateTicksByStep((tickCount - 1) / step, 0, step);\n};\n\n/**\n * 根据start、stop、count进行分割，不要求count完全准确\n * @param start\n * @param stop\n * @param count\n * @returns\n */\nexport const d3Ticks = memoize<ContinuousTicksFunc>(\n  (start: number, stop: number, count: number, options?: { noDecimals?: boolean }) => {\n    let reverse;\n    let i = -1;\n    let n;\n    let ticks;\n    let step;\n\n    stop = +stop;\n    start = +start;\n    count = +count;\n\n    // add check for start equal stop\n    if (start === stop) {\n      return [start];\n    }\n\n    if (Math.abs(start - stop) <= Number.MIN_VALUE && count > 0) {\n      return [start];\n    }\n    if ((reverse = stop < start)) {\n      n = start;\n      start = stop;\n      stop = n;\n    }\n    step = tickIncrement(start, stop, count).step;\n    // why return empty array when stop === 0 ?\n    // if (stop === 0 || !isFinite(step)) {\n    if (!isFinite(step)) {\n      return [];\n    }\n\n    if (step > 0) {\n      let r0 = Math.round(start / step);\n      let r1 = Math.round(stop / step);\n      if (r0 * step < start) {\n        ++r0;\n      }\n      if (r1 * step > stop) {\n        --r1;\n      }\n      ticks = new Array((n = r1 - r0 + 1));\n      while (++i < n) {\n        ticks[i] = (r0 + i) * step;\n      }\n    } else if (step < 0 && options?.noDecimals) {\n      step = 1;\n      const r0 = Math.ceil(start);\n      const r1 = Math.floor(stop);\n\n      if (r0 <= r1) {\n        ticks = new Array((n = r1 - r0 + 1));\n        while (++i < n) {\n          ticks[i] = r0 + i;\n        }\n      } else {\n        return [];\n      }\n    } else {\n      step = -step;\n      let r0 = Math.round(start * step);\n      let r1 = Math.round(stop * step);\n      if (r0 / step < start) {\n        ++r0;\n      }\n      if (r1 / step > stop) {\n        --r1;\n      }\n      ticks = new Array((n = r1 - r0 + 1));\n      while (++i < n) {\n        ticks[i] = (r0 + i) / step;\n      }\n    }\n\n    if (reverse) {\n      ticks.reverse();\n    }\n\n    return ticks;\n  }\n);\n\nconst calculateTicksByStep = (start: number, stop: number, step: number) => {\n  let i = -1;\n  let n;\n  let ticks;\n\n  if (step > 0) {\n    let r0 = Math.floor(start / step);\n    let r1 = Math.ceil(stop / step);\n    if ((r0 + 1) * step < start) {\n      ++r0;\n    }\n    if ((r1 - 1) * step > stop) {\n      --r1;\n    }\n    ticks = new Array((n = r1 - r0 + 1));\n    while (++i < n) {\n      ticks[i] = (r0 + i) * step;\n    }\n  } else {\n    step = -step;\n    let r0 = Math.floor(start * step);\n    let r1 = Math.ceil(stop * step);\n    if ((r0 + 1) / step < start) {\n      ++r0;\n    }\n    if ((r1 - 1) / step > stop) {\n      --r1;\n    }\n    ticks = new Array((n = r1 - r0 + 1));\n    while (++i < n) {\n      ticks[i] = (r0 + i) / step;\n    }\n  }\n\n  return ticks;\n};\n\nexport const appendTicksToCount = (ticks: number[], count: number, step: number) => {\n  let n: number;\n  const firstTick = ticks[0];\n  const lastTick = ticks[ticks.length - 1];\n  const appendCount = count - ticks.length;\n\n  if (lastTick <= 0) {\n    const headTicks: number[] = [];\n    // append to head\n    for (n = appendCount; n >= 1; n--) {\n      headTicks.push(firstTick - n * step);\n    }\n    return headTicks.concat(ticks);\n  } else if (firstTick >= 0) {\n    // append to tail\n    for (n = 1; n <= appendCount; n++) {\n      ticks.push(lastTick + n * step);\n    }\n\n    return ticks;\n  }\n  let headTicks: number[] = [];\n  const tailTicks: number[] = [];\n  // append to head and tail\n  for (n = 1; n <= appendCount; n++) {\n    if (n % 2 === 0) {\n      headTicks = [firstTick - Math.floor(n / 2) * step].concat(headTicks);\n    } else {\n      tailTicks.push(lastTick + Math.ceil(n / 2) * step);\n    }\n  }\n\n  return headTicks.concat(ticks).concat(tailTicks);\n};\n\n/**\n * 根据start、stop、count进行分割，不要求count完全准确\n * @param start\n * @param stop\n * @param count\n * @returns\n */\nexport const ticks = memoize<ContinuousTicksFunc>(\n  (start: number, stop: number, count: number, options?: { noDecimals?: boolean }) => {\n    let reverse;\n    let ticks;\n    let n;\n    const maxIterations = 5;\n\n    stop = +stop;\n    start = +start;\n    count = +count;\n\n    // add check for start equal stop\n    if (start === stop) {\n      return calculateTicksOfSingleValue(start, count, options?.noDecimals);\n    }\n\n    if (Math.abs(start - stop) <= Number.MIN_VALUE && count > 0) {\n      return calculateTicksOfSingleValue(start, count, options?.noDecimals);\n    }\n    if ((reverse = stop < start)) {\n      n = start;\n      start = stop;\n      stop = n;\n    }\n    const stepRes = tickIncrement(start, stop, count);\n    let step = stepRes.step;\n    // why return empty array when stop === 0 ?\n    // if (stop === 0 || !isFinite(step)) {\n    if (!isFinite(step)) {\n      return [];\n    }\n\n    if (step > 0) {\n      let cur = 1;\n      const { power, gap } = stepRes;\n      const delatStep = gap === 10 ? 2 * 10 ** power : 1 * 10 ** power;\n      while (\n        cur <= maxIterations &&\n        ((ticks = calculateTicksByStep(start, stop, step)), ticks.length > count + 1) &&\n        count > 2\n      ) {\n        step += delatStep;\n\n        cur += 1;\n      }\n\n      if (count > 2 && ticks.length < count - 1) {\n        ticks = appendTicksToCount(ticks, count, step);\n      }\n    } else {\n      if (options?.noDecimals && step < 0) {\n        step = 1;\n      }\n      ticks = calculateTicksByStep(start, stop, step);\n    }\n\n    if (reverse) {\n      ticks.reverse();\n    }\n\n    return ticks;\n  }\n);\n\nconst getNickStep = (step: number) => {\n  const power = Math.floor(Math.log(step) / Math.LN10); // 对数取整\n  const error = step / 10 ** power;\n\n  let gap = niceNumbers[0];\n  if (error >= e10) {\n    gap = niceNumbers[3];\n  } else if (error >= e5) {\n    gap = niceNumbers[2];\n  } else if (error >= e2) {\n    gap = niceNumbers[1];\n  }\n\n  if (power >= 0) {\n    return { step: gap * 10 ** power, gap, power };\n  }\n  return { step: -(10 ** -power) / gap, gap, power };\n};\n\nexport function tickIncrement(start: number, stop: number, count: number) {\n  const step = (stop - start) / Math.max(0, count);\n  return getNickStep(step);\n}\n\n/**\n * 严格根据start、stop、count进行分割，要求start、stop、count完全准确（除了count = 1的情况下stop可能不准确）\n * @param start\n * @param stop\n * @param count\n * @returns\n */\nexport function forceTicks(start: number, stop: number, count: number) {\n  let step;\n\n  stop = +stop;\n  start = +start;\n  count = +count;\n  if (start === stop && count > 0) {\n    return [start];\n  }\n  if (count <= 0 || (step = forceTickIncrement(start, stop, count)) === 0 || !isFinite(step)) {\n    return [];\n  }\n\n  const ticks = new Array(count);\n  for (let i = 0; i < count; i++) {\n    ticks[i] = start + i * step;\n  }\n\n  return ticks;\n}\n\nexport function forceTickIncrement(start: number, stop: number, count: number) {\n  // 用绝对数值做步进距离\n  const step = (stop - start) / Math.max(1, count - 1);\n  return step;\n}\n\n/**\n * 给定step的ticks分割\n * @param start\n * @param stop\n * @param step\n * @returns\n */\nexport function stepTicks(start: number, stop: number, step: number) {\n  let i = -1;\n  let n;\n  let reverse;\n\n  stop = +stop;\n  start = +start;\n  step = +step;\n  if ((reverse = stop < start)) {\n    n = start;\n    start = stop;\n    stop = n;\n  }\n  if (!isFinite(step) || stop - start <= step) {\n    return [start];\n  }\n  const count = Math.floor((stop - start) / step + 1);\n  const ticks = new Array(count);\n  while (++i < count) {\n    ticks[i] = start + i * step;\n  }\n  if (reverse) {\n    ticks.reverse();\n  }\n  return ticks;\n}\n\nexport function niceLinear(d: number[], count: number = 10) {\n  let i0 = 0;\n  let i1 = d.length - 1;\n  let start = d[i0];\n  let stop = d[i1];\n  let prestep;\n  let step;\n  let maxIter = 10;\n\n  if (stop < start) {\n    step = start;\n    start = stop;\n    stop = step;\n    step = i0;\n    i0 = i1;\n    i1 = step;\n  }\n\n  while (maxIter-- > 0) {\n    step = tickIncrement(start, stop, count).step;\n    if (step === prestep) {\n      d[i0] = start;\n      d[i1] = stop;\n      return d;\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    } else {\n      break;\n    }\n    prestep = step;\n  }\n\n  return;\n}\n\nexport function parseNiceOptions(originalDomain: number[], option: NiceOptions) {\n  const hasForceMin = isNumber(option.forceMin);\n  const hasForceMax = isNumber(option.forceMax);\n  let niceType: NiceType = null;\n  const niceMinMax = [];\n  let niceDomain: number[] = null;\n\n  const domainValidator =\n    hasForceMin && hasForceMax\n      ? (x: number) => x >= option.forceMin && x <= option.forceMax\n      : hasForceMin\n      ? (x: number) => x >= option.forceMin\n      : hasForceMax\n      ? (x: number) => x <= option.forceMax\n      : null;\n\n  if (hasForceMin) {\n    niceMinMax[0] = option.forceMin;\n  } else if (\n    isNumber(option.min) &&\n    option.min <= Math.min(originalDomain[0], originalDomain[originalDomain.length - 1])\n  ) {\n    niceMinMax[0] = option.min;\n  }\n\n  if (hasForceMax) {\n    niceMinMax[1] = option.forceMax;\n  } else if (\n    isNumber(option.max) &&\n    option.max >= Math.max(originalDomain[0], originalDomain[originalDomain.length - 1])\n  ) {\n    niceMinMax[1] = option.max;\n  }\n\n  if (isNumber(niceMinMax[0]) && isNumber(niceMinMax[1])) {\n    niceDomain = originalDomain.slice();\n    niceDomain[0] = niceMinMax[0];\n    niceDomain[niceDomain.length - 1] = niceMinMax[1];\n  } else if (!isNumber(niceMinMax[0]) && !isNumber(niceMinMax[1])) {\n    niceType = 'all';\n  } else if (!isNumber(niceMinMax[0])) {\n    niceType = 'min';\n  } else {\n    niceType = 'max';\n  }\n\n  return { niceType, niceDomain, niceMinMax, domainValidator };\n}\n\nexport const fixPrecision = (start: number, stop: number, value: number) => {\n  return Math.abs(stop - start) < 1 ? +value.toFixed(1) : Math.round(+value);\n};\n\nexport const d3TicksForLog = memoize<D3TicksForLogTransformFunc>(\n  (\n    start: number,\n    stop: number,\n    count: number,\n    base: number,\n    transformer: TransformType,\n    untransformer: TransformType,\n    options?: { noDecimals?: boolean }\n  ) => {\n    let u = start;\n    let v = stop;\n    const r = v < u;\n\n    if (r) {\n      [u, v] = [v, u];\n    }\n\n    let i = transformer(u);\n    let j = transformer(v);\n    let k;\n    let t;\n    let z = [];\n\n    if (!(base % 1) && j - i < count) {\n      // this._base is integer\n      (i = Math.floor(i)), (j = Math.ceil(j));\n      if (u > 0) {\n        for (; i <= j; ++i) {\n          for (k = 1; k < base; ++k) {\n            t = i < 0 ? k / untransformer(-i) : k * untransformer(i);\n            if (t < u) {\n              continue;\n            }\n            if (t > v) {\n              break;\n            }\n            z.push(t);\n          }\n        }\n      } else {\n        for (; i <= j; ++i) {\n          for (k = base - 1; k >= 1; --k) {\n            t = i > 0 ? k / untransformer(-i) : k * untransformer(i);\n            if (t < u) {\n              continue;\n            }\n            if (t > v) {\n              break;\n            }\n            z.push(t);\n          }\n        }\n      }\n      if (z.length * 2 < count) {\n        z = ticks(u, v, count);\n      }\n    } else {\n      z = ticks(i, j, Math.min(j - i, count)).map(untransformer);\n    }\n    z = z.filter((t: number) => t !== 0);\n    if (options?.noDecimals) {\n      z = Array.from(new Set(z.map((t: number) => Math.floor(t))));\n    }\n    return r ? z.reverse() : z;\n  }\n);\n\nexport const ticksBaseTransform = memoize<TicksBaseTransformFunc>(\n  (\n    start: number,\n    stop: number,\n    count: number,\n    base: number,\n    transformer: TransformType,\n    untransformer: TransformType\n  ) => {\n    const ticksResult: number[] = [];\n    const ticksMap = {};\n    const startExp = transformer(start);\n    const stopExp = transformer(stop);\n    let ticksExp = [];\n    // get ticks exp\n    if (Number.isInteger(base)) {\n      ticksExp = ticks(startExp, stopExp, count);\n    } else {\n      const stepExp = (stopExp - startExp) / (count - 1);\n      for (let i = 0; i < count; i++) {\n        ticksExp.push(startExp + i * stepExp);\n      }\n    }\n    ticksExp.forEach((tl: number) => {\n      // get pow\n      const power = untransformer(tl);\n      // nice\n      const nicePower = Number.isInteger(base)\n        ? fixPrecision(start, stop, power)\n        : fixPrecision(start, stop, niceNumber(power));\n      // scope\n      const scopePower = fixPrecision(start, stop, restrictNumber(nicePower, [start, stop]));\n      // dedupe\n      if (!ticksMap[scopePower] && !isNaN(scopePower) && ticksExp.length > 1) {\n        ticksMap[scopePower] = 1;\n        ticksResult.push(scopePower);\n      }\n    });\n    return ticksResult;\n  }\n);\n\nexport const forceTicksBaseTransform = memoize<ForceTicksBaseTransformFunc>(\n  (start: number, stop: number, count: number, transformer: TransformType, untransformer: TransformType) => {\n    const startExp = transformer(start);\n    const stopExp = transformer(stop);\n    const ticksExp = forceTicks(startExp, stopExp, count);\n    return ticksExp.map((te: number) => niceNumber(untransformer(te)));\n  }\n);\n\nexport const forceStepTicksBaseTransform = memoize<ForceTicksBaseTransformFunc>(\n  (start: number, stop: number, step: number, transformer: TransformType, untransformer: TransformType) => {\n    const startExp = transformer(start);\n    const stopExp = transformer(stop);\n    const ticksExp = stepTicks(startExp, stopExp, step);\n    return ticksExp.map((te: number) => niceNumber(untransformer(te)));\n  }\n);\n"]}