/**
 * Plugin to change the tree after compiling JSX away.
 *
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns
 *   Transform.
 */
export function recmaBuildJsxTransform(options?: Readonly<Options> | null | undefined): (tree: Program) => undefined;
/**
 * Configuration for internal plugin `recma-build-jsx-transform`.
 */
export type Options = {
    /**
     * Whether to keep the import of the automatic runtime or get it from
     * `arguments[0]` instead (default: `'program'`).
     */
    outputFormat?: "function-body" | "program" | null | undefined;
};
import type { Program } from 'estree-jsx';
//# sourceMappingURL=recma-build-jsx-transform.d.ts.map