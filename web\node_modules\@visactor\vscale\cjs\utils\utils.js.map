{"version": 3, "sources": ["../src/utils/utils.ts"], "names": [], "mappings": ";;;AAAA,6CAAiD;AAGjD,SAAgB,QAAQ,CAAC,CAAM;IAC7B,OAAO,CAAC,CAAC;AACX,CAAC;AAFD,4BAEC;AAEM,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAE,EAAE;IAC9C,OAAO,CAAC,CAAS,EAAE,EAAE;QACnB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACjE,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,WAAW,eAItB;AAEK,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE;IAChC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE;IAClC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC,CAAC;AAFW,QAAA,MAAM,UAEjB;AAEK,MAAM,GAAG,GAAG,CAAC,CAAS,EAAE,EAAE;IAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,GAAG,GAAG,CAAC,CAAS,EAAE,EAAE;IAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC;AAFW,QAAA,GAAG,OAEd;AAEK,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,EAAE;IACvC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AAFW,QAAA,WAAW,eAEtB;AAEK,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,EAAE;IACvC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AAFW,QAAA,WAAW,eAEtB;AAEK,MAAM,KAAK,GAAG,CAAC,CAAS,EAAE,EAAE;IACjC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC;AAFW,QAAA,KAAK,SAEhB;AAEK,MAAM,IAAI,GAAG,CAAC,IAAY,EAAE,EAAE;IACnC,OAAO,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,aAAK,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7F,CAAC,CAAC;AAFW,QAAA,IAAI,QAEf;AAEK,MAAM,IAAI,GAAG,CAAC,IAAY,EAAE,EAAE;IACnC,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC,IAAI,CAAC,GAAG;QACV,CAAC,CAAC,IAAI,KAAK,EAAE;YACb,CAAC,CAAC,IAAI,CAAC,KAAK;YACZ,CAAC,CAAC,IAAI,KAAK,CAAC;gBACZ,CAAC,CAAC,IAAI,CAAC,IAAI;gBACX,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAS,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACnE,CAAC,CAAC;AARW,QAAA,IAAI,QAQf;AAEK,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE;IAClC,OAAO,CAAC,CAAS,EAAE,EAAE;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,MAAM,UAIjB;AAEK,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE;IAClC,OAAO,CAAC,CAAS,EAAE,EAAE;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,MAAM,UAIjB;AAEF,SAAgB,SAAS,CAAC,CAAS,EAAE,CAAS;IAC5C,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,IAAI,CAAC,CAAC;IACP,IAAI,CAAC,EAAE;QACL,OAAO,CAAC,CAAS,EAAE,EAAE;YACnB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC,CAAC;KACH;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3C,OAAO,GAAG,EAAE;QACV,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAbD,8BAaC;AAMD,SAAgB,KAAK,CACnB,MAAwB,EACxB,KAAiB,EACjB,WAAiC;IAEjC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,KAAU,CAAC;IACf,IAAI,KAAU,CAAC;IACf,IAAI,EAAE,GAAG,EAAE,EAAE;QACX,KAAK,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1B,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KAC7B;SAAM;QACL,KAAK,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1B,KAAK,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KAC7B;IACD,OAAO,CAAC,CAAS,EAAE,EAAE;QACnB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC;AArBD,sBAqBC;AAED,SAAgB,SAAS,CAAC,KAAa,EAAE,YAAoB,EAAE,YAAoB;IACjF,IAAI,KAAK,CAAC;IAGV,IAAI,KAAK,KAAK,CAAC,EAAE;QACf,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,CAAC;KAClC;SAAM;QACL,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;KACjD;IACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAVD,8BAUC;AAGD,SAAgB,mBAAmB,CAAC,KAAa,EAAE,SAAiB,EAAE,YAAoB,EAAE,YAAoB;IAC9G,IAAI,YAAY,KAAK,CAAC,EAAE;QACtB,YAAY,GAAG,CAAC,CAAC;KAClB;IACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,SAAS,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;IAC5C,MAAM,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC;IAC/B,OAAO,SAAS,CAAC;AACnB,CAAC;AARD,kDAQC;AAGD,SAAgB,oCAAoC,CAClD,KAAa,EACb,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,KAAc;IAEd,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IAC3D,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IAC/C,IAAI,KAAK,EAAE;QACT,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzB;IACD,IAAI,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;IAC1C,IAAI,KAAK,EAAE;QACT,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KACnC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAjBD,oFAiBC;AAGD,SAAgB,kCAAkC,CAAC,KAAe,EAAE,WAAqB;IACvF,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IACxC,MAAM,EAAE,GAAG,CAAC,CAAC;IACb,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClB,CAAC;AAND,gFAMC;AAED,SAAgB,OAAO,CAAC,MAAgB,EAAE,KAAY,EAAE,WAAiC;IACvF,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAGX,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;QACzB,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;QAClC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;KACjC;IAED,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,OAAO,UAAU,CAAS;QACxB,MAAM,CAAC,GAAG,IAAA,eAAM,EAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC;AACJ,CAAC;AArBD,0BAqBC;AAEM,MAAM,IAAI,GAAG,CAAC,MAAyB,EAAE,OAA2B,EAAE,EAAE;IAC7E,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAEjC,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,IAAI,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;IAC/B,IAAI,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;IAE7B,IAAI,EAAE,GAAG,EAAE,EAAE;QACX,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAChD,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KACrB;IAED,SAAS,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC1C,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEvC,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAjBW,QAAA,IAAI,QAiBf;AAEK,MAAM,UAAU,GAAG,CAAC,KAAa,EAAE,QAAiB,KAAK,EAAE,EAAE;IAClE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/C,MAAM,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAEhD,IAAI,YAAoB,CAAC;IAEzB,IAAI,KAAK,EAAE;QACT,IAAI,QAAQ,GAAG,GAAG,EAAE;YAClB,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM,IAAI,QAAQ,GAAG,CAAC,EAAE;YACvB,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM,IAAI,QAAQ,GAAG,CAAC,EAAE;YACvB,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM;YACL,YAAY,GAAG,EAAE,CAAC;SACnB;KACF;SAAM;QACL,IAAI,QAAQ,IAAI,CAAC,EAAE;YACjB,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM,IAAI,QAAQ,IAAI,CAAC,EAAE;YACxB,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM,IAAI,QAAQ,IAAI,CAAC,EAAE;YACxB,YAAY,GAAG,CAAC,CAAC;SAClB;aAAM;YACL,YAAY,GAAG,EAAE,CAAC;SACnB;KACF;IAED,OAAO,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC/C,CAAC,CAAC;AA7BW,QAAA,UAAU,cA6BrB;AAEK,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,MAAwB,EAAE,EAAE;IACxE,IAAI,GAAG,CAAC;IACR,IAAI,GAAG,CAAC;IACR,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;QACzB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACjB;SAAM;QACL,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACjB;IACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC7C,CAAC,CAAC;AAXW,QAAA,cAAc,kBAWzB", "file": "utils.js", "sourcesContent": ["import { bisect, range } from '@visactor/vutils';\nimport type { FloorCeilType, InterpolateType } from '../interface';\n\nexport function identity(x: any) {\n  return x;\n}\n\nexport const generatePow = (exponent: number) => {\n  return (x: number) => {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n};\n\nexport const sqrt = (x: number) => {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n};\n\nexport const square = (x: number) => {\n  return x < 0 ? -x * x : x * x;\n};\n\nexport const log = (x: number) => {\n  return Math.log(x);\n};\n\nexport const exp = (x: number) => {\n  return Math.exp(x);\n};\n\nexport const logNegative = (x: number) => {\n  return -Math.log(-x);\n};\n\nexport const expNegative = (x: number) => {\n  return -Math.exp(-x);\n};\n\nexport const pow10 = (x: number) => {\n  return isFinite(x) ? Math.pow(10, x) : x < 0 ? 0 : x;\n};\n\nexport const powp = (base: number) => {\n  return base === 10 ? pow10 : base === Math.E ? Math.exp : (x: number) => Math.pow(base, x);\n};\n\nexport const logp = (base: number) => {\n  return base === Math.E\n    ? Math.log\n    : base === 10\n    ? Math.log10\n    : base === 2\n    ? Math.log2\n    : ((base = Math.log(base)), (x: number) => Math.log(x) / base);\n};\n\nexport const symlog = (c: number) => {\n  return (x: number) => {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n};\n\nexport const symexp = (c: number) => {\n  return (x: number) => {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n};\n\nexport function normalize(a: number, b: number): (x: number) => number {\n  a = Number(a);\n  b = Number(b);\n  b -= a;\n  if (b) {\n    return (x: number) => {\n      return (x - a) / b;\n    };\n  }\n  const result = Number.isNaN(b) ? NaN : 0.5;\n  return () => {\n    return result;\n  };\n}\n\n// 基于d3-scale\n// https://github.com/d3/d3-scale/blob/main/src/continuous.js\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nexport function bimap(\n  domain: [number, number],\n  range: [any, any],\n  interpolate: InterpolateType<any>\n): (x: number) => any {\n  const d0 = domain[0];\n  const d1 = domain[1];\n  const r0 = range[0];\n  const r1 = range[1];\n  let d0Fuc: any;\n  let r0Fuc: any;\n  if (d1 < d0) {\n    d0Fuc = normalize(d1, d0);\n    r0Fuc = interpolate(r1, r0);\n  } else {\n    d0Fuc = normalize(d0, d1);\n    r0Fuc = interpolate(r0, r1);\n  }\n  return (x: number) => {\n    return r0Fuc(d0Fuc(x));\n  };\n}\n\nexport function bandSpace(count: number, paddingInner: number, paddingOuter: number): number {\n  let space;\n  // count 等于 1 时需要特殊处理，否则 step 会超出 range 范围\n  // 计算公式: step = paddingOuter * step * 2 + paddingInner * step + bandwidth\n  if (count === 1) {\n    space = count + paddingOuter * 2;\n  } else {\n    space = count - paddingInner + paddingOuter * 2;\n  }\n  return count ? (space > 0 ? space : 1) : 0;\n}\n\n/** 计算 scale 的实际 range 长度 */\nexport function scaleWholeRangeSize(count: number, bandwidth: number, paddingInner: number, paddingOuter: number) {\n  if (paddingInner === 1) {\n    paddingInner = 0; // 保护\n  }\n  const space = bandSpace(count, paddingInner, paddingOuter);\n  const step = bandwidth / (1 - paddingInner);\n  const wholeSize = space * step;\n  return wholeSize;\n}\n\n/** 根据 scale 的实际 range 长度计算 bandwidth */\nexport function calculateBandwidthFromWholeRangeSize(\n  count: number,\n  wholeSize: number,\n  paddingInner: number,\n  paddingOuter: number,\n  round: boolean\n) {\n  const space = bandSpace(count, paddingInner, paddingOuter);\n  let step = wholeSize / Math.max(1, space || 1);\n  if (round) {\n    step = Math.floor(step);\n  }\n  let bandwidth = step * (1 - paddingInner);\n  if (round) {\n    bandwidth = Math.round(bandwidth);\n  }\n  return bandwidth;\n}\n\n/** 根据可见 range 和 rangeFactor 计算整体 range */\nexport function calculateWholeRangeFromRangeFactor(range: number[], rangeFactor: number[]): [number, number] {\n  const k = (range[1] - range[0]) / (rangeFactor[1] - rangeFactor[0]);\n  const b = range[0] - k * rangeFactor[0];\n  const r0 = b;\n  const r1 = k + b;\n  return [r0, r1];\n}\n\nexport function polymap(domain: number[], range: any[], interpolate: InterpolateType<any>): (x: number) => any {\n  const j = Math.min(domain.length, range.length) - 1;\n  const d = new Array(j);\n  const r = new Array(j);\n  let i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function (x: number) {\n    const i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport const nice = (domain: number[] | Date[], options: FloorCeilType<any>) => {\n  const newDomain = domain.slice();\n\n  let startIndex = 0;\n  let endIndex = newDomain.length - 1;\n  let x0 = newDomain[startIndex];\n  let x1 = newDomain[endIndex];\n\n  if (x1 < x0) {\n    [startIndex, endIndex] = [endIndex, startIndex];\n    [x0, x1] = [x1, x0];\n  }\n\n  newDomain[startIndex] = options.floor(x0);\n  newDomain[endIndex] = options.ceil(x1);\n\n  return newDomain;\n};\n\nexport const niceNumber = (value: number, round: boolean = false) => {\n  const exponent = Math.floor(Math.log10(value));\n  const fraction = value / Math.pow(10, exponent);\n\n  let niceFraction: number;\n\n  if (round) {\n    if (fraction < 1.5) {\n      niceFraction = 1;\n    } else if (fraction < 3) {\n      niceFraction = 2;\n    } else if (fraction < 7) {\n      niceFraction = 5;\n    } else {\n      niceFraction = 10;\n    }\n  } else {\n    if (fraction <= 1) {\n      niceFraction = 1;\n    } else if (fraction <= 2) {\n      niceFraction = 2;\n    } else if (fraction <= 5) {\n      niceFraction = 5;\n    } else {\n      niceFraction = 10;\n    }\n  }\n\n  return niceFraction * Math.pow(10, exponent);\n};\n\nexport const restrictNumber = (value: number, domain: [number, number]) => {\n  let min;\n  let max;\n  if (domain[0] < domain[1]) {\n    min = domain[0];\n    max = domain[1];\n  } else {\n    min = domain[1];\n    max = domain[0];\n  }\n  return Math.min(Math.max(value, min), max);\n};\n"]}