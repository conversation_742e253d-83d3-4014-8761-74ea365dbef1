/*!
 * # Semantic UI 2.5.0 - Input
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
           Standard
*******************************/


/*--------------------
        Inputs
---------------------*/

.ui.input {
  position: relative;
  font-weight: normal;
  font-style: normal;
  display: inline-flex;
  color: rgba(0, 0, 0, 0.87);
}
.ui.input > input {
  margin: 0em;
  max-width: 100%;
  flex: 1 0 auto;
  outline: none;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  text-align: left;
  line-height: 1.21428571em;
  font-family: '<PERSON><PERSON>', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  padding: 0.67857143em 1em;
  background: #FFFFFF;
  border: 1px solid rgba(34, 36, 38, 0.15);
  color: rgba(0, 0, 0, 0.87);
  border-radius: 0.28571429rem;
  transition: box-shadow 0.1s ease, border-color 0.1s ease;
  box-shadow: none;
}

/*--------------------
      Placeholder
---------------------*/


/* browsers require these rules separate */
.ui.input > input::-webkit-input-placeholder {
  color: rgba(191, 191, 191, 0.87);
}
.ui.input > input::-moz-placeholder {
  color: rgba(191, 191, 191, 0.87);
}
.ui.input > input:-ms-input-placeholder {
  color: rgba(191, 191, 191, 0.87);
}


/*******************************
            States
*******************************/


/*--------------------
        Disabled
---------------------*/

.ui.disabled.input,
.ui.input:not(.disabled) input[disabled] {
  opacity: 0.45;
}
.ui.disabled.input > input,
.ui.input:not(.disabled) input[disabled] {
  pointer-events: none;
}

/*--------------------
        Active
---------------------*/

.ui.input > input:active,
.ui.input.down input {
  border-color: rgba(0, 0, 0, 0.3);
  background: #FAFAFA;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: none;
}

/*--------------------
       Loading
---------------------*/

.ui.loading.loading.input > i.icon:before {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;
  margin: -0.64285714em 0em 0em -0.64285714em;
  width: 1.28571429em;
  height: 1.28571429em;
  border-radius: 500rem;
  border: 0.2em solid rgba(0, 0, 0, 0.1);
}
.ui.loading.loading.input > i.icon:after {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;
  margin: -0.64285714em 0em 0em -0.64285714em;
  width: 1.28571429em;
  height: 1.28571429em;
  -webkit-animation: button-spin 0.6s linear;
          animation: button-spin 0.6s linear;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  border-radius: 500rem;
  border-color: #767676 transparent transparent;
  border-style: solid;
  border-width: 0.2em;
  box-shadow: 0px 0px 0px 1px transparent;
}

/*--------------------
        Focus
---------------------*/

.ui.input.focus > input,
.ui.input > input:focus {
  border-color: #85B7D9;
  background: #FFFFFF;
  color: rgba(0, 0, 0, 0.8);
  box-shadow: none;
}
.ui.input.focus > input::-webkit-input-placeholder,
.ui.input > input:focus::-webkit-input-placeholder {
  color: rgba(115, 115, 115, 0.87);
}
.ui.input.focus > input::-moz-placeholder,
.ui.input > input:focus::-moz-placeholder {
  color: rgba(115, 115, 115, 0.87);
}
.ui.input.focus > input:-ms-input-placeholder,
.ui.input > input:focus:-ms-input-placeholder {
  color: rgba(115, 115, 115, 0.87);
}

/*--------------------
        Error
---------------------*/

.ui.input.error > input {
  background-color: #FFF6F6;
  border-color: #E0B4B4;
  color: #9F3A38;
  box-shadow: none;
}

/* Error Placeholder */
.ui.input.error > input::-webkit-input-placeholder {
  color: #e7bdbc;
}
.ui.input.error > input::-moz-placeholder {
  color: #e7bdbc;
}
.ui.input.error > input:-ms-input-placeholder {
  color: #e7bdbc !important;
}

/* Focused Error Placeholder */
.ui.input.error > input:focus::-webkit-input-placeholder {
  color: #da9796;
}
.ui.input.error > input:focus::-moz-placeholder {
  color: #da9796;
}
.ui.input.error > input:focus:-ms-input-placeholder {
  color: #da9796 !important;
}


/*******************************
           Variations
*******************************/


/*--------------------
      Transparent
---------------------*/

.ui.transparent.input > input {
  border-color: transparent !important;
  background-color: transparent !important;
  padding: 0em !important;
  box-shadow: none !important;
  border-radius: 0px !important;
}

/* Transparent Icon */
.ui.transparent.icon.input > i.icon {
  width: 1.1em;
}
.ui.transparent.icon.input > input {
  padding-left: 0em !important;
  padding-right: 2em !important;
}
.ui.transparent[class*="left icon"].input > input {
  padding-left: 2em !important;
  padding-right: 0em !important;
}

/* Transparent Inverted */
.ui.transparent.inverted.input {
  color: #FFFFFF;
}
.ui.transparent.inverted.input > input {
  color: inherit;
}
.ui.transparent.inverted.input > input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}
.ui.transparent.inverted.input > input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.5);
}
.ui.transparent.inverted.input > input:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/*--------------------
         Icon
---------------------*/

.ui.icon.input > i.icon {
  cursor: default;
  position: absolute;
  line-height: 1;
  text-align: center;
  top: 0px;
  right: 0px;
  margin: 0em;
  height: 100%;
  width: 2.67142857em;
  opacity: 0.5;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
  transition: opacity 0.3s ease;
}
.ui.icon.input > i.icon:not(.link) {
  pointer-events: none;
}
.ui.icon.input > input {
  padding-right: 2.67142857em !important;
}
.ui.icon.input > i.icon:before,
.ui.icon.input > i.icon:after {
  left: 0;
  position: absolute;
  text-align: center;
  top: 50%;
  width: 100%;
  margin-top: -0.5em;
}
.ui.icon.input > i.link.icon {
  cursor: pointer;
}
.ui.icon.input > i.circular.icon {
  top: 0.35em;
  right: 0.5em;
}

/* Left Icon Input */
.ui[class*="left icon"].input > i.icon {
  right: auto;
  left: 1px;
  border-radius: 0.28571429rem 0em 0em 0.28571429rem;
}
.ui[class*="left icon"].input > i.circular.icon {
  right: auto;
  left: 0.5em;
}
.ui[class*="left icon"].input > input {
  padding-left: 2.67142857em !important;
  padding-right: 1em !important;
}

/* Focus */
.ui.icon.input > input:focus ~ i.icon {
  opacity: 1;
}

/*--------------------
        Labeled
---------------------*/


/* Adjacent Label */
.ui.labeled.input > .label {
  flex: 0 0 auto;
  margin: 0;
  font-size: 1em;
}
.ui.labeled.input > .label:not(.corner) {
  padding-top: 0.78571429em;
  padding-bottom: 0.78571429em;
}

/* Regular Label on Left */
.ui.labeled.input:not([class*="corner labeled"]) .label:first-child {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.ui.labeled.input:not([class*="corner labeled"]) .label:first-child + input {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  border-left-color: transparent;
}
.ui.labeled.input:not([class*="corner labeled"]) .label:first-child + input:focus {
  border-left-color: #85B7D9;
}

/* Regular Label on Right */
.ui[class*="right labeled"].input > input {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
  border-right-color: transparent !important;
}
.ui[class*="right labeled"].input > input + .label {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.ui[class*="right labeled"].input > input:focus {
  border-right-color: #85B7D9 !important;
}

/* Corner Label */
.ui.labeled.input .corner.label {
  top: 1px;
  right: 1px;
  font-size: 0.64285714em;
  border-radius: 0em 0.28571429rem 0em 0em;
}

/* Spacing with corner label */
.ui[class*="corner labeled"]:not([class*="left corner labeled"]).labeled.input > input {
  padding-right: 2.5em !important;
}
.ui[class*="corner labeled"].icon.input:not([class*="left corner labeled"]) > input {
  padding-right: 3.25em !important;
}
.ui[class*="corner labeled"].icon.input:not([class*="left corner labeled"]) > .icon {
  margin-right: 1.25em;
}

/* Left Labeled */
.ui[class*="left corner labeled"].labeled.input > input {
  padding-left: 2.5em !important;
}
.ui[class*="left corner labeled"].icon.input > input {
  padding-left: 3.25em !important;
}
.ui[class*="left corner labeled"].icon.input > .icon {
  margin-left: 1.25em;
}

/* Corner Label Position  */
.ui.input > .ui.corner.label {
  top: 1px;
  right: 1px;
}
.ui.input > .ui.left.corner.label {
  right: auto;
  left: 1px;
}

/*--------------------
        Action
---------------------*/

.ui.action.input > .button,
.ui.action.input > .buttons {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}
.ui.action.input > .button,
.ui.action.input > .buttons > .button {
  padding-top: 0.78571429em;
  padding-bottom: 0.78571429em;
  margin: 0;
}

/* Button on Right */
.ui.action.input:not([class*="left action"]) > input {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
  border-right-color: transparent !important;
}
.ui.action.input:not([class*="left action"]) > .dropdown:not(:first-child),
.ui.action.input:not([class*="left action"]) > .button:not(:first-child),
.ui.action.input:not([class*="left action"]) > .buttons:not(:first-child) > .button {
  border-radius: 0px;
}
.ui.action.input:not([class*="left action"]) > .dropdown:last-child,
.ui.action.input:not([class*="left action"]) > .button:last-child,
.ui.action.input:not([class*="left action"]) > .buttons:last-child > .button {
  border-radius: 0px 0.28571429rem 0.28571429rem 0px;
}

/* Input Focus */
.ui.action.input:not([class*="left action"]) > input:focus {
  border-right-color: #85B7D9 !important;
}

/* Button on Left */
.ui[class*="left action"].input > input {
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-left-color: transparent !important;
}
.ui[class*="left action"].input > .dropdown,
.ui[class*="left action"].input > .button,
.ui[class*="left action"].input > .buttons > .button {
  border-radius: 0px;
}
.ui[class*="left action"].input > .dropdown:first-child,
.ui[class*="left action"].input > .button:first-child,
.ui[class*="left action"].input > .buttons:first-child > .button {
  border-radius: 0.28571429rem 0px 0px 0.28571429rem;
}

/* Input Focus */
.ui[class*="left action"].input > input:focus {
  border-left-color: #85B7D9 !important;
}

/*--------------------
       Inverted
---------------------*/


/* Standard */
.ui.inverted.input > input {
  border: none;
}

/*--------------------
        Fluid
---------------------*/

.ui.fluid.input {
  display: flex;
}
.ui.fluid.input > input {
  width: 0px !important;
}

/*--------------------
        Size
---------------------*/

.ui.mini.input {
  font-size: 0.78571429em;
}
.ui.small.input {
  font-size: 0.92857143em;
}
.ui.input {
  font-size: 1em;
}
.ui.large.input {
  font-size: 1.14285714em;
}
.ui.big.input {
  font-size: 1.28571429em;
}
.ui.huge.input {
  font-size: 1.42857143em;
}
.ui.massive.input {
  font-size: 1.71428571em;
}


/*******************************
         Theme Overrides
*******************************/



/*******************************
         Site Overrides
*******************************/

