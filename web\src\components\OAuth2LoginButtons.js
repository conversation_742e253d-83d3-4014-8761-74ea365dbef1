/*
Copyright (c) 2025 Tethys Plex

This file is part of Veloera.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program. If not, see <https://www.gnu.org/licenses/>.
*/

import React, { useEffect, useState } from 'react';
import { Button, Icon } from '@douyinfe/semi-ui';
import { IconGithubLogo } from '@douyinfe/semi-icons';
import { onOAuth2Clicked, getOAuth2Providers } from './utils';
import OIDCIcon from './OIDCIcon.js';
import LinuxDoIcon from './LinuxDoIcon.js';

// OAuth2提供商图标映射
const providerIcons = {
  github: <IconGithubLogo />,
  oidc: <OIDCIcon />,
  linuxdo: <LinuxDoIcon />,
  google: <Icon svg={<GoogleIcon />} />,
  microsoft: <Icon svg={<MicrosoftIcon />} />,
  discord: <Icon svg={<DiscordIcon />} />,
  gitlab: <Icon svg={<GitLabIcon />} />,
};

// OAuth2提供商颜色映射
const providerColors = {
  github: '#24292e',
  google: '#4285f4',
  microsoft: '#0078d4',
  discord: '#5865f2',
  gitlab: '#fc6d26',
  linuxdo: '#ff6b35',
  oidc: '#007acc',
};

// OAuth2提供商显示名称映射
const providerNames = {
  github: 'GitHub',
  google: 'Google',
  microsoft: 'Microsoft',
  discord: 'Discord',
  gitlab: 'GitLab',
  linuxdo: 'LinuxDO',
  oidc: 'OIDC',
};

// Google图标组件
function GoogleIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 24 24">
      <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
      <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
      <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
      <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
    </svg>
  );
}

// Microsoft图标组件
function MicrosoftIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 24 24">
      <path fill="#f25022" d="M1 1h10v10H1z"/>
      <path fill="#00a4ef" d="M13 1h10v10H13z"/>
      <path fill="#7fba00" d="M1 13h10v10H1z"/>
      <path fill="#ffb900" d="M13 13h10v10H13z"/>
    </svg>
  );
}

// Discord图标组件
function DiscordIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 24 24">
      <path fill="#5865f2" d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
    </svg>
  );
}

// GitLab图标组件
function GitLabIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 24 24">
      <path fill="#fc6d26" d="M12 21.42l3.684-11.333H8.316L12 21.42z"/>
      <path fill="#e24329" d="M12 21.42l-3.684-11.333H1.68L12 21.42z"/>
      <path fill="#fc6d26" d="M1.68 10.087L.153 14.64a.72.72 0 0 0 .263.806L12 21.42 1.68 10.087z"/>
      <path fill="#fca326" d="M1.68 10.087h6.636L6.831 3.31a.36.36 0 0 0-.685 0L1.68 10.087z"/>
      <path fill="#e24329" d="M12 21.42l3.684-11.333h6.636L12 21.42z"/>
      <path fill="#fc6d26" d="M22.32 10.087l1.527 4.553a.72.72 0 0 1-.263.806L12 21.42l10.32-11.333z"/>
      <path fill="#fca326" d="M22.32 10.087h-6.636L17.169 3.31a.36.36 0 0 1 .685 0l4.466 6.777z"/>
    </svg>
  );
}

// OAuth2登录按钮组件
function OAuth2LoginButtons({ style = {} }) {
  const [providers, setProviders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providerList = await getOAuth2Providers();
        setProviders(providerList);
      } catch (error) {
        console.error('加载OAuth2提供商失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProviders();
  }, []);

  const handleOAuth2Click = (provider) => {
    onOAuth2Clicked(provider);
  };

  if (loading) {
    return null;
  }

  if (providers.length === 0) {
    return null;
  }

  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', ...style }}>
      {providers.map((provider) => {
        const icon = providerIcons[provider.name] || <Icon svg={<div>{provider.name.charAt(0).toUpperCase()}</div>} />;
        const color = providerColors[provider.name] || '#666';
        const displayName = providerNames[provider.name] || provider.name;

        return (
          <Button
            key={provider.name}
            type="tertiary"
            icon={icon}
            style={{
              borderColor: color,
              color: color,
            }}
            onClick={() => handleOAuth2Click(provider.name)}
            title={`使用 ${displayName} 登录`}
          >
            {displayName}
          </Button>
        );
      })}
    </div>
  );
}

export default OAuth2LoginButtons;
