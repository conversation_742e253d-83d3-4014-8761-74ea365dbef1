!function(j,q,L,N){"use strict";q=void 0!==q&&q.Math==Math?q:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),j.fn.modal=function(w){var C,e=j(this),F=j(q),M=j(L),x=j("body"),H=e.selector||"",A=(new Date).getTime(),O=[],D=w,T="string"==typeof D,z=[].slice.call(arguments,1),E=q.requestAnimationFrame||q.mozRequestAnimationFrame||q.webkitRequestAnimationFrame||q.msRequestAnimationFrame||function(e){setTimeout(e,0)};return e.each(function(){var i,t,e,o,a,n,r,s,c=j.isPlainObject(w)?j.extend(!0,{},j.fn.modal.settings,w):j.extend({},j.fn.modal.settings),l=c.selector,d=c.className,u=c.namespace,m=c.error,f="."+u,g="module-"+u,h=j(this),v=j(c.context),b=h.find(l.close),p=this,y=h.data(g),k=!1,S={initialize:function(){S.verbose("Initializing dimmer",v),S.create.id(),S.create.dimmer(),S.refreshModals(),S.bind.events(),c.observeChanges&&S.observeChanges(),S.instantiate()},instantiate:function(){S.verbose("Storing instance of modal"),y=S,h.data(g,y)},create:{dimmer:function(){var e={debug:c.debug,variation:!c.centered&&"top aligned",dimmerName:"modals"},n=j.extend(!0,e,c.dimmerSettings);j.fn.dimmer!==N?(S.debug("Creating dimmer"),o=v.dimmer(n),c.detachable?(S.verbose("Modal is detachable, moving content into dimmer"),o.dimmer("add content",h)):S.set.undetached(),a=o.dimmer("get dimmer")):S.error(m.dimmer)},id:function(){r=(Math.random().toString(16)+"000000000").substr(2,8),n="."+r,S.verbose("Creating unique id for element",r)}},destroy:function(){s&&s.disconnect(),S.verbose("Destroying previous modal"),h.removeData(g).off(f),F.off(n),a.off(n),b.off(f),v.dimmer("destroy")},observeChanges:function(){"MutationObserver"in q&&((s=new MutationObserver(function(e){S.debug("DOM tree modified, refreshing"),S.refresh()})).observe(p,{childList:!0,subtree:!0}),S.debug("Setting up mutation observer",s))},refresh:function(){S.remove.scrolling(),S.cacheSizes(),S.can.useFlex()||S.set.modalOffset(),S.set.screenHeight(),S.set.type()},refreshModals:function(){t=h.siblings(l.modal),i=t.add(h)},attachEvents:function(e,n){var i=j(e);n=j.isFunction(S[n])?S[n]:S.toggle,0<i.length?(S.debug("Attaching modal events to element",e,n),i.off(f).on("click"+f,n)):S.error(m.notFound,e)},bind:{events:function(){S.verbose("Attaching events"),h.on("click"+f,l.close,S.event.close).on("click"+f,l.approve,S.event.approve).on("click"+f,l.deny,S.event.deny),F.on("resize"+n,S.event.resize)},scrollLock:function(){o.get(0).addEventListener("touchmove",S.event.preventScroll,{passive:!1})}},unbind:{scrollLock:function(){o.get(0).removeEventListener("touchmove",S.event.preventScroll,{passive:!1})}},get:{id:function(){return(Math.random().toString(16)+"000000000").substr(2,8)}},event:{approve:function(){k||!1===c.onApprove.call(p,j(this))?S.verbose("Approve callback returned false cancelling hide"):(k=!0,S.hide(function(){k=!1}))},preventScroll:function(e){e.preventDefault()},deny:function(){k||!1===c.onDeny.call(p,j(this))?S.verbose("Deny callback returned false cancelling hide"):(k=!0,S.hide(function(){k=!1}))},close:function(){S.hide()},click:function(e){var n,i;c.closable?(n=0<j(e.target).closest(l.modal).length,i=j.contains(L.documentElement,e.target),!n&&i&&S.is.active()&&(S.debug("Dimmer clicked, hiding all modals"),S.remove.clickaway(),c.allowMultiple?S.hide():S.hideAll())):S.verbose("Dimmer clicked but closable setting is disabled")},debounce:function(e,n){clearTimeout(S.timer),S.timer=setTimeout(e,n)},keyboard:function(e){27==e.which&&(c.closable?(S.debug("Escape key pressed hiding modal"),S.hide()):S.debug("Escape key pressed, but closable is set to false"),e.preventDefault())},resize:function(){o.dimmer("is active")&&(S.is.animating()||S.is.active())&&E(S.refresh)}},toggle:function(){S.is.active()||S.is.animating()?S.hide():S.show()},show:function(e){e=j.isFunction(e)?e:function(){},S.refreshModals(),S.set.dimmerSettings(),S.set.dimmerStyles(),S.showModal(e)},hide:function(e){e=j.isFunction(e)?e:function(){},S.refreshModals(),S.hideModal(e)},showModal:function(e){e=j.isFunction(e)?e:function(){},S.is.animating()||!S.is.active()?(S.showDimmer(),S.cacheSizes(),S.can.useFlex()?S.remove.legacy():(S.set.legacy(),S.set.modalOffset(),S.debug("Using non-flex legacy modal positioning.")),S.set.screenHeight(),S.set.type(),S.set.clickaway(),!c.allowMultiple&&S.others.active()?S.hideOthers(S.showModal):(c.allowMultiple&&c.detachable&&h.detach().appendTo(a),c.onShow.call(p),c.transition&&j.fn.transition!==N&&h.transition("is supported")?(S.debug("Showing modal with css animations"),h.transition({debug:c.debug,animation:c.transition+" in",queue:c.queue,duration:c.duration,useFailSafe:!0,onComplete:function(){c.onVisible.apply(p),c.keyboardShortcuts&&S.add.keyboardShortcuts(),S.save.focus(),S.set.active(),c.autofocus&&S.set.autofocus(),e()}})):S.error(m.noTransition))):S.debug("Modal is already visible")},hideModal:function(e,n){e=j.isFunction(e)?e:function(){},S.debug("Hiding modal"),!1!==c.onHide.call(p,j(this))?(S.is.animating()||S.is.active())&&(c.transition&&j.fn.transition!==N&&h.transition("is supported")?(S.remove.active(),h.transition({debug:c.debug,animation:c.transition+" out",queue:c.queue,duration:c.duration,useFailSafe:!0,onStart:function(){S.others.active()||n||S.hideDimmer(),c.keyboardShortcuts&&S.remove.keyboardShortcuts()},onComplete:function(){c.onHidden.call(p),S.remove.dimmerStyles(),S.restore.focus(),e()}})):S.error(m.noTransition)):S.verbose("Hide callback returned false cancelling hide")},showDimmer:function(){o.dimmer("is animating")||!o.dimmer("is active")?(S.debug("Showing dimmer"),o.dimmer("show")):S.debug("Dimmer already visible")},hideDimmer:function(){o.dimmer("is animating")||o.dimmer("is active")?(S.unbind.scrollLock(),o.dimmer("hide",function(){S.remove.clickaway(),S.remove.screenHeight()})):S.debug("Dimmer is not visible cannot hide")},hideAll:function(e){var n=i.filter("."+d.active+", ."+d.animating);e=j.isFunction(e)?e:function(){},0<n.length&&(S.debug("Hiding all visible modals"),S.hideDimmer(),n.modal("hide modal",e))},hideOthers:function(e){var n=t.filter("."+d.active+", ."+d.animating);e=j.isFunction(e)?e:function(){},0<n.length&&(S.debug("Hiding other modals",t),n.modal("hide modal",e,!0))},others:{active:function(){return 0<t.filter("."+d.active).length},animating:function(){return 0<t.filter("."+d.animating).length}},add:{keyboardShortcuts:function(){S.verbose("Adding keyboard shortcuts"),M.on("keyup"+f,S.event.keyboard)}},save:{focus:function(){0<j(L.activeElement).closest(h).length||(e=j(L.activeElement).blur())}},restore:{focus:function(){e&&0<e.length&&e.focus()}},remove:{active:function(){h.removeClass(d.active)},legacy:function(){h.removeClass(d.legacy)},clickaway:function(){a.off("click"+n)},dimmerStyles:function(){a.removeClass(d.inverted),o.removeClass(d.blurring)},bodyStyle:function(){""===x.attr("style")&&(S.verbose("Removing style attribute"),x.removeAttr("style"))},screenHeight:function(){S.debug("Removing page height"),x.css("height","")},keyboardShortcuts:function(){S.verbose("Removing keyboard shortcuts"),M.off("keyup"+f)},scrolling:function(){o.removeClass(d.scrolling),h.removeClass(d.scrolling)}},cacheSizes:function(){h.addClass(d.loading);var e=h.prop("scrollHeight"),n=h.outerWidth(),i=h.outerHeight();S.cache!==N&&0===i||(S.cache={pageHeight:j(L).outerHeight(),width:n,height:i+c.offset,scrollHeight:e+c.offset,contextHeight:"body"==c.context?j(q).height():o.height()},S.cache.topOffset=-S.cache.height/2),h.removeClass(d.loading),S.debug("Caching modal and container sizes",S.cache)},can:{useFlex:function(){return"auto"==c.useFlex?c.detachable&&!S.is.ie():c.useFlex},fit:function(){var e=S.cache.contextHeight,n=S.cache.contextHeight/2,i=S.cache.topOffset,t=S.cache.scrollHeight,o=S.cache.height,a=c.padding;return o<t?n+i+t+a<e:o+2*a<e}},is:{active:function(){return h.hasClass(d.active)},ie:function(){return!q.ActiveXObject&&"ActiveXObject"in q||"ActiveXObject"in q},animating:function(){return h.transition("is supported")?h.transition("is animating"):h.is(":visible")},scrolling:function(){return o.hasClass(d.scrolling)},modernBrowser:function(){return!(q.ActiveXObject||"ActiveXObject"in q)}},set:{autofocus:function(){var e=h.find("[tabindex], :input").filter(":visible"),n=e.filter("[autofocus]"),i=0<n.length?n.first():e.first();0<i.length&&i.focus()},clickaway:function(){a.on("click"+n,S.event.click)},dimmerSettings:function(){var e,n;j.fn.dimmer!==N?(e={debug:c.debug,dimmerName:"modals",closable:"auto",useFlex:S.can.useFlex(),variation:!c.centered&&"top aligned",duration:{show:c.duration,hide:c.duration}},n=j.extend(!0,e,c.dimmerSettings),c.inverted&&(n.variation=n.variation!==N?n.variation+" inverted":"inverted"),v.dimmer("setting",n)):S.error(m.dimmer)},dimmerStyles:function(){c.inverted?a.addClass(d.inverted):a.removeClass(d.inverted),c.blurring?o.addClass(d.blurring):o.removeClass(d.blurring)},modalOffset:function(){var e=S.cache.width,n=S.cache.height;h.css({marginTop:c.centered&&S.can.fit()?-n/2:0,marginLeft:-e/2}),S.verbose("Setting modal offset for legacy mode")},screenHeight:function(){S.can.fit()?x.css("height",""):(S.debug("Modal is taller than page content, resizing page height"),x.css("height",S.cache.height+2*c.padding))},active:function(){h.addClass(d.active)},scrolling:function(){o.addClass(d.scrolling),h.addClass(d.scrolling),S.unbind.scrollLock()},legacy:function(){h.addClass(d.legacy)},type:function(){S.can.fit()?(S.verbose("Modal fits on screen"),S.others.active()||S.others.animating()||(S.remove.scrolling(),S.bind.scrollLock())):(S.verbose("Modal cannot fit on screen setting to scrolling"),S.set.scrolling())},undetached:function(){o.addClass(d.undetached)}},setting:function(e,n){if(S.debug("Changing setting",e,n),j.isPlainObject(e))j.extend(!0,c,e);else{if(n===N)return c[e];j.isPlainObject(c[e])?j.extend(!0,c[e],n):c[e]=n}},internal:function(e,n){if(j.isPlainObject(e))j.extend(!0,S,e);else{if(n===N)return S[e];S[e]=n}},debug:function(){!c.silent&&c.debug&&(c.performance?S.performance.log(arguments):(S.debug=Function.prototype.bind.call(console.info,console,c.name+":"),S.debug.apply(console,arguments)))},verbose:function(){!c.silent&&c.verbose&&c.debug&&(c.performance?S.performance.log(arguments):(S.verbose=Function.prototype.bind.call(console.info,console,c.name+":"),S.verbose.apply(console,arguments)))},error:function(){c.silent||(S.error=Function.prototype.bind.call(console.error,console,c.name+":"),S.error.apply(console,arguments))},performance:{log:function(e){var n,i;c.performance&&(i=(n=(new Date).getTime())-(A||n),A=n,O.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:p,"Execution Time":i})),clearTimeout(S.performance.timer),S.performance.timer=setTimeout(S.performance.display,500)},display:function(){var e=c.name+":",i=0;A=!1,clearTimeout(S.performance.timer),j.each(O,function(e,n){i+=n["Execution Time"]}),e+=" "+i+"ms",H&&(e+=" '"+H+"'"),(console.group!==N||console.table!==N)&&0<O.length&&(console.groupCollapsed(e),console.table?console.table(O):j.each(O,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),O=[]}},invoke:function(t,e,n){var o,a,i,r=y;return e=e||z,n=p||n,"string"==typeof t&&r!==N&&(t=t.split(/[\. ]/),o=t.length-1,j.each(t,function(e,n){var i=e!=o?n+t[e+1].charAt(0).toUpperCase()+t[e+1].slice(1):t;if(j.isPlainObject(r[i])&&e!=o)r=r[i];else{if(r[i]!==N)return a=r[i],!1;if(!j.isPlainObject(r[n])||e==o)return r[n]!==N&&(a=r[n]),!1;r=r[n]}})),j.isFunction(a)?i=a.apply(n,e):a!==N&&(i=a),j.isArray(C)?C.push(i):C!==N?C=[C,i]:i!==N&&(C=i),a}};T?(y===N&&S.initialize(),S.invoke(D)):(y!==N&&y.invoke("destroy"),S.initialize())}),C!==N?C:this},j.fn.modal.settings={name:"Modal",namespace:"modal",useFlex:"auto",offset:0,silent:!1,debug:!1,verbose:!1,performance:!0,observeChanges:!1,allowMultiple:!1,detachable:!0,closable:!0,autofocus:!0,inverted:!1,blurring:!1,centered:!0,dimmerSettings:{closable:!1,useCSS:!0},keyboardShortcuts:!0,context:"body",queue:!1,duration:500,transition:"scale",padding:50,onShow:function(){},onVisible:function(){},onHide:function(){return!0},onHidden:function(){},onApprove:function(){return!0},onDeny:function(){return!0},selector:{close:"> .close",approve:".actions .positive, .actions .approve, .actions .ok",deny:".actions .negative, .actions .deny, .actions .cancel",modal:".ui.modal"},error:{dimmer:"UI Dimmer, a required component is not included in this page",method:"The method you called is not defined.",notFound:"The element you specified could not be found"},className:{active:"active",animating:"animating",blurring:"blurring",inverted:"inverted",legacy:"legacy",loading:"loading",scrolling:"scrolling",undetached:"undetached"}}}(jQuery,window,document);