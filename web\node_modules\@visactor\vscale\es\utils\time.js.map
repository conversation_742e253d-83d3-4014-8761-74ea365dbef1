{"version": 3, "sources": ["../src/utils/time.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,IAAI,EACJ,GAAG,EACH,KAAK,EACL,IAAI,EACJ,kBAAkB,EAClB,kBAAkB,EAClB,eAAe,EACf,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,oBAAoB,EACpB,MAAM,EACN,QAAQ,EACT,MAAM,kBAAkB,CAAC;AAG1B,MAAM,aAAa,GAA+B;IAChD,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;IACrB,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IACzB,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;IAC3B,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;IAC3B,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC;IACrB,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;IACzB,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;IAC3B,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;IAC3B,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;IACjB,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC;IACrB,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC;IACrB,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC;IACvB,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC;IACf,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IACnB,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IACnB,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC;IACnB,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;IACvB,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;IACvB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;CACvB,CAAC;AAEF,MAAM,UAAU,YAAY,CAAC,CAAe;IAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,GAAS,EAAE,GAAS,EAAE,SAAiB,EAAE,KAAe;IACtF,MAAM,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IACzC,MAAM,GAAG,GAAG,MAAM,CAChB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACpC,MAAM,CACP,CAAC;IAEF,IAAI,GAAG,KAAK,aAAa,CAAC,MAAM,EAAE;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,CAAC,IAAU,EAAE,EAAE;YAC1B,IAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAE,IAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC9G,IAAY,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,IAAY,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAElD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QACF,MAAM,MAAM,GAAG,CAAC,IAAU,EAAE,CAAS,EAAE,EAAE;YACtC,IAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAE,IAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YAEhG,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,OAAO;YACL,KAAK;YACL,MAAM;YACN,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;SAClC,CAAC;KACH;IAED,IAAI,GAAG,KAAK,CAAC,EAAE;QACb,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,MAAM,KAAK,GAAG,CAAC,IAAU,EAAE,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QACF,MAAM,MAAM,GAAG,CAAC,IAAU,EAAE,CAAS,EAAE,EAAE;YACvC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YAE/B,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,OAAO;YACL,KAAK;YACL,MAAM;YACN,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;SAClC,CAAC;KACH;IAED,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GACpB,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACrG,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAElE,OAAO,oBAAoB,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;AAC3D,CAAC", "file": "time.js", "sourcesContent": ["import {\n  SECOND,\n  MINUTE,\n  toDate,\n  HOUR,\n  DAY,\n  MONTH,\n  YEAR,\n  fullYearSetterName,\n  fullYearGetterName,\n  monthSetterName,\n  hoursSetterName,\n  generateCeil,\n  getIntervalOptions,\n  generateStepInterval,\n  bisect,\n  tickStep\n} from '@visactor/vutils';\nimport type { DateLikeType } from '../interface';\n\nconst timeIntervals: [string, number, number][] = [\n  ['second', 1, SECOND],\n  ['second', 5, SECOND * 5],\n  ['second', 10, SECOND * 10],\n  ['second', 30, SECOND * 30],\n  ['minute', 1, MINUTE],\n  ['minute', 5, MINUTE * 5],\n  ['minute', 10, MINUTE * 10],\n  ['minute', 30, MINUTE * 30],\n  ['hour', 1, HOUR],\n  ['hour', 3, HOUR * 3],\n  ['hour', 6, HOUR * 6],\n  ['hour', 12, HOUR * 12],\n  ['day', 1, DAY],\n  ['day', 2, DAY * 2],\n  ['day', 7, DAY * 7],\n  ['month', 1, MONTH],\n  ['month', 3, MONTH * 3],\n  ['month', 6, MONTH * 6],\n  ['year', 1, DAY * 365] // 借鉴echarts，保证每个周期累加时不会碰到恰巧不够的问题\n];\n\nexport function toDateNumber(t: DateLikeType) {\n  return +toDate(t);\n}\n\nexport function getTickInterval(min: Date, max: Date, tickCount: number, isUTC?: boolean) {\n  const target = (+max - +min) / tickCount;\n  const idx = bisect(\n    timeIntervals.map(entry => entry[2]),\n    target\n  );\n\n  if (idx === timeIntervals.length) {\n    const step = Math.max(tickStep(+min / YEAR, +max / YEAR, tickCount), 1);\n    const floor = (date: Date) => {\n      (date as any)[fullYearSetterName(isUTC)](Math.floor((date as any)[fullYearGetterName(isUTC)]() / step) * step);\n      (date as any)[monthSetterName(isUTC)](0, 1);\n      (date as any)[hoursSetterName(isUTC)](0, 0, 0, 0);\n\n      return date;\n    };\n    const offset = (date: Date, s: number) => {\n      (date as any)[fullYearSetterName(isUTC)]((date as any)[fullYearGetterName(isUTC)]() + s * step);\n\n      return date;\n    };\n\n    return {\n      floor,\n      offset,\n      ceil: generateCeil(floor, offset)\n    };\n  }\n\n  if (idx === 0) {\n    const step = Math.max(tickStep(+min, +max, tickCount), 1);\n    const floor = (date: Date) => {\n      date.setTime(Math.floor(+date / step) * step);\n      return date;\n    };\n    const offset = (date: Date, s: number) => {\n      date.setTime(+date + s * step);\n\n      return date;\n    };\n\n    return {\n      floor,\n      offset,\n      ceil: generateCeil(floor, offset)\n    };\n  }\n\n  const [timeUnit, step] =\n    timeIntervals[target / timeIntervals[idx - 1][2] < timeIntervals[idx][2] / target ? idx - 1 : idx];\n  const simpleIntervalOptions = getIntervalOptions(timeUnit, isUTC);\n\n  return generateStepInterval(step, simpleIntervalOptions);\n}\n"]}