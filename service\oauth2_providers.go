// Copyright (c) 2025 Tethys Plex
//
// This file is part of Veloera.
//
// This program is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program. If not, see <https://www.gnu.org/licenses/>.

package service

import (
	"fmt"
	"strconv"
	"veloera/common"
	"veloera/setting/system_setting"
)

// GitHubProvider GitHub OAuth2提供商
type GitHubProvider struct {
	BaseOAuth2Provider
}

func NewGitHubProvider() *GitHubProvider {
	return &GitHubProvider{
		BaseOAuth2Provider: BaseOAuth2Provider{
			Name: "github",
			Config: OAuth2Config{
				ClientID:     common.GitHubClientId,
				ClientSecret: common.GitHubClientSecret,
				AuthURL:      "https://github.com/login/oauth/authorize",
				TokenURL:     "https://github.com/login/oauth/access_token",
				UserInfoURL:  "https://api.github.com/user",
				Scopes:       "user:email",
				Enabled:      common.GitHubOAuthEnabled,
			},
		},
	}
}

func (p *GitHubProvider) parseUserInfo(data map[string]interface{}) (*OAuth2User, error) {
	user := &OAuth2User{}
	
	if id, ok := data["id"].(float64); ok {
		user.ID = strconv.Itoa(int(id))
	}
	
	if login, ok := data["login"].(string); ok {
		user.Username = login
	}
	
	if email, ok := data["email"].(string); ok {
		user.Email = email
	}
	
	if name, ok := data["name"].(string); ok {
		user.DisplayName = name
	}
	
	if avatarURL, ok := data["avatar_url"].(string); ok {
		user.AvatarURL = avatarURL
	}
	
	if user.ID == "" || user.Username == "" {
		return nil, fmt.Errorf("GitHub用户信息不完整")
	}
	
	return user, nil
}

// LinuxDOProvider LinuxDO OAuth2提供商
type LinuxDOProvider struct {
	BaseOAuth2Provider
}

func NewLinuxDOProvider() *LinuxDOProvider {
	return &LinuxDOProvider{
		BaseOAuth2Provider: BaseOAuth2Provider{
			Name: "linuxdo",
			Config: OAuth2Config{
				ClientID:     common.LinuxDOClientId,
				ClientSecret: common.LinuxDOClientSecret,
				AuthURL:      "https://connect.linux.do/oauth2/authorize",
				TokenURL:     "https://connect.linux.do/oauth2/token",
				UserInfoURL:  "https://connect.linux.do/api/user",
				Scopes:       "read",
				Enabled:      common.LinuxDOOAuthEnabled,
			},
		},
	}
}

// NodelocProvider Nodeloc OAuth2提供商
type NodelocProvider struct {
	BaseOAuth2Provider
}

func NewNodelocProvider() *NodelocProvider {
	return &NodelocProvider{
		BaseOAuth2Provider: BaseOAuth2Provider{
			Name: "nodeloc",
			Config: OAuth2Config{
				ClientID:     common.NodelocClientId,
				ClientSecret: common.NodelocClientSecret,
				AuthURL:      "https://conn.nodeloc.cc/oauth2/auth",
				TokenURL:     "https://conn.nodeloc.cc/oauth2/token",
				UserInfoURL:  "https://conn.nodeloc.cc/api/user",
				Scopes:       "openid profile",
				Enabled:      common.NodelocOAuthEnabled,
			},
		},
	}
}

func (p *LinuxDOProvider) parseUserInfo(data map[string]interface{}) (*OAuth2User, error) {
	user := &OAuth2User{}
	
	if id, ok := data["id"].(float64); ok {
		user.ID = strconv.Itoa(int(id))
	}
	
	if username, ok := data["username"].(string); ok {
		user.Username = username
	}
	
	if name, ok := data["name"].(string); ok {
		user.DisplayName = name
	}
	
	if user.ID == "" || user.Username == "" {
		return nil, fmt.Errorf("LinuxDO用户信息不完整")
	}
	
	return user, nil
}

// OIDCProvider OIDC OAuth2提供商
type OIDCProvider struct {
	BaseOAuth2Provider
}

func NewOIDCProvider() *OIDCProvider {
	settings := system_setting.GetOIDCSettings()
	return &OIDCProvider{
		BaseOAuth2Provider: BaseOAuth2Provider{
			Name: "oidc",
			Config: OAuth2Config{
				ClientID:     settings.ClientId,
				ClientSecret: settings.ClientSecret,
				AuthURL:      settings.AuthorizationEndpoint,
				TokenURL:     settings.TokenEndpoint,
				UserInfoURL:  settings.UserInfoEndpoint,
				Scopes:       "openid profile email",
				Enabled:      settings.Enabled,
			},
		},
	}
}

func (p *OIDCProvider) parseUserInfo(data map[string]interface{}) (*OAuth2User, error) {
	user := &OAuth2User{}
	
	// OIDC标准字段
	if sub, ok := data["sub"].(string); ok {
		user.ID = sub
	}
	
	if preferredUsername, ok := data["preferred_username"].(string); ok {
		user.Username = preferredUsername
	}
	
	if email, ok := data["email"].(string); ok {
		user.Email = email
	}
	
	if name, ok := data["name"].(string); ok {
		user.DisplayName = name
	}
	
	if picture, ok := data["picture"].(string); ok {
		user.AvatarURL = picture
	}
	
	if user.ID == "" {
		return nil, fmt.Errorf("OIDC用户信息不完整，缺少sub字段")
	}
	
	return user, nil
}

// GenericOAuth2Provider 通用OAuth2提供商
type GenericOAuth2Provider struct {
	BaseOAuth2Provider
	UserInfoParser func(map[string]interface{}) (*OAuth2User, error)
}

func NewGenericOAuth2Provider(name string, config OAuth2Config, parser func(map[string]interface{}) (*OAuth2User, error)) *GenericOAuth2Provider {
	return &GenericOAuth2Provider{
		BaseOAuth2Provider: BaseOAuth2Provider{
			Name:   name,
			Config: config,
		},
		UserInfoParser: parser,
	}
}

func (p *GenericOAuth2Provider) parseUserInfo(data map[string]interface{}) (*OAuth2User, error) {
	if p.UserInfoParser != nil {
		return p.UserInfoParser(data)
	}
	return nil, fmt.Errorf("通用OAuth2提供商 '%s' 未设置用户信息解析器", p.Name)
}

// 常用的OAuth2提供商配置模板
var OAuth2ProviderTemplates = map[string]OAuth2Config{
	"google": {
		AuthURL:     "https://accounts.google.com/o/oauth2/v2/auth",
		TokenURL:    "https://oauth2.googleapis.com/token",
		UserInfoURL: "https://www.googleapis.com/oauth2/v2/userinfo",
		Scopes:      "openid profile email",
	},
	"microsoft": {
		AuthURL:     "https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
		TokenURL:    "https://login.microsoftonline.com/common/oauth2/v2.0/token",
		UserInfoURL: "https://graph.microsoft.com/v1.0/me",
		Scopes:      "openid profile email",
	},
	"discord": {
		AuthURL:     "https://discord.com/api/oauth2/authorize",
		TokenURL:    "https://discord.com/api/oauth2/token",
		UserInfoURL: "https://discord.com/api/users/@me",
		Scopes:      "identify email",
	},
	"gitlab": {
		AuthURL:     "https://gitlab.com/oauth/authorize",
		TokenURL:    "https://gitlab.com/oauth/token",
		UserInfoURL: "https://gitlab.com/api/v4/user",
		Scopes:      "read_user",
	},
}

// 常用的用户信息解析器
var OAuth2UserParsers = map[string]func(map[string]interface{}) (*OAuth2User, error){
	"google": func(data map[string]interface{}) (*OAuth2User, error) {
		user := &OAuth2User{}
		if id, ok := data["id"].(string); ok {
			user.ID = id
		}
		if email, ok := data["email"].(string); ok {
			user.Email = email
			user.Username = email // Google使用email作为用户名
		}
		if name, ok := data["name"].(string); ok {
			user.DisplayName = name
		}
		if picture, ok := data["picture"].(string); ok {
			user.AvatarURL = picture
		}
		if user.ID == "" || user.Email == "" {
			return nil, fmt.Errorf("Google用户信息不完整")
		}
		return user, nil
	},
	"microsoft": func(data map[string]interface{}) (*OAuth2User, error) {
		user := &OAuth2User{}
		if id, ok := data["id"].(string); ok {
			user.ID = id
		}
		if userPrincipalName, ok := data["userPrincipalName"].(string); ok {
			user.Username = userPrincipalName
			user.Email = userPrincipalName
		}
		if displayName, ok := data["displayName"].(string); ok {
			user.DisplayName = displayName
		}
		if user.ID == "" || user.Username == "" {
			return nil, fmt.Errorf("Microsoft用户信息不完整")
		}
		return user, nil
	},
	"discord": func(data map[string]interface{}) (*OAuth2User, error) {
		user := &OAuth2User{}
		if id, ok := data["id"].(string); ok {
			user.ID = id
		}
		if username, ok := data["username"].(string); ok {
			user.Username = username
		}
		if email, ok := data["email"].(string); ok {
			user.Email = email
		}
		if globalName, ok := data["global_name"].(string); ok {
			user.DisplayName = globalName
		}
		if avatar, ok := data["avatar"].(string); ok && avatar != "" {
			user.AvatarURL = fmt.Sprintf("https://cdn.discordapp.com/avatars/%s/%s.png", user.ID, avatar)
		}
		if user.ID == "" || user.Username == "" {
			return nil, fmt.Errorf("Discord用户信息不完整")
		}
		return user, nil
	},
	"gitlab": func(data map[string]interface{}) (*OAuth2User, error) {
		user := &OAuth2User{}
		if id, ok := data["id"].(float64); ok {
			user.ID = strconv.Itoa(int(id))
		}
		if username, ok := data["username"].(string); ok {
			user.Username = username
		}
		if email, ok := data["email"].(string); ok {
			user.Email = email
		}
		if name, ok := data["name"].(string); ok {
			user.DisplayName = name
		}
		if avatarURL, ok := data["avatar_url"].(string); ok {
			user.AvatarURL = avatarURL
		}
		if user.ID == "" || user.Username == "" {
			return nil, fmt.Errorf("GitLab用户信息不完整")
		}
		return user, nil
	},
}
