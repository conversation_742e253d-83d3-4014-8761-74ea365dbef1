{"version": 3, "sources": ["../src/type.ts"], "names": [], "mappings": "AAAA,MAAM,CAAN,IAAY,SA4BX;AA5BD,WAAY,SAAS;IAEnB,kCAAqB,CAAA;IAGrB,8BAAiB,CAAA;IAEjB,wBAAW,CAAA;IACX,wBAAW,CAAA;IACX,0BAAa,CAAA;IACb,8BAAiB,CAAA;IACjB,0BAAa,CAAA;IAQb,kCAAqB,CAAA;IACrB,kCAAqB,CAAA;IACrB,oCAAuB,CAAA;IAGvB,gCAAmB,CAAA;IACnB,4BAAe,CAAA;IACf,0BAAa,CAAA;AAEf,CAAC,EA5BW,SAAS,KAAT,SAAS,QA4BpB;AAED,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;IACnC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,MAAM,UAAU,YAAY,CAAC,IAAY;IACvC,QAAQ,IAAI,EAAE;QACZ,KAAK,SAAS,CAAC,MAAM,CAAC;QACtB,KAAK,SAAS,CAAC,GAAG,CAAC;QACnB,KAAK,SAAS,CAAC,GAAG,CAAC;QACnB,KAAK,SAAS,CAAC,IAAI,CAAC;QACpB,KAAK,SAAS,CAAC,MAAM,CAAC;QACtB,KAAK,SAAS,CAAC,IAAI;YACjB,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,IAAY;IAC3C,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,IAAY;IACrC,QAAQ,IAAI,EAAE;QACZ,KAAK,SAAS,CAAC,OAAO,CAAC;QACvB,KAAK,SAAS,CAAC,KAAK,CAAC;QACrB,KAAK,SAAS,CAAC,IAAI;YACjB,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,IAAY;IACzC,QAAQ,IAAI,EAAE;QACZ,KAAK,SAAS,CAAC,QAAQ,CAAC;QACxB,KAAK,SAAS,CAAC,QAAQ,CAAC;QACxB,KAAK,SAAS,CAAC,SAAS;YACtB,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AACD,MAAM,UAAU,kBAAkB,CAAC,IAAY;IAC7C,QAAQ,IAAI,EAAE;QACZ,KAAK,SAAS,CAAC,MAAM,CAAC;QACtB,KAAK,SAAS,CAAC,GAAG,CAAC;QACnB,KAAK,SAAS,CAAC,GAAG,CAAC;QACnB,KAAK,SAAS,CAAC,IAAI,CAAC;QACpB,KAAK,SAAS,CAAC,MAAM,CAAC;QACtB,KAAK,SAAS,CAAC,IAAI,CAAC;QACpB,KAAK,SAAS,CAAC,IAAI,CAAC;QACpB,KAAK,SAAS,CAAC,KAAK;YAClB,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC", "file": "type.js", "sourcesContent": ["export enum ScaleEnum {\n  // identity scale\n  Identity = 'identity',\n\n  // continuous scales\n  Linear = 'linear',\n  // LinearFactory = 'linearfactory',\n  Log = 'log',\n  Pow = 'pow',\n  Sqrt = 'sqrt',\n  Symlog = 'symlog',\n  Time = 'time',\n  // UTC = 'utc',\n\n  // sequential scales\n  // Sequential = 'sequential',\n  // Diverging = 'diverging',\n\n  // discretizing scales\n  Quantile = 'quantile',\n  Quantize = 'quantize',\n  Threshold = 'threshold',\n\n  // discrete scales\n  Ordinal = 'ordinal',\n  Point = 'point',\n  Band = 'band'\n  // BinOrdinal = 'bin-ordinal',\n}\n\nconst EnableScaleMap = {};\nObject.values(ScaleEnum).forEach(v => {\n  EnableScaleMap[v] = true;\n});\n\nexport function isContinuous(type: string) {\n  switch (type) {\n    case ScaleEnum.Linear:\n    case ScaleEnum.Log:\n    case ScaleEnum.Pow:\n    case ScaleEnum.Sqrt:\n    case ScaleEnum.Symlog:\n    case ScaleEnum.Time:\n      return true;\n    default:\n      return false;\n  }\n}\n\nexport function isValidScaleType(type: string) {\n  return !!EnableScaleMap[type];\n}\n\nexport function isDiscrete(type: string) {\n  switch (type) {\n    case ScaleEnum.Ordinal:\n    case ScaleEnum.Point:\n    case ScaleEnum.Band:\n      return true;\n    default:\n      return false;\n  }\n}\n\nexport function isDiscretizing(type: string) {\n  switch (type) {\n    case ScaleEnum.Quantile:\n    case ScaleEnum.Quantize:\n    case ScaleEnum.Threshold:\n      return true;\n    default:\n      return false;\n  }\n}\nexport function supportRangeFactor(type: string) {\n  switch (type) {\n    case ScaleEnum.Linear:\n    case ScaleEnum.Log:\n    case ScaleEnum.Pow:\n    case ScaleEnum.Sqrt:\n    case ScaleEnum.Symlog:\n    case ScaleEnum.Time:\n    case ScaleEnum.Band:\n    case ScaleEnum.Point:\n      return true;\n    default:\n      return false;\n  }\n}\n"]}