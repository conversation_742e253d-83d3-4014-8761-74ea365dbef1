{"version": 3, "sources": ["../src/quantize-scale.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACrE,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACnC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAG/E,MAAM,OAAO,aAAa;IAA1B;QACW,SAAI,GAA0B,SAAS,CAAC,QAAQ,CAAC;QAEhD,WAAM,GAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvB,YAAO,GAAa,CAAC,GAAG,CAAC,CAAC;QAC1B,OAAE,GAAW,CAAC,CAAC;QACf,OAAE,GAAW,CAAC,CAAC;QACf,MAAC,GAAW,CAAC,CAAC;IAsJ1B,CAAC;IAhJC,OAAO,CAAC,CAAO;QACb,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACrB,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;QACD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,OAAiB;QACvB,IAAI,OAAO,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEjC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/E;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,CAAM;QACV,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC5F,CAAC;IAED,YAAY,CAAC,CAAM;QACjB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC;YACV,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;YACZ,CAAC,CAAC,CAAC,GAAG,CAAC;gBACP,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;oBACrC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAID,MAAM,CAAC,CAAS,EAAE,OAAiB;QACjC,IAAI,CAAC,CAAC,EAAE;YACN,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3B;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAErB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAID,KAAK,CAAC,CAAS,EAAE,OAAiB;QAChC,IAAI,CAAC,CAAC,EAAE;YACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SAC5B;QACD,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK;QACH,OAAO,IAAI,aAAa,EAAE;aACvB,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;aAChC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;aAClB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAkB,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,QAAgB,EAAE;QACtB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAExB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAMD,UAAU,CAAC,QAAgB,EAAE;QAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAMD,SAAS,CAAC,IAAY;QACpB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAExB,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,CAAC,QAAgB,EAAE;QACrB,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;QAEpD,IAAI,UAAU,EAAE;YACd,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SAChC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,OAAO,CAAC,QAAgB,EAAE;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;QAEpD,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACzB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAQD,OAAO,CAAC,QAAgB,EAAE;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;QAEpD,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SACzB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "file": "quantize-scale.js", "sourcesContent": ["import { isValidNumber, bisect, arrayEqual } from '@visactor/vutils';\nimport { ScaleEnum } from './type';\nimport { forceTicks, niceLinear, stepTicks, ticks } from './utils/tick-sample';\nimport type { DiscretizingScaleType, IBaseScale } from './interface';\n\nexport class QuantizeScale implements IBaseScale {\n  readonly type: DiscretizingScaleType = ScaleEnum.Quantile;\n\n  protected _range: any[] = [0, 1];\n  protected _domain: number[] = [0.5];\n  protected x0: number = 0;\n  protected x1: number = 1;\n  protected n: number = 1;\n\n  protected _unknown: any;\n\n  unknown(): any[];\n  unknown(_: any): this;\n  unknown(_?: any): this | any {\n    if (!arguments.length) {\n      return this._unknown;\n    }\n    this._unknown = _;\n    return this;\n  }\n\n  rescale(slience?: boolean) {\n    if (slience) {\n      return this;\n    }\n    let i = -1;\n    this._domain = new Array(this.n);\n\n    while (++i < this.n) {\n      this._domain[i] = ((i + 1) * this.x1 - (i - this.n) * this.x0) / (this.n + 1);\n    }\n    return this;\n  }\n\n  scale(x: any) {\n    return isValidNumber(x) ? this._range[bisect(this._domain, x, 0, this.n)] : this._unknown;\n  }\n\n  invertExtent(y: any) {\n    const i = this._range.indexOf(y);\n    return i < 0\n      ? [NaN, NaN]\n      : i < 1\n      ? [this.x0, this._domain[0]]\n      : i >= this.n\n      ? [this._domain[this.n - 1], this.x1]\n      : [this._domain[i - 1], this._domain[i]];\n  }\n\n  thresholds() {\n    return this._domain.slice();\n  }\n\n  domain(): any[];\n  domain(_: any[], slience?: boolean): this;\n  domain(_?: any[], slience?: boolean): this | any {\n    if (!_) {\n      return [this.x0, this.x1];\n    }\n    const domain = Array.from(_);\n    this.x0 = +domain[0];\n    this.x1 = +domain[1];\n\n    return this.rescale(slience);\n  }\n\n  range(): any[];\n  range(_: any[], slience?: boolean): this;\n  range(_?: any[], slience?: boolean): this | any {\n    if (!_) {\n      return this._range.slice();\n    }\n    const nextRange = Array.from(_);\n    if (arrayEqual(this._range, nextRange)) {\n      return this;\n    }\n    this.n = nextRange.length - 1;\n    this._range = nextRange;\n    return this.rescale(slience);\n  }\n\n  clone(): QuantizeScale {\n    return new QuantizeScale()\n      .domain([this.x0, this.x1], true)\n      .range(this._range)\n      .unknown(this._unknown) as QuantizeScale;\n  }\n\n  ticks(count: number = 10) {\n    const d = this.domain();\n\n    return ticks(d[0], d[d.length - 1], count);\n  }\n\n  /**\n   * 生成tick数组，这个tick数组的长度就是count的长度\n   * @param count\n   */\n  forceTicks(count: number = 10): any[] {\n    const d = this.domain();\n    return forceTicks(d[0], d[d.length - 1], count);\n  }\n\n  /**\n   * 基于给定step的ticks数组生成\n   * @param step\n   */\n  stepTicks(step: number): any[] {\n    const d = this.domain();\n\n    return stepTicks(d[0], d[d.length - 1], step);\n  }\n\n  nice(count: number = 10): this {\n    const niceDomain = niceLinear(this.domain(), count);\n\n    if (niceDomain) {\n      return this.domain(niceDomain);\n    }\n\n    return this;\n  }\n\n  /**\n   * 只对min区间进行nice\n   * 如果保持某一边界的值，就很难有好的nice效果，所以这里实现就是nice之后还原固定的边界值\n   * @param count\n   */\n  niceMin(count: number = 10): this {\n    const maxD = this._domain[this._domain.length - 1];\n    const niceDomain = niceLinear(this.domain(), count);\n\n    if (niceDomain) {\n      niceDomain[niceDomain.length - 1] = maxD;\n      this.domain(niceDomain);\n    }\n\n    return this;\n  }\n\n  /**\n   * 只对max区间进行nice\n   * 如果保持某一边界的值，就很难有好的nice效果，所以这里实现就是nice之后还原固定的边界值\n   * @param count\n   * @returns\n   */\n  niceMax(count: number = 10): this {\n    const minD = this._domain[0];\n    const niceDomain = niceLinear(this.domain(), count);\n\n    if (niceDomain) {\n      niceDomain[0] = minD;\n      this.domain(niceDomain);\n    }\n\n    return this;\n  }\n}\n"]}