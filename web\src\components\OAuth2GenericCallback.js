/*
Copyright (c) 2025 Tethys Plex

This file is part of Veloera.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program. If not, see <https://www.gnu.org/licenses/>.
*/

import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams, useParams } from 'react-router-dom';
import { API, showError, showSuccess, updateAPI } from '../helpers';
import { UserContext } from '../context/User';
import { setUserData } from '../helpers/data.js';
import { Spin, Card, Typography } from '@douyinfe/semi-ui';

const { Title, Text } = Typography;

const OAuth2GenericCallback = () => {
  const [searchParams] = useSearchParams();
  const { provider } = useParams();
  const [userState, userDispatch] = useContext(UserContext);
  const [prompt, setPrompt] = useState('正在处理OAuth2认证...');
  const [processing, setProcessing] = useState(true);
  const navigate = useNavigate();

  const sendCode = async (code, state, count = 0) => {
    try {
      setPrompt(`正在验证${provider}认证信息...`);
      
      const res = await API.get(
        `/api/oauth2/callback/${provider}?code=${code}&state=${state}`,
      );
      
      const { success, message, data } = res.data;
      
      if (success) {
        if (message === 'bind' || message === '绑定成功') {
          showSuccess(`${provider}账户绑定成功！`);
          navigate('/admin/settings');
        } else {
          // 登录成功
          userDispatch({ type: 'login', payload: data });
          localStorage.setItem('user', JSON.stringify(data));
          setUserData(data);
          updateAPI();
          showSuccess(`通过${provider}登录成功！`);
          navigate(searchParams.get('returnTo') || '/app/tokens');
        }
      } else {
        // 认证失败
        showError(message || `${provider}认证失败`);
        
        if (count >= 2) {
          // 重试次数过多，跳转到登录页面
          setPrompt(`认证失败，正在重定向到登录页面...`);
          setTimeout(() => {
            navigate('/login');
          }, 2000);
          return;
        }
        
        // 重试
        count++;
        setPrompt(`认证出现错误，第 ${count} 次重试中...`);
        await new Promise((resolve) => setTimeout(resolve, count * 2000));
        await sendCode(code, state, count);
      }
    } catch (error) {
      console.error('OAuth2认证错误:', error);
      showError(`${provider}认证过程中发生错误: ${error.message}`);
      
      setPrompt('认证失败，正在重定向到登录页面...');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } finally {
      setProcessing(false);
    }
  };

  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error) {
      // OAuth2认证被用户拒绝或出现错误
      const errorMsg = errorDescription || error || '认证被取消';
      showError(`${provider}认证失败: ${errorMsg}`);
      setPrompt('认证失败，正在重定向到登录页面...');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
      return;
    }

    if (!code || !state) {
      showError('缺少必要的认证参数');
      setPrompt('认证参数错误，正在重定向到登录页面...');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
      return;
    }

    if (!provider) {
      showError('缺少OAuth2提供商信息');
      setPrompt('认证参数错误，正在重定向到登录页面...');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
      return;
    }

    // 开始认证流程
    sendCode(code, state);
  }, [searchParams, provider, navigate]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '60vh',
      padding: '20px'
    }}>
      <Card style={{ 
        width: '400px', 
        textAlign: 'center',
        padding: '40px 20px'
      }}>
        <div style={{ marginBottom: '20px' }}>
          <Spin size="large" />
        </div>
        
        <Title heading={4} style={{ marginBottom: '10px' }}>
          OAuth2 认证处理中
        </Title>
        
        <Text type="secondary">
          {prompt}
        </Text>
        
        {provider && (
          <div style={{ marginTop: '20px' }}>
            <Text type="tertiary" size="small">
              提供商: {provider.toUpperCase()}
            </Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default OAuth2GenericCallback;
