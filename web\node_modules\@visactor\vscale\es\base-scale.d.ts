import type { IRangeFactor, ScaleFishEyeOptions } from './interface';
export declare abstract class BaseScale implements IRangeFactor {
    protected _wholeRange: any[];
    protected _rangeFactorStart?: number;
    protected _rangeFactorEnd?: number;
    protected _unknown: any;
    protected _fishEyeOptions?: ScaleFishEyeOptions;
    protected _fishEyeTransform?: (output: number) => number;
    abstract range(): any[];
    abstract domain(): any[];
    abstract invert(d: any): any;
    protected _calculateWholeRange(range: any[]): any[];
    abstract calculateVisibleDomain(range: any[]): any[];
    rangeFactor(): [number, number];
    rangeFactor(_: [number, number], slience?: boolean, clear?: boolean): this;
    rangeFactorStart(): number;
    rangeFactorStart(_: number, slience?: boolean): this;
    rangeFactorEnd(): number;
    rangeFactorEnd(_: number, slience?: boolean): this;
    protected generateFishEyeTransform(): void;
    unknown(): any[];
    unknown(_: any): this;
    get(key: string, defaultValue?: any): any;
}
